<?php
/**
 * Sistema de Integração e Validação do Módulo Financeiro
 * Garante que todos os componentes funcionem de forma sincronizada
 */

// Processa ações via AJAX PRIMEIRO - antes de qualquer include
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Suprimir avisos para AJAX
    error_reporting(E_ERROR | E_PARSE);
    
    // Limpa qualquer output que possa ter sido gerado
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Inicia novo buffer
    ob_start();
    
    // Carrega apenas o necessário para AJAX
    require_once '../includes/init.php';
    require_once '../includes/Database.php';
    require_once '../includes/Auth.php';
    
    // Verifica autenticação para AJAX
    try {
        Auth::requireLogin();
        $userType = Auth::getUserType();
        if (!in_array($userType, ['financeiro', 'admin_master'])) {
            throw new Exception('Acesso negado');
        }
        
        $db = Database::getInstance();
        
        // Include da classe aqui para AJAX
        require_once __DIR__ . '/includes/integrador_class.php';
        
        $acao = $_POST['acao'] ?? '';
        $integrador = new IntegradorFinanceiro($db);
        
        switch ($acao) {
            case 'sincronizar':
                $sucesso = $integrador->sincronizarSistema();
                $response = [
                    'sucesso' => $sucesso,
                    'logs' => $integrador->getLogs()
                ];
                break;
                
            case 'validar':
                $problemas = $integrador->validarIntegridade();
                $response = [
                    'problemas' => $problemas,
                    'logs' => $integrador->getLogs()
                ];
                break;
                
            case 'otimizar':
                $sucesso = $integrador->otimizarPerformance();
                $response = [
                    'sucesso' => $sucesso,
                    'logs' => $integrador->getLogs()
                ];
                break;
                
            case 'relatorio_saude':
                $response = $integrador->gerarRelatorioSaude();
                break;
                
            default:
                $response = ['erro' => 'Ação não reconhecida'];
                break;
        }
        
        // Limpa qualquer output acidental
        ob_clean();
        
        // Envia resposta JSON
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        // Limpa qualquer output acidental
        ob_clean();
        
        // Resposta de erro
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'erro' => $e->getMessage(),
            'sucesso' => false
        ], JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

// Suprimir avisos para AJAX
error_reporting(E_ERROR | E_PARSE);

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Verifica autenticação
Auth::requireLogin();
$userType = Auth::getUserType();
if (!in_array($userType, ['financeiro', 'admin_master'])) {
    $_SESSION['error'] = 'Você não tem permissão para acessar o módulo financeiro.';
    header('Location: ../index.php');
    exit;
}

$db = Database::getInstance();

// Include da classe IntegradorFinanceiro
require_once __DIR__ . '/includes/integrador_class.php';

$pageTitle = 'Integração e Manutenção do Sistema Financeiro';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Faciência ERP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col ml-64">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>
            
            <!-- Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-6xl mx-auto">
                    
                    <!-- Header da página -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Sistema de Integração Financeira</h1>
                        <p class="text-gray-600 mt-2">Sincronização, validação e manutenção do módulo financeiro</p>
                    </div>

                    <!-- Cards de Ação -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <button onclick="executarAcao('sincronizar')" 
                                class="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg shadow transition-colors">
                            <div class="text-center">
                                <i class="fas fa-sync-alt text-3xl mb-3"></i>
                                <h3 class="text-lg font-semibold">Sincronizar</h3>
                                <p class="text-sm opacity-90">Atualiza saldos e dados</p>
                            </div>
                        </button>

                        <button onclick="executarAcao('validar')" 
                                class="bg-green-600 hover:bg-green-700 text-white p-6 rounded-lg shadow transition-colors">
                            <div class="text-center">
                                <i class="fas fa-check-circle text-3xl mb-3"></i>
                                <h3 class="text-lg font-semibold">Validar</h3>
                                <p class="text-sm opacity-90">Verifica integridade</p>
                            </div>
                        </button>

                        <button onclick="executarAcao('otimizar')" 
                                class="bg-purple-600 hover:bg-purple-700 text-white p-6 rounded-lg shadow transition-colors">
                            <div class="text-center">
                                <i class="fas fa-rocket text-3xl mb-3"></i>
                                <h3 class="text-lg font-semibold">Otimizar</h3>
                                <p class="text-sm opacity-90">Melhora performance</p>
                            </div>
                        </button>

                        <button onclick="executarAcao('relatorio_saude')" 
                                class="bg-orange-600 hover:bg-orange-700 text-white p-6 rounded-lg shadow transition-colors">
                            <div class="text-center">
                                <i class="fas fa-heartbeat text-3xl mb-3"></i>
                                <h3 class="text-lg font-semibold">Diagnóstico</h3>
                                <p class="text-sm opacity-90">Relatório de saúde</p>
                            </div>
                        </button>
                    </div>

                    <!-- Área de Resultados -->
                    <div id="resultados" class="bg-white rounded-lg shadow p-6 mb-8" style="display: none;">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Resultados</h3>
                        <div id="conteudo-resultados"></div>
                    </div>

                    <!-- Log de Operações -->
                    <div id="log-operacoes" class="bg-gray-900 text-green-400 rounded-lg p-6" style="display: none;">
                        <h3 class="text-lg font-semibold mb-4">Log de Operações</h3>
                        <div id="conteudo-log" class="font-mono text-sm space-y-1 max-h-96 overflow-y-auto"></div>
                    </div>

                </div>
            </main>
        </div>
    </div>

    <script>
        async function executarAcao(acao) {
            // Mostra loading
            mostrarResultados(`
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3">Executando ${acao}...</span>
                </div>
            `);
            
            try {
                const response = await fetch('integrador.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `acao=${acao}`
                });
                
                // Verifica se a resposta é válida
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Obtém o texto da resposta para debugging
                const responseText = await response.text();
                
                // Tenta fazer parse do JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Resposta não é JSON válido:', responseText);
                    throw new Error(`Resposta inválida do servidor. Primeiro caractere: "${responseText.charAt(0)}"`);
                }
                
                if (acao === 'relatorio_saude') {
                    mostrarRelatorioSaude(data);
                } else {
                    mostrarResultado(data);
                }
                
                if (data.logs) {
                    mostrarLogs(data.logs);
                }
                
            } catch (error) {
                console.error('Erro completo:', error);
                mostrarResultados(`
                    <div class="bg-red-50 border border-red-200 rounded p-4">
                        <h4 class="font-semibold text-red-800">Erro</h4>
                        <p class="text-red-700">${error.message}</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-sm text-red-600">Detalhes técnicos</summary>
                            <pre class="mt-2 text-xs bg-red-100 p-2 rounded overflow-x-auto">${error.stack || 'Sem stack trace disponível'}</pre>
                        </details>
                    </div>
                `);
            }
        }
        
        function mostrarResultados(html) {
            document.getElementById('resultados').style.display = 'block';
            document.getElementById('conteudo-resultados').innerHTML = html;
        }
        
        function mostrarResultado(data) {
            let html = '';
            
            if (data.sucesso) {
                html = `
                    <div class="bg-green-50 border border-green-200 rounded p-4">
                        <h4 class="font-semibold text-green-800">✅ Operação concluída com sucesso!</h4>
                    </div>
                `;
            } else if (data.problemas) {
                html = `
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-4">
                        <h4 class="font-semibold text-yellow-800">⚠ Problemas encontrados:</h4>
                        <ul class="mt-2 text-yellow-700">
                            ${data.problemas.map(p => `<li>• ${p}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else {
                html = `
                    <div class="bg-red-50 border border-red-200 rounded p-4">
                        <h4 class="font-semibold text-red-800">❌ Operação falhou</h4>
                    </div>
                `;
            }
            
            mostrarResultados(html);
        }
        
        function mostrarRelatorioSaude(data) {
            const html = `
                <div class="space-y-4">
                    <div class="bg-${data.status_geral === 'ok' ? 'green' : 'yellow'}-50 border border-${data.status_geral === 'ok' ? 'green' : 'yellow'}-200 rounded p-4">
                        <h4 class="font-semibold text-${data.status_geral === 'ok' ? 'green' : 'yellow'}-800">
                            Status Geral: ${data.status_geral === 'ok' ? '✅ Saudável' : '⚠ Atenção'}
                        </h4>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h5 class="font-semibold mb-2">Componentes:</h5>
                            ${Object.entries(data.componentes).map(([nome, status]) => 
                                `<div class="flex justify-between">
                                    <span>${nome}:</span>
                                    <span class="text-${status === 'ativo' ? 'green' : 'red'}-600">${status}</span>
                                </div>`
                            ).join('')}
                        </div>
                        
                        <div>
                            <h5 class="font-semibold mb-2">Métricas:</h5>
                            ${Object.entries(data.metricas).map(([nome, valor]) => 
                                `<div class="flex justify-between">
                                    <span>${nome}:</span>
                                    <span class="font-mono">${valor}</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                    
                    ${data.recomendacoes && data.recomendacoes.length > 0 ? `
                        <div>
                            <h5 class="font-semibold mb-2">Recomendações:</h5>
                            <ul class="space-y-1 text-yellow-700">
                                ${data.recomendacoes.map(r => `<li>• ${r}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
            
            mostrarResultados(html);
        }
        
        function mostrarLogs(logs) {
            document.getElementById('log-operacoes').style.display = 'block';
            document.getElementById('conteudo-log').innerHTML = logs.map(log => 
                `<div>${log}</div>`
            ).join('');
            
            // Scroll para o final
            const logContainer = document.getElementById('conteudo-log');
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>
</html>
