<?php
/**
 * ================================================================
 * FACIÊNCIA ERP - MÓDULO DE GERENCIAMENTO DE PROFESSORES
 * ================================================================
 *
 * Sistema de gerenciamento de professores
 * Permite cadastrar, editar, listar e gerenciar professores
 *
 * @version 1.0.0
 * <AUTHOR> ERP Development Team
 * @created 2025-06-23
 *
 * Funcionalidades:
 * - Listagem de professores com busca e paginação
 * - Cadastro de novos professores
 * - Edição de professores existentes
 * - Ativação/Inativação de professores
 * ================================================================
 */

// ================================================================
// CONFIGURAÇÕES INICIAIS DO SISTEMA
// ================================================================

// Configurações de depuração (remover em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configurações de performance
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 60);
set_time_limit(60);

// ================================================================
// CARREGAMENTO DE DEPENDÊNCIAS
// ================================================================

// Carrega as configurações do sistema
require_once '../config/config.php';

// Carrega as classes principais do sistema
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once '../includes/Utils.php';

// Carrega as funções e inicializações
require_once '../includes/functions.php';
require_once '../includes/init.php';

// ================================================================
// INICIALIZAÇÃO DA SESSÃO E AUTENTICAÇÃO
// ================================================================

// Inicia a sessão se necessário
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está autenticado (comentado para testes)
// exigirLogin();
// exigirPermissao('professores');

// Para testes, simula um usuário logado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Administrador';
}

// Inicialização de variáveis do sistema
$titulo_pagina = 'Gerenciamento de Professores';
$view = 'listar';
$mensagem = $_SESSION['mensagem'] ?? null;

// Limpa mensagens da sessão após uso
if (isset($_SESSION['mensagem'])) {
    unset($_SESSION['mensagem']);
}

// Conecta ao banco de dados
$db = Database::getInstance();

// ================================================================
// FUNÇÕES AUXILIARES PARA OPERAÇÕES DE BANCO DE DADOS
// ================================================================

/**
 * Executa uma consulta SQL que retorna um único resultado
 */
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

/**
 * Executa uma consulta SQL que retorna múltiplos resultados
 */
function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        $result = $db->fetchAll($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL (fetchAll): ' . $e->getMessage());
        return $default;
    }
}

// ================================================================
// VARIÁVEIS DE CONTROLE
// ================================================================

$action = $_GET['action'] ?? 'listar';
$professores = [];
$professor = [];
$erros = [];

// ================================================================
// CONTROLADOR PRINCIPAL - PROCESSAMENTO DE AÇÕES
// ================================================================

// Obtém a ação solicitada (GET ou POST)
$action = $_GET['action'] ?? ($_POST['action'] ?? 'listar');

// Router principal - processa as diferentes ações do sistema
switch ($action) {

    case 'salvar':
        // Salva professor (novo ou edição)
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Log dos dados recebidos para debug
            error_log("Dados POST recebidos: " . print_r($_POST, true));

            $id = $_POST['id'] ?? null;
            $nome = trim($_POST['nome'] ?? '');
            $formacao = trim($_POST['formacao'] ?? '');
            $area_atuacao = trim($_POST['area_atuacao'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $cpf = trim($_POST['cpf'] ?? '');
            $telefone = trim($_POST['telefone'] ?? '');
            $status = trim($_POST['status'] ?? 'ativo');

            // Log dos dados processados
            error_log("Dados processados - Nome: '$nome', Formação: '$formacao', Área: '$area_atuacao'");

            // Validação
            if (empty($nome)) {
                $erros[] = 'O nome do professor é obrigatório.';
            }
            if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $erros[] = 'Email inválido.';
            }
            if (!empty($cpf) && !validarCPF($cpf)) {
                $erros[] = 'CPF inválido.';
            }

            if (empty($erros)) {
                $dados = [
                    'nome' => $nome,
                    'formacao' => $formacao,
                    'area_atuacao' => $area_atuacao,
                    'email' => $email,
                    'cpf' => $cpf,
                    'telefone' => $telefone,
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                try {
                    if ($id) { // Edição
                        $sql = "UPDATE professores SET nome = ?, formacao = ?, area_atuacao = ?, email = ?, cpf = ?, telefone = ?, status = ?, updated_at = ? WHERE id = ?";
                        $params = [$nome, $formacao, $area_atuacao, $email, $cpf, $telefone, $status, date('Y-m-d H:i:s'), $id];
                        $resultado = $db->query($sql, $params);

                        $_SESSION['mensagem'] = [
                            'tipo' => 'sucesso',
                            'texto' => 'Professor atualizado com sucesso!'
                        ];

                        // Log para debug
                        error_log("Professor ID $id atualizado com sucesso");

                    } else { // Criação
                        $sql = "INSERT INTO professores (nome, formacao, area_atuacao, email, cpf, telefone, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        $params = [$nome, $formacao, $area_atuacao, $email, $cpf, $telefone, $status, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')];
                        $resultado = $db->query($sql, $params);

                        $novo_id = $db->getConnection()->lastInsertId();

                        $_SESSION['mensagem'] = [
                            'tipo' => 'sucesso',
                            'texto' => "Professor '$nome' cadastrado com sucesso! (ID: $novo_id)"
                        ];

                        // Log para debug
                        error_log("Professor '$nome' criado com ID $novo_id. Formação: '$formacao'");
                    }
                    header('Location: professores.php');
                    exit;
                } catch (Exception $e) {
                    // Log detalhado do erro
                    error_log("Erro ao salvar professor: " . $e->getMessage());
                    error_log("SQL: " . ($sql ?? 'N/A'));
                    error_log("Params: " . print_r($params ?? [], true));

                    $_SESSION['mensagem'] = [
                        'tipo' => 'erro',
                        'texto' => 'Erro ao salvar o professor: ' . $e->getMessage()
                    ];

                    // Manter dados no formulário em caso de erro
                    $professor = $_POST;
                    $view = 'formulario';
                    $titulo_pagina = $id ? 'Editar Professor' : 'Novo Professor';
                }
            } else {
                // Se houver erros, preenche os dados do professor para reenviar ao formulário
                $professor = $_POST;
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => implode('<br>', $erros)
                ];
                $view = 'formulario';
                $titulo_pagina = $id ? 'Editar Professor' : 'Novo Professor';
            }
        }
        break;

    case 'novo':
        // Exibe formulário para novo professor
        $view = 'formulario';
        $titulo_pagina = 'Novo Professor';
        $professor = [
            'nome' => '',
            'formacao' => '',
            'area_atuacao' => '',
            'email' => '',
            'cpf' => '',
            'telefone' => '',
            'status' => 'ativo'
        ];
        break;

    case 'editar':
        // Exibe formulário para editar professor
        $id = intval($_GET['id'] ?? 0);
        if ($id > 0) {
            $sql = "SELECT * FROM professores WHERE id = ?";
            $professor = executarConsulta($db, $sql, [$id]);

            if ($professor) {
                $view = 'formulario';
                $titulo_pagina = 'Editar Professor';
            } else {
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => 'Professor não encontrado.'
                ];
                header('Location: professores.php');
                exit;
            }
        } else {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'ID inválido.'
            ];
            header('Location: professores.php');
            exit;
        }
        break;

    case 'ativar':
    case 'inativar':
        // Ativa ou inativa professor
        $id = intval($_GET['id'] ?? 0);
        if ($id > 0) {
            $novo_status = ($action === 'ativar') ? 'ativo' : 'inativo';
            $sql = "UPDATE professores SET status = ?, updated_at = ? WHERE id = ?";
            $params = [$novo_status, date('Y-m-d H:i:s'), $id];

            try {
                $db->query($sql, $params);
                $_SESSION['mensagem'] = [
                    'tipo' => 'sucesso',
                    'texto' => 'Status do professor atualizado com sucesso!'
                ];
            } catch (Exception $e) {
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => 'Erro ao atualizar status: ' . $e->getMessage()
                ];
            }
        } else {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'ID inválido.'
            ];
        }
        header('Location: professores.php');
        exit;

    case 'listar':
    default:
        // Lista professores com busca e paginação
        $termo = trim($_GET['termo'] ?? '');
        $pagina = max(1, intval($_GET['pagina'] ?? 1));
        $por_pagina = 15;
        $offset = ($pagina - 1) * $por_pagina;

        $sql_base = "FROM professores WHERE 1=1";
        $params = [];

        if (!empty($termo)) {
            $sql_base .= " AND (nome LIKE ? OR formacao LIKE ? OR email LIKE ?)";
            $params[] = "%{$termo}%";
            $params[] = "%{$termo}%";
            $params[] = "%{$termo}%";
        }

        $sql_count = "SELECT COUNT(*) as total " . $sql_base;
        $total_result = executarConsulta($db, $sql_count, $params);
        $total_registros = $total_result['total'] ?? 0;
        $total_paginas = ceil($total_registros / $por_pagina);

        $sql = "SELECT * " . $sql_base . " ORDER BY nome ASC LIMIT ? OFFSET ?";
        $params[] = $por_pagina;
        $params[] = $offset;

        $professores = executarConsultaAll($db, $sql, $params);
        $view = 'listar';
        break;
}

// ================================================================
// CARREGAMENTO DA VIEW
// ================================================================
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - Faciência ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden ml-0 lg:ml-64">
            <!-- Header -->
            <?php include '../includes/header.php'; ?>

            <!-- Main -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6 pt-16">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>
                        <?php if ($view === 'listar'): ?>
                        <a href="professores.php?action=novo" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-plus mr-2"></i>
                            Novo Professor
                        </a>
                        <?php endif; ?>
                    </div>

                    <?php if ($mensagem): ?>
                    <div class="bg-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-100 border-l-4 border-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-500 text-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-700 p-4 mb-6">
                        <?php echo $mensagem['texto']; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Conteúdo Principal -->
                    <?php
                    // Carrega a view específica
                    if ($view === 'listar') {
                        include '../views/professores_listar.php';
                    } elseif ($view === 'formulario') {
                        include '../views/professores_formulario.php';
                    }
                    ?>
                </div>
            </main>

            <!-- Footer -->
            <?php include '../includes/footer.php'; ?>
        </div>
    </div>
</body>
</html>
