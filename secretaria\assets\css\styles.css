/* Tailwind CSS */
@import 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

/* Sidebar CSS */
@import 'sidebar.css';

/* Estilos personalizados */
.sidebar {
    width: 250px;
    transition: width 0.3s;
}

.sidebar-collapsed {
    width: 70px;
}

.sidebar-expanded {
    width: 250px;
}

/* Correções para o menu lateral */
#sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 30;
    transition: width 0.3s ease;
    display: flex;
    flex-direction: column;
}

/* Layout principal com sidebar fixo */
body {
    margin: 0;
    padding: 0;
}

.flex.h-screen {
    display: flex;
    height: 100vh;
}

/* Conteúdo principal ajustado para sidebar fixo */
.flex-1.flex.flex-col.overflow-hidden {
    margin-left: 256px; /* 256px = w-64 do sidebar */
    width: calc(100% - 256px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 100vh;
}

/* Garante que o main ocupe o espaço restante */
main.flex-1 {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

/* Ajustes para dispositivos móveis */
@media (max-width: 1024px) {
    .flex-1.flex.flex-col.overflow-hidden {
        margin-left: 0;
        width: 100%;
    }

    #sidebar {
        transform: translateX(-100%);
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 100;
        transition: transform 0.3s ease;
    }

    #sidebar.sidebar-expanded {
        transform: translateX(0);
    }

    /* Overlay para mobile */
    #sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 50;
        transition: opacity 0.3s ease;
    }
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #4b5563;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

.dropdown-item i {
    width: 20px;
    margin-right: 0.5rem;
}

/* Formulários */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #1f2937;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #93c5fd;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* Botões */
.btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    background-color: #3b82f6;
    border: 1px solid #3b82f6;
    border-radius: 0.375rem;
    transition: background-color 0.2s, border-color 0.2s;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: background-color 0.2s, border-color 0.2s;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.btn-danger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    background-color: #ef4444;
    border: 1px solid #ef4444;
    border-radius: 0.375rem;
    transition: background-color 0.2s, border-color 0.2s;
    cursor: pointer;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Cards */
.card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

.badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

/* Task Cards */
.task-card {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1rem;
    border-left: 4px solid #e5e7eb;
}

.task-card.urgent {
    border-left-color: #ef4444;
}

.task-card.important {
    border-left-color: #f59e0b;
}

.task-card.normal {
    border-left-color: #3b82f6;
}

.task-card.completed {
    border-left-color: #10b981;
}

/* Nav Item */
.nav-item {
    transition: background-color 0.2s;
    border-radius: 0.375rem;
    margin-bottom: 2px;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

/* Menu Categories */
.sidebar h3.text-xs {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    letter-spacing: 0.05em;
}

/* Correções para formulários */
.form-group {
    margin-bottom: 1rem;
}

select, input[type="date"], input[type="text"] {
    display: block;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-sizing: border-box;
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}
