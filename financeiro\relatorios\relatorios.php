<?php
/**
 * Relatórios - Módulo Financeiro
 * Sistema Faciência ERP - Versão FINAL CORRIGIDA
 */

require_once "includes/config.php";
$pageTitle = "Relatórios";
$pdo = getConnection();

// Variáveis de controle
$action = $_GET["action"] ?? "listar";
$id = $_GET["id"] ?? null;
$mensagem = "";
$erro = "";

// Processamento de formulários
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        switch ($_POST["action"]) {
            case "salvar":
                // Lógica de salvamento aqui
                $mensagem = "Registro salvo com sucesso!";
                break;
                
            case "excluir":
                // Lógica de exclusão aqui
                $mensagem = "Registro excluído com sucesso!";
                break;
        }
    } catch (Exception $e) {
        $erro = "Erro: " . $e->getMessage();
    }
}

// Buscar dados
$registros = fetchData($pdo, "SELECT * FROM relatorios ORDER BY id DESC");

include "includes/header_padronizado_novo.php";
?>

<!-- CSS Específico para Relatórios -->
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .reports-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        overflow: hidden;
    }
    
    .reports-header {
        background: linear-gradient(135deg, #059669, #0d9488);
        color: white;
        padding: 24px;
        text-align: center;
    }
    
    .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary {
        background: #3b82f6;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2563eb;
        color: white;
    }
    
    .table-responsive {
        overflow-x: auto;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }
    
    .table th {
        background: #f8fafc;
        padding: 12px;
        font-weight: 600;
        color: #374151;
        border-bottom: 2px solid #e5e7eb;
        text-align: left;
    }
    
    .table td {
        padding: 12px;
        border-bottom: 1px solid #f3f4f6;
        color: #6b7280;
    }
    
    .table tr:hover {
        background: #f8fafc;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-active {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-inactive {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #9ca3af;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .page-container {
            padding: 16px;
        }
        
        .action-buttons {
            justify-content: center;
        }
        
        .reports-header {
            padding: 16px;
        }
        
        .table th,
        .table td {
            padding: 8px;
            font-size: 14px;
        }
    }
</style>

<div class="page-container">

<!-- Header da página -->
<div class="reports-card">
    <div class="reports-header">
        <h1><i class="fas fa-chart-bar me-3"></i>Relatórios</h1>
        <p class="mt-2 opacity-90">Gestão e controle • <?php echo date("d/m/Y H:i"); ?></p>
    </div>
</div>

<!-- Mensagens -->
<?php if ($mensagem): ?>
    <div class="reports-card">
        <div class="alert alert-success d-flex align-items-center">
            <i class="fas fa-check-circle me-2"></i>
            <span><?php echo $mensagem; ?></span>
        </div>
    </div>
<?php endif; ?>

<?php if ($erro): ?>
    <div class="reports-card">
        <div class="alert alert-danger d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span><?php echo $erro; ?></span>
        </div>
    </div>
<?php endif; ?>

<!-- Ações principais -->
<div class="reports-card">
    <div class="p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Registros</h5>
            <button type="button" class="btn-action btn-primary" data-bs-toggle="modal" data-bs-target="#modalForm">
                <i class="fas fa-plus"></i>Novo Registro
            </button>
        </div>
        
        <?php if (empty($registros)): ?>
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <p class="mb-3">Nenhum registro encontrado</p>
                <button type="button" class="btn-action btn-primary" data-bs-toggle="modal" data-bs-target="#modalForm">
                    <i class="fas fa-plus"></i>Adicionar Primeiro Registro
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome/Descrição</th>
                            <th>Status</th>
                            <th>Data</th>
                            <th width="120">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($registros as $registro): ?>
                        <tr>
                            <td><?php echo $registro["id"]; ?></td>
                            <td><?php echo htmlspecialchars($registro["nome"] ?? $registro["descricao"] ?? "N/A"); ?></td>
                            <td>
                                <span class="status-badge status-active">Ativo</span>
                            </td>
                            <td><?php echo date("d/m/Y", strtotime($registro["created_at"] ?? "now")); ?></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                        onclick="editarRegistro(<?php echo $registro['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="excluirRegistro(<?php echo $registro['id']; ?>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
                                        <span class="badge bg-success">Ativo</span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap"><?php echo formatDate($registro["created_at"] ?? date("Y-m-d")); ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editarRegistro(<?php echo $registro[\"id\"]; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="excluirRegistro(<?php echo $registro[\"id\"]; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Formulário -->
<div class="modal fade" id="modalForm" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Novo Registro
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="salvar">
                    <input type="hidden" name="id" id="registro_id">
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">Nome/Descrição *</label>
                            <input type="text" name="nome" class="form-control" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tipo de Relatório</label>
                            <select name="tipo_relatorio" class="form-select">
                                <option value="balancete">Balancete</option>
                                <option value="dre">DRE</option>
                                <option value="fluxo_caixa">Fluxo de Caixa</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Período</label>
                            <select name="periodo" class="form-select">
                                <option value="mensal">Mensal</option>
                                <option value="trimestral">Trimestral</option>
                                <option value="anual">Anual</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

</div>

<?php include "includes/footer_padronizado.php"; ?>

<!-- Scripts Específicos -->
<script>
function editarRegistro(id) {
    // Implementar edição
    console.log("Editar registro:", id);
    // Aqui você pode carregar os dados via AJAX e preencher o modal
}

function excluirRegistro(id) {
    if (confirm("Tem certeza que deseja excluir este registro?")) {
        // Implementar exclusão via POST ou AJAX
        window.location.href = "?action=excluir&id=" + id;
    }
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    console.log('Relatórios carregados');
});
</script>