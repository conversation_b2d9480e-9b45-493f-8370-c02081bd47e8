/**
 * CSS específico para o módulo Turma-Disciplinas
 * Correções de layout e estilização da paginação
 */

/* ================================================================
   CORREÇÕES DE LAYOUT
   ================================================================ */

/* Garante que o conteúdo não fique atrás do menu */
.main-content {
    margin-left: 256px !important;
    width: calc(100% - 256px) !important;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Layout responsivo para mobile */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    /* Ajusta sidebar em mobile */
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
}

/* Garante que o header não sobreponha o conteúdo */
.main-content main {
    padding-top: 1rem;
    min-height: calc(100vh - 4rem);
}

/* ================================================================
   ESTILIZAÇÃO DA PAGINAÇÃO
   ================================================================ */

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0;
    margin: 1rem 0;
}

.pagination a, 
.pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
}

/* Primeira página */
.pagination a:first-child,
.pagination span:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* Última página */
.pagination a:last-child,
.pagination span:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Remove bordas duplicadas */
.pagination a + a,
.pagination a + span,
.pagination span + a,
.pagination span + span {
    border-left: none;
}

/* Estados dos botões */
.pagination a {
    color: #374151;
    background-color: #ffffff;
}

.pagination a:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.pagination a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

/* Página atual */
.pagination .current,
.pagination span.current {
    background-color: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
}

/* Botões desabilitados */
.pagination .disabled,
.pagination span.disabled {
    color: #9ca3af;
    background-color: #f9fafb;
    cursor: not-allowed;
}

/* Reticências */
.pagination .dots {
    color: #6b7280;
    background-color: #ffffff;
    cursor: default;
}

/* ================================================================
   MELHORIAS NA TABELA
   ================================================================ */

/* Zebra striping para melhor legibilidade */
.table-striped tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.table-striped tbody tr:hover {
    background-color: #f3f4f6;
}

/* Responsividade da tabela */
@media (max-width: 1024px) {
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table-responsive table {
        min-width: 800px;
    }
}

/* ================================================================
   CARDS DE ESTATÍSTICAS
   ================================================================ */

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0.5rem;
    padding: 1.5rem;
    color: white;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stats-card .number {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1;
    margin: 0.5rem 0;
}

.stats-card .label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* ================================================================
   FILTROS E BUSCA
   ================================================================ */

.filter-container {
    background-color: #f8fafc;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
}

.filter-container .form-group {
    margin-bottom: 1rem;
}

.filter-container .form-group:last-child {
    margin-bottom: 0;
}

.filter-container label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.filter-container input,
.filter-container select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.filter-container input:focus,
.filter-container select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ================================================================
   BOTÕES DE AÇÃO
   ================================================================ */

.btn-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

/* ================================================================
   ALERTAS E NOTIFICAÇÕES
   ================================================================ */

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-info {
    background-color: #eff6ff;
    border-left-color: #3b82f6;
    color: #1e40af;
}

.alert-warning {
    background-color: #fffbeb;
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-success {
    background-color: #ecfdf5;
    border-left-color: #10b981;
    color: #065f46;
}

.alert-error {
    background-color: #fef2f2;
    border-left-color: #ef4444;
    color: #991b1b;
}

/* ================================================================
   UTILITÁRIOS
   ================================================================ */

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-hover {
    transition: box-shadow 0.2s ease;
}

.shadow-hover:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
