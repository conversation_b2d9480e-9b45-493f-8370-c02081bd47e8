/**
 * CSS Específico para o Módulo Secretaria
 * Design moderno com tema azul
 */

/* Sidebar Styles */
.sidebar-item {
    transition: all 0.3s ease;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
}

.sidebar-item:hover {
    background-color: rgba(29, 78, 216, 0.7) !important;
    transform: translateX(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar-item.active {
    background-color: rgba(37, 99, 235, 1) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #60a5fa;
}

.sidebar-item i {
    width: 1.5rem;
    text-align: center;
    margin-right: 0.75rem;
}

/* Gradiente do sidebar */
.sidebar-gradient {
    background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
}

/* Animações suaves */
.sidebar-item,
.sidebar-item i,
.sidebar-label {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Efeito hover nos ícones */
.sidebar-item:hover i {
    transform: scale(1.1);
    color: #dbeafe;
}

/* Estilo para seções de título */
.sidebar-section-title {
    color: #93c5fd;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
}

/* Estilo para o logo */
.sidebar-logo {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Efeitos de foco para acessibilidade */
.sidebar-item:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
}

/* Estilo para badges/notificações */
.sidebar-badge {
    background-color: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    margin-left: auto;
}

/* Animação de entrada */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.sidebar {
    animation: slideInLeft 0.3s ease-out;
}

/* Estilo para separadores */
.sidebar-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #374151 50%, transparent 100%);
    margin: 1rem 0;
}

/* Efeito de brilho no hover */
.sidebar-item:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    border-radius: 0.375rem;
    pointer-events: none;
}

/* Posicionamento relativo para o efeito de brilho */
.sidebar-item {
    position: relative;
    overflow: hidden;
}

/* Estilo para tooltips */
.sidebar-tooltip {
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-left: 0.5rem;
}

.sidebar-item:hover .sidebar-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Sombra personalizada para o sidebar */
.sidebar-shadow {
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Estilo para o conteúdo principal quando sidebar está ativo */
.main-content {
    margin-left: 16rem;
    transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
}

/* Estilo para cards do dashboard */
.dashboard-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Cores específicas para diferentes tipos de cards */
.card-blue {
    border-left: 4px solid #3b82f6;
}

.card-green {
    border-left: 4px solid #10b981;
}

.card-yellow {
    border-left: 4px solid #f59e0b;
}

.card-red {
    border-left: 4px solid #ef4444;
}

/* Estilo para botões do módulo secretaria */
.btn-secretaria {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secretaria:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Estilo para tabelas */
.table-secretaria {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-secretaria th {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-weight: 600;
    padding: 1rem;
    text-align: left;
}

.table-secretaria td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.table-secretaria tr:hover {
    background-color: #f8fafc;
}

/* Estilo para formulários */
.form-secretaria .form-group {
    margin-bottom: 1rem;
}

.form-secretaria label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-secretaria input,
.form-secretaria select,
.form-secretaria textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.form-secretaria input:focus,
.form-secretaria select:focus,
.form-secretaria textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
