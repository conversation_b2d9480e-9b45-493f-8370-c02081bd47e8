/* Estilos personalizados para o dashboard da secretaria */

/* Cards */
.card {
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Badges */
.badge {
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-primary {
    background-color: #3B82F6;
    color: white;
}

.badge-warning {
    background-color: #F59E0B;
    color: white;
}

.badge-danger {
    background-color: #EF4444;
    color: white;
}

.badge-success {
    background-color: #10B981;
    color: white;
}

/* Task Cards */
.task-card {
    border-left: 4px solid;
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.task-card.urgent {
    border-left-color: #EF4444;
}

.task-card.important {
    border-left-color: #F59E0B;
}

.task-card.normal {
    border-left-color: #3B82F6;
}

.task-card.completed {
    border-left-color: #10B981;
    opacity: 0.7;
}

/* Buttons */
.btn-primary {
    background-color: #3B82F6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563EB;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.1s;
}

.fade-in-delay-2 {
    animation-delay: 0.2s;
}

.fade-in-delay-3 {
    animation-delay: 0.3s;
}

.fade-in-delay-4 {
    animation-delay: 0.4s;
}
