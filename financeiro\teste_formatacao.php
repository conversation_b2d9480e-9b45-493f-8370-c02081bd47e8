<?php
/**
 * Teste da formatação de valores monetários
 */

// Simular diferentes entradas do usuário
$testes = [
    '1500,50',
    '1.500,50', 
    '1500.50',
    'R$ 1.500,50',
    '15000',
    '1,5',
    '0,50'
];

echo "<h2>🧪 Teste de Formatação de Valores</h2>\n";

foreach ($testes as $valor) {
    echo "<h3>Teste: '{$valor}'</h3>\n";
    
    // Aplicar a nova lógica de limpeza
    $valorLimpo = preg_replace('/[^0-9,.-]/', '', $valor);
    echo "1. Após remover caracteres: '{$valorLimpo}'\n";
    
    // Nova lógica de conversão
    if (strpos($valorLimpo, ',') !== false) {
        // Formato brasileiro: 1.500,50 ou 1500,50
        $valorConvertido = str_replace('.', '', $valorLimpo); // Remove pontos (separadores de milhares)
        $valorConvertido = str_replace(',', '.', $valorConvertido); // Converte vírgula para ponto decimal
        echo "2. Formato brasileiro detectado: '{$valorConvertido}'\n";
    } else {
        // Formato americano ou número inteiro
        $valorConvertido = $valorLimpo;
        echo "2. Formato americano/inteiro: '{$valorConvertido}'\n";
    }
    
    $valorFinal = floatval($valorConvertido);
    echo "3. Valor final: {$valorFinal}\n";
    echo "4. Formatado para exibição: R$ " . number_format($valorFinal, 2, ',', '.') . "\n";
    echo "---\n";
}

echo "<h3>✅ Todos os testes de formatação concluídos!</h3>\n";
?>
