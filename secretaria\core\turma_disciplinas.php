<?php
/**
 * ================================================================
 * FACIÊNCIA ERP - MÓDULO DE GERENCIAMENTO TURMA-DISCIPLINAS
 * ================================================================
 * 
 * Sistema de gerenciamento do relacionamento entre turmas e disciplinas
 * Permite criar, editar, visualizar e excluir vinculações específicas
 * 
 * @version 1.0.0
 * <AUTHOR> ERP Development Team
 * @created 2025-06-18
 * 
 * Funcionalidades:
 * - Listagem de turmas e suas disciplinas
 * - Adição/remoção de disciplinas em turmas
 * - Definição de professores específicos por turma
 * - Configuração de carga horária específica
 * - Ordenação das disciplinas na grade
 * ================================================================
 */

// Configurações iniciais
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Carregamento de dependências
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/Auth.php';
require_once 'includes/Utils.php';
require_once 'includes/functions.php';
require_once 'includes/init.php';

// Inicialização da sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verifica autenticação
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Conecta ao banco
$db = Database::getInstance();

// Inicialização de variáveis
$titulo_pagina = 'Gerenciar Turma-Disciplinas';
$view = 'listar';
$mensagem = $_SESSION['mensagem'] ?? null;

if (isset($_SESSION['mensagem'])) {
    unset($_SESSION['mensagem']);
}

// Funções auxiliares
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        $result = $db->fetchAll($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

// Processamento de ações
$action = $_GET['action'] ?? $_POST['action'] ?? 'listar';

switch ($action) {
    case 'listar':
        $view = 'listar';
        break;
        
    case 'gerenciar':
        $turma_id = $_GET['turma_id'] ?? null;
        if (!$turma_id) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'ID da turma não informado.'
            ];
            header('Location: turma_disciplinas.php');
            exit;
        }
        $view = 'gerenciar';
        break;
        
    case 'adicionar_disciplina':
        $turma_id = $_POST['turma_id'] ?? null;
        $disciplina_id = $_POST['disciplina_id'] ?? null;
        $professor_id = $_POST['professor_id'] ?? null;
        $carga_horaria = $_POST['carga_horaria_turma'] ?? null;
        $periodo = $_POST['periodo_turma'] ?? null;
        
        if (!$turma_id || !$disciplina_id) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'Turma e disciplina são obrigatórios.'
            ];
        } else {
            try {
                // Verifica se já existe
                $sql = "SELECT id FROM turma_disciplinas WHERE turma_id = ? AND disciplina_id = ?";
                $existe = executarConsulta($db, $sql, [$turma_id, $disciplina_id]);
                
                if ($existe) {
                    $_SESSION['mensagem'] = [
                        'tipo' => 'erro',
                        'texto' => 'Esta disciplina já está vinculada à turma.'
                    ];
                } else {
                    // Busca próxima ordem
                    $sql = "SELECT COALESCE(MAX(ordem), 0) + 1 as proxima_ordem FROM turma_disciplinas WHERE turma_id = ?";
                    $ordem_result = executarConsulta($db, $sql, [$turma_id]);
                    $ordem = $ordem_result['proxima_ordem'] ?? 1;
                    
                    // Insere vinculação
                    $sql = "INSERT INTO turma_disciplinas (turma_id, disciplina_id, professor_id, carga_horaria_turma, periodo_turma, ordem, status, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, 'ativo', NOW())";
                    
                    $params = [
                        $turma_id,
                        $disciplina_id,
                        $professor_id ?: null,
                        $carga_horaria ?: null,
                        $periodo ?: null,
                        $ordem
                    ];
                    
                    $db->query($sql, $params);
                    
                    $_SESSION['mensagem'] = [
                        'tipo' => 'sucesso',
                        'texto' => 'Disciplina adicionada à turma com sucesso!'
                    ];
                }
            } catch (Exception $e) {
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => 'Erro ao adicionar disciplina: ' . $e->getMessage()
                ];
            }
        }
        
        header('Location: turma_disciplinas.php?action=gerenciar&turma_id=' . $turma_id);
        exit;
        
    case 'remover_disciplina':
        $id = $_GET['id'] ?? null;
        $turma_id = $_GET['turma_id'] ?? null;
        
        if (!$id) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'ID da vinculação não informado.'
            ];
        } else {
            try {
                $sql = "DELETE FROM turma_disciplinas WHERE id = ?";
                $db->query($sql, [$id]);
                
                $_SESSION['mensagem'] = [
                    'tipo' => 'sucesso',
                    'texto' => 'Disciplina removida da turma com sucesso!'
                ];
            } catch (Exception $e) {
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => 'Erro ao remover disciplina: ' . $e->getMessage()
                ];
            }
        }
        
        header('Location: turma_disciplinas.php?action=gerenciar&turma_id=' . $turma_id);
        exit;
        
    case 'atualizar_ordem':
        $ids = $_POST['ids'] ?? [];
        $turma_id = $_POST['turma_id'] ?? null;
        
        if (!empty($ids) && $turma_id) {
            try {
                foreach ($ids as $ordem => $id) {
                    $sql = "UPDATE turma_disciplinas SET ordem = ?, updated_at = NOW() WHERE id = ? AND turma_id = ?";
                    $db->query($sql, [$ordem + 1, $id, $turma_id]);
                }
                
                $_SESSION['mensagem'] = [
                    'tipo' => 'sucesso',
                    'texto' => 'Ordem das disciplinas atualizada com sucesso!'
                ];
            } catch (Exception $e) {
                $_SESSION['mensagem'] = [
                    'tipo' => 'erro',
                    'texto' => 'Erro ao atualizar ordem: ' . $e->getMessage()
                ];
            }
        }
        
        header('Location: turma_disciplinas.php?action=gerenciar&turma_id=' . $turma_id);
        exit;
        
    default:
        $view = 'listar';
        break;
}

// Carregamento da view
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - Faciência ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/turma-disciplinas.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        /* Estilo para paginação */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .pagination a, .pagination span {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            text-decoration: none;
            transition: all 0.2s;
        }

        .pagination a:hover {
            background-color: #f3f4f6;
        }

        .pagination .current {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pagination .disabled {
            color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>

            <!-- Main -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>
                    </div>

                    <?php if ($mensagem): ?>
                    <div class="bg-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-100 border-l-4 border-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-500 text-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-700 p-4 mb-6">
                        <?php echo $mensagem['texto']; ?>
                    </div>
                    <?php endif; ?>

                    <?php
                    // Inclui a view correspondente
                    $base_path = __DIR__ . '/views/turma_disciplinas/';

                    switch ($view) {
                        case 'listar':
                            $view_file = $base_path . 'listar.php';
                            if (file_exists($view_file)) {
                                include $view_file;
                            } else {
                                echo "<div class='bg-red-100 border-l-4 border-red-500 text-red-700 p-4'>";
                                echo "<h3>Erro: View não encontrada</h3>";
                                echo "<p>Arquivo não encontrado: $view_file</p>";
                                echo "<p>Diretório atual: " . __DIR__ . "</p>";
                                echo "<p>Arquivos disponíveis:</p>";
                                if (is_dir(__DIR__ . '/views/turma_disciplinas/')) {
                                    $files = scandir(__DIR__ . '/views/turma_disciplinas/');
                                    echo "<ul>";
                                    foreach ($files as $file) {
                                        if ($file != '.' && $file != '..') {
                                            echo "<li>$file</li>";
                                        }
                                    }
                                    echo "</ul>";
                                } else {
                                    echo "<p>Diretório views/turma_disciplinas/ não existe</p>";
                                }
                                echo "</div>";
                            }
                            break;
                        case 'gerenciar':
                            $view_file = $base_path . 'gerenciar.php';
                            if (file_exists($view_file)) {
                                include $view_file;
                            } else {
                                echo "<div class='bg-red-100 border-l-4 border-red-500 text-red-700 p-4'>";
                                echo "<h3>Erro: View não encontrada</h3>";
                                echo "<p>Arquivo não encontrado: $view_file</p>";
                                echo "</div>";
                            }
                            break;
                        default:
                            $view_file = $base_path . 'listar.php';
                            if (file_exists($view_file)) {
                                include $view_file;
                            } else {
                                echo "<div class='bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4'>";
                                echo "<h3>Sistema em Configuração</h3>";
                                echo "<p>O módulo Turma-Disciplinas está sendo configurado.</p>";
                                echo "<p><a href='verificar_e_criar_tabela.php' class='text-blue-600 hover:underline'>Clique aqui para configurar</a></p>";
                                echo "</div>";
                            }
                            break;
                    }
                    ?>
                </div>
            </main>

            <!-- Footer -->
            <?php include 'includes/footer.php'; ?>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
