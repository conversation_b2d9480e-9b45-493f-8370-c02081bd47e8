<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Incluir configuração básica
require_once 'includes/config.php';

// Conectar ao banco usando função simples
try {
    $pdo = getConnection();
} catch (Exception $e) {
    die("Erro de conexão com banco: " . $e->getMessage());
}

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        return $_SESSION['user_nome'] ?? $_SESSION['user_name'] ?? 'Usuário';
    }
}

// Título da página
$pageTitle = "Folha de Pagamento";

// Processar ações (gerar nova folha, fechar folha, etc.)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'gerar_folha':
                    $mes = (int)$_POST['mes'];
                    $ano = (int)$_POST['ano'];
                    
                    // Verificar se já existe folha para este período
                    $stmt = $pdo->prepare("SELECT id FROM folha_pagamento WHERE mes_referencia = ? AND ano_referencia = ?");
                    $stmt->execute([$mes, $ano]);
                    
                    if ($stmt->fetch()) {
                        $_SESSION['msg_error'] = "Já existe uma folha para {$mes}/{$ano}";
                    } else {
                        // Criar nova folha
                        $stmt = $pdo->prepare("
                            INSERT INTO folha_pagamento (mes_referencia, ano_referencia, data_geracao, valor_total, status) 
                            VALUES (?, ?, NOW(), 0.00, 'aberta')
                        ");
                        $stmt->execute([$mes, $ano]);
                        $folha_id = $pdo->lastInsertId();
                        
                        // Buscar funcionários ativos e calcular folha
                        $stmt = $pdo->query("SELECT * FROM funcionarios WHERE status = 'ativo'");
                        $funcionarios = $stmt->fetchAll();
                        
                        $valor_total_folha = 0;
                        
                        foreach ($funcionarios as $funcionario) {
                            $salario_base = $funcionario['salario'];
                            
                            // Cálculos básicos (simplificados)
                            $inss = $salario_base * 0.11; // 11% INSS
                            $fgts = $salario_base * 0.08; // 8% FGTS
                            $irrf = $salario_base > 2000 ? $salario_base * 0.075 : 0; // 7.5% IR se > R$ 2000
                            
                            $valor_liquido = $salario_base - $inss - $irrf;
                            $valor_total_folha += $valor_liquido;
                            
                            // Inserir item da folha
                            $stmt = $pdo->prepare("
                                INSERT INTO folha_pagamento_itens 
                                (folha_id, funcionario_id, salario_base, inss, irrf, fgts, valor_liquido) 
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([$folha_id, $funcionario['id'], $salario_base, $inss, $irrf, $fgts, $valor_liquido]);
                        }
                        
                        // Atualizar valor total da folha
                        $stmt = $pdo->prepare("UPDATE folha_pagamento SET valor_total = ? WHERE id = ?");
                        $stmt->execute([$valor_total_folha, $folha_id]);
                        
                        $_SESSION['msg_success'] = "Folha de pagamento gerada com sucesso para {$mes}/{$ano}";
                    }
                    break;
                    
                case 'fechar_folha':
                    $folha_id = (int)$_POST['folha_id'];
                    $stmt = $pdo->prepare("UPDATE folha_pagamento SET status = 'fechada' WHERE id = ?");
                    $stmt->execute([$folha_id]);
                    $_SESSION['msg_success'] = "Folha de pagamento fechada com sucesso";
                    break;
            }
        }
    } catch (Exception $e) {
        $_SESSION['msg_error'] = "Erro ao processar ação: " . $e->getMessage();
    }
}
// Tentar carregar os dados do dashboard de folha de pagamento
try {
    // Dados para o Dashboard
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM folha_pagamento WHERE status = 'aberta'");
    $folhasAbertas = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM folha_pagamento WHERE status = 'fechada'");
    $folhasFechadas = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COALESCE(SUM(valor_total), 0) as total FROM folha_pagamento WHERE MONTH(data_geracao) = MONTH(CURDATE()) AND YEAR(data_geracao) = YEAR(CURDATE())");
    $valorMesAtual = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM funcionarios WHERE status = 'ativo'");
    $funcionariosAtivos = $stmt->fetch();
    
    // Folhas de pagamento recentes
    $stmt = $pdo->query("
        SELECT fp.*, 
               (SELECT COUNT(*) FROM folha_pagamento_itens WHERE folha_id = fp.id) as total_funcionarios
        FROM folha_pagamento fp 
        ORDER BY fp.data_geracao DESC 
        LIMIT 5
    ");
    $folhasRecentes = $stmt->fetchAll();
    
    // Folhas por status para gráfico
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as total 
        FROM folha_pagamento 
        GROUP BY status
    ");
    $folhasPorStatus = $stmt->fetchAll();
    
    // Evolução dos valores mensais (últimos 6 meses)
    $stmt = $pdo->query("
        SELECT 
            CONCAT(mes_referencia, '/', ano_referencia) as periodo,
            SUM(valor_total) as valor_total
        FROM folha_pagamento 
        WHERE data_geracao >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY ano_referencia, mes_referencia
        ORDER BY ano_referencia, mes_referencia
        LIMIT 6
    ");
    $evolucaoMensal = $stmt->fetchAll();
    
    // Maiores salários
    $stmt = $pdo->query("
        SELECT f.nome, f.cargo, f.departamento, f.salario
        FROM funcionarios f
        WHERE f.status = 'ativo'
        ORDER BY f.salario DESC
        LIMIT 5
    ");
    $maioresSalarios = $stmt->fetchAll();
    
} catch (Exception $e) {
    $_SESSION['msg_error'] = "Erro ao carregar dados: " . $e->getMessage();
    // Definir arrays vazios para evitar erros
    $folhasAbertas = ['total' => 0];
    $folhasFechadas = ['total' => 0];
    $valorMesAtual = ['total' => 0];
    $funcionariosAtivos = ['total' => 0];
    $folhasRecentes = [];
    $folhasPorStatus = [];
    $evolucaoMensal = [];
    $maioresSalarios = [];
}

// Incluir o cabeçalho e sidebar padronizados
include_once 'includes/header_padronizado_novo.php';
include_once 'includes/sidebar_padronizado.php';
?>

<div class="main-content" style="padding-left: 0.25rem; margin-left: 16rem;">
    <!-- Header da página -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900"><?php echo $pageTitle; ?></h1>
            <p class="text-gray-600">Gestão de Folha de Pagamento e RH</p>
        </div>
        <div class="flex space-x-2">
            <button onclick="showGerarFolhaModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Gerar Nova Folha
            </button>
        </div>
    </div>

    <!-- Dashboard Content - Cards de Folha de Pagamento -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Card Folhas Abertas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-yellow-100 mr-4">
                <i class="fas fa-folder-open text-yellow-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Folhas Abertas</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $folhasAbertas['total'] ?? 0; ?>
                </p>
            </div>
        </div>

        <!-- Card Folhas Fechadas -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-blue-100 mr-4">
                <i class="fas fa-check-circle text-blue-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Folhas Fechadas</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $folhasFechadas['total'] ?? 0; ?>
                </p>
            </div>
        </div>

        <!-- Card Valor Mês Atual -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-green-100 mr-4">
                <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Valor Mês Atual</p>
                <p class="text-xl font-bold text-gray-800">
                    R$ <?php echo number_format($valorMesAtual['total'] ?? 0, 2, ',', '.'); ?>
                </p>
            </div>
        </div>

        <!-- Card Funcionários Ativos -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-purple-100 mr-4">
                <i class="fas fa-users text-purple-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Funcionários Ativos</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $funcionariosAtivos['total'] ?? 0; ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Gráficos e Tabelas -->
    <div class="grid grid-cols-1 lg:grid-cols-7 gap-6 mb-8">
        <!-- Gráfico Evolução Mensal -->
        <div class="lg:col-span-4 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Evolução dos Valores Mensais</h2>
            <div class="h-80">
                <canvas id="chartEvolucao"></canvas>
            </div>
        </div>
        
        <!-- Folhas Recentes -->
        <div class="lg:col-span-3 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Folhas Recentes</h2>
            <?php if (empty($folhasRecentes)): ?>
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-file-invoice-dollar text-3xl mb-2"></i>
                <p>Nenhuma folha encontrada</p>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left pb-3 text-sm font-medium text-gray-500">Período</th>
                            <th class="text-left pb-3 text-sm font-medium text-gray-500">Status</th>
                            <th class="text-right pb-3 text-sm font-medium text-gray-500">Valor</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($folhasRecentes as $folha): ?>
                        <tr class="border-b border-gray-100">
                            <td class="py-3 text-sm">
                                <div class="font-medium text-gray-900"><?php echo $folha['mes_referencia'] . '/' . $folha['ano_referencia']; ?></div>
                                <div class="text-xs text-gray-500"><?php echo $folha['total_funcionarios']; ?> funcionários</div>
                            </td>
                            <td class="py-3 text-sm">
                                <?php 
                                $statusColors = [
                                    'aberta' => 'bg-yellow-100 text-yellow-800',
                                    'fechada' => 'bg-blue-100 text-blue-800',
                                    'paga' => 'bg-green-100 text-green-800'
                                ];
                                $color = $statusColors[$folha['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $color; ?>">
                                    <?php echo ucfirst($folha['status']); ?>
                                </span>
                            </td>
                            <td class="py-3 text-sm text-right text-gray-600">
                                R$ <?php echo number_format($folha['valor_total'], 2, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-center">
                <a href="folha_pagamento_lista.php" class="text-sm text-green-600 hover:text-green-700 font-medium">Ver todas as folhas</a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Tabela de Maiores Salários -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Maiores Salários</h2>
        <?php if (empty($maioresSalarios)): ?>
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-chart-line text-3xl mb-2"></i>
            <p>Nenhum dado disponível</p>
        </div>
        <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left pb-3 text-sm font-medium text-gray-500">Funcionário</th>
                        <th class="text-left pb-3 text-sm font-medium text-gray-500">Cargo</th>
                        <th class="text-left pb-3 text-sm font-medium text-gray-500">Departamento</th>
                        <th class="text-right pb-3 text-sm font-medium text-gray-500">Salário</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($maioresSalarios as $funcionario): ?>
                    <tr class="border-b border-gray-100">
                        <td class="py-3 text-sm font-medium text-gray-900">
                            <?php echo htmlspecialchars($funcionario['nome']); ?>
                        </td>
                        <td class="py-3 text-sm text-gray-600">
                            <?php echo htmlspecialchars($funcionario['cargo']); ?>
                        </td>
                        <td class="py-3 text-sm text-gray-600">
                            <?php echo htmlspecialchars($funcionario['departamento'] ?? 'Não informado'); ?>
                        </td>
                        <td class="py-3 text-sm text-right text-gray-600">
                            R$ <?php echo number_format($funcionario['salario'], 2, ',', '.'); ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>

    <!-- Ações Rápidas -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Ações Rápidas</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <button onclick="showGerarFolhaModal()" class="flex items-center p-3 bg-green-50 text-green-700 rounded-md hover:bg-green-100 transition-colors">
                <div class="p-2 rounded-full bg-green-100">
                    <i class="fas fa-plus text-green-600"></i>
                </div>
                <span class="ml-3 font-medium">Gerar Nova Folha</span>
            </button>
            
            <a href="folha_pagamento_lista.php" class="flex items-center p-3 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                <div class="p-2 rounded-full bg-blue-100">
                    <i class="fas fa-list text-blue-600"></i>
                </div>
                <span class="ml-3 font-medium">Listar Folhas</span>
            </a>
            
            <a href="relatorio_folha.php" class="flex items-center p-3 bg-purple-50 text-purple-700 rounded-md hover:bg-purple-100 transition-colors">
                <div class="p-2 rounded-full bg-purple-100">
                    <i class="fas fa-chart-bar text-purple-600"></i>
                </div>
                <span class="ml-3 font-medium">Relatórios</span>
            </a>
            
            <a href="funcionarios.php" class="flex items-center p-3 bg-yellow-50 text-yellow-700 rounded-md hover:bg-yellow-100 transition-colors">
                <div class="p-2 rounded-full bg-yellow-100">
                    <i class="fas fa-users text-yellow-600"></i>
                </div>
                <span class="ml-3 font-medium">Funcionários</span>
            </a>
        </div>
    </div>
</div>

<!-- Modal Gerar Nova Folha -->
<div id="modalGerarFolha" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Gerar Nova Folha</h3>
            <form method="POST">
                <input type="hidden" name="action" value="gerar_folha">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mês</label>
                    <select name="mes" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <?php for ($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo $i == date('n') ? 'selected' : ''; ?>>
                            <?php echo date('F', mktime(0, 0, 0, $i, 1)); ?>
                        </option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Ano</label>
                    <select name="ano" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <?php for ($i = date('Y') - 1; $i <= date('Y') + 1; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo $i == date('Y') ? 'selected' : ''; ?>><?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideGerarFolhaModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                        Cancelar
                    </button>
                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-plus mr-2"></i>Gerar Folha
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Scripts para gráficos e interações -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dados para o gráfico de evolução
    const periodos = <?php echo json_encode(array_column($evolucaoMensal ?? [], 'periodo')); ?>;
    const valores = <?php echo json_encode(array_column($evolucaoMensal ?? [], 'valor_total')); ?>;
    
    if (periodos.length > 0) {
        const ctx = document.getElementById('chartEvolucao').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: periodos,
                datasets: [{
                    label: 'Valor da Folha',
                    data: valores,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'R$ ' + context.raw.toLocaleString('pt-BR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        }
                    }
                }
            }
        });
    }
});

// Funções do modal
function showGerarFolhaModal() {
    document.getElementById('modalGerarFolha').classList.remove('hidden');
}

function hideGerarFolhaModal() {
    document.getElementById('modalGerarFolha').classList.add('hidden');
}

// Fechar modal clicando fora
document.getElementById('modalGerarFolha').addEventListener('click', function(e) {
    if (e.target === this) {
        hideGerarFolhaModal();
    }
});
</script>

<?php include 'includes/footer_padronizado.php'; ?>