<?php
/**
 * Webhook do Asaas
 * Recebe notificações de pagamento e atualiza o status das mensalidades
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/asaas_config.php';

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Obter dados do webhook
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Log do webhook recebido
error_log('Webhook Asaas recebido: ' . $input);

// Verificar se os dados são válidos
if (!$data || !isset($data['event'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid data']);
    exit;
}

try {
    require_once __DIR__ . '/../config/database.php';
    // $pdo está disponível globalmente
    
    // Salvar webhook no log
    $stmt = $pdo->prepare("
        INSERT INTO asaas_webhooks (event_type, payment_id, customer_id, webhook_data) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        $data['event'],
        $data['payment']['id'] ?? null,
        $data['payment']['customer'] ?? null,
        $input
    ]);
    
    $webhook_id = $pdo->lastInsertId();
      // Processar diferentes tipos de evento
    switch ($data['event']) {
        case 'PAYMENT_RECEIVED':
        case 'PAYMENT_CONFIRMED':
            processarPagamento($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_OVERDUE':
            marcarVencido($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_DELETED':
            cancelarPagamento($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_CREATED':
        case 'PAYMENT_AWAITING_PAYMENT':
            atualizarStatusPagamento($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_UPDATED':
            atualizarDadosPagamento($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_RESTORED':
            restaurarPagamento($pdo, $data['payment'], $webhook_id);
            break;
            
        case 'PAYMENT_CHARGEBACK_REQUESTED':
        case 'PAYMENT_CHARGEBACK_DISPUTE':
            processarEstorno($pdo, $data['payment'], $webhook_id);
            break;
            
        default:
            error_log("Evento não tratado: " . $data['event']);
            break;
    }
    
    // Marcar webhook como processado
    $stmt = $pdo->prepare("UPDATE asaas_webhooks SET processed = 1, processed_at = NOW() WHERE id = ?");
    $stmt->execute([$webhook_id]);
    
    http_response_code(200);
    echo json_encode(['status' => 'success']);
    
} catch (Exception $e) {
    error_log('Erro no webhook Asaas: ' . $e->getMessage());
    
    if (isset($webhook_id)) {
        $stmt = $pdo->prepare("UPDATE asaas_webhooks SET error_message = ? WHERE id = ?");
        $stmt->execute([$e->getMessage(), $webhook_id]);
    }
    
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Processar pagamento confirmado
 */
function processarPagamento($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    $valor_pago = $payment['value'];
    $data_pagamento = $payment['paymentDate'];
    
    // Atualizar mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET status = 'pago', 
            data_pagamento = ?, 
            valor_pago = ?,
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$data_pagamento, $valor_pago, $payment['status'], $payment_id]);
    
    // Atualizar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET status = 'pago', 
            data_pagamento = ?, 
            valor_pago = ?,
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$data_pagamento, $valor_pago, $payment['status'], $payment_id]);
    
    error_log("Pagamento processado: {$payment_id} - Valor: {$valor_pago}");
}

/**
 * Marcar pagamento como vencido
 */
function marcarVencido($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    
    // Atualizar mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET status = 'vencido',
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    // Atualizar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET status = 'vencido',
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    error_log("Pagamento marcado como vencido: {$payment_id}");
}

/**
 * Cancelar pagamento
 */
function cancelarPagamento($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    
    // Atualizar mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET status = 'cancelado',
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    // Atualizar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET status = 'cancelado',
            asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    error_log("Pagamento cancelado: {$payment_id}");
}

/**
 * Atualizar status do pagamento (para eventos como CREATED, AWAITING_PAYMENT)
 */
function atualizarStatusPagamento($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    $status = $payment['status'];
    
    // Atualizar mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$status, $payment_id]);
    
    // Atualizar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET asaas_status = ?
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$status, $payment_id]);
    
    error_log("Status atualizado: {$payment_id} - Status: {$status}");
}

/**
 * Atualizar dados do pagamento
 */
function atualizarDadosPagamento($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    $status = $payment['status'];
    $value = $payment['value'] ?? null;
    $due_date = $payment['dueDate'] ?? null;
    
    // Atualizar mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET asaas_status = ?, 
            valor = COALESCE(?, valor),
            data_vencimento = COALESCE(?, data_vencimento)
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$status, $value, $due_date, $payment_id]);
    
    // Atualizar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET asaas_status = ?,
            valor = COALESCE(?, valor),
            data_vencimento = COALESCE(?, data_vencimento)
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$status, $value, $due_date, $payment_id]);
    
    error_log("Dados atualizados: {$payment_id} - Status: {$status}");
}

/**
 * Restaurar pagamento
 */
function restaurarPagamento($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    
    // Restaurar mensalidade para pendente
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET status = 'pendente',
            asaas_status = ?,
            data_pagamento = NULL,
            valor_pago = NULL
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    // Restaurar boleto se existir
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET status = 'pendente',
            asaas_status = ?,
            data_pagamento = NULL,
            valor_pago = NULL
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment_id]);
    
    error_log("Pagamento restaurado: {$payment_id}");
}

/**
 * Processar estorno/chargeback
 */
function processarEstorno($pdo, $payment, $webhook_id) {
    $payment_id = $payment['id'];
    
    // Marcar como estornado na mensalidade
    $stmt = $pdo->prepare("
        UPDATE mensalidades_alunos 
        SET status = 'pendente',
            asaas_status = ?,
            observacoes = CONCAT(COALESCE(observacoes, ''), '\n[ESTORNO] ', NOW(), ' - ', ?)
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment['chargebackReason'] ?? 'Estorno solicitado', $payment_id]);
    
    // Marcar boleto como estornado
    $stmt = $pdo->prepare("
        UPDATE boletos 
        SET status = 'pendente',
            asaas_status = ?,
            observacoes = CONCAT(COALESCE(observacoes, ''), '\n[ESTORNO] ', NOW(), ' - ', ?)
        WHERE asaas_payment_id = ?
    ");
    $stmt->execute([$payment['status'], $payment['chargebackReason'] ?? 'Estorno solicitado', $payment_id]);
    
    error_log("Estorno processado: {$payment_id}");
}
?>
