<?php
/**
 * <PERSON><PERSON><PERSON><PERSON>rma-Disciplinas - Versão Simplificada
 * Funciona sem arquivos de view externos
 */

require_once 'includes/init.php';

$db = Database::getInstance();

// Funções auxiliares
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        return $default;
    }
}

function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        $result = $db->fetchAll($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        return $default;
    }
}

// Verifica se a tabela turma_disciplinas existe
$sql_check = "SHOW TABLES LIKE 'turma_disciplinas'";
$tabela_existe = executarConsulta($db, $sql_check);

$titulo_pagina = 'Gerenciar Turma-Disciplinas';
$action = $_GET['action'] ?? 'listar';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - Faciência ERP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/layout-fixes.css" rel="stylesheet">
    <link href="css/turma-disciplinas.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Correções específicas para esta página */
        .main-content {
            margin-left: 256px !important;
            width: calc(100% - 256px) !important;
            min-height: 100vh;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }

        /* Estilo para paginação */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .pagination a, .pagination span {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            text-decoration: none;
            transition: all 0.2s;
        }

        .pagination a:hover {
            background-color: #f3f4f6;
        }

        .pagination .current {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pagination .disabled {
            color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <?php include '../includes/header.php'; ?>

        <!-- Main -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>
                        <a href="verificar_e_criar_tabela.php" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                            <i class="fas fa-cog mr-2"></i>Configurar Sistema
                        </a>
                    </div>

                    <?php if (!$tabela_existe): ?>
                    <!-- Aviso: Tabela não existe -->
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Tabela turma_disciplinas não encontrada</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>Para usar este módulo completamente, você precisa criar a tabela turma_disciplinas primeiro.</p>
                                    <div class="mt-3">
                                        <a href="verificar_e_criar_tabela.php" 
                                           class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm transition duration-200 mr-2">
                                            <i class="fas fa-plus mr-1"></i>
                                            Criar Tabela
                                        </a>
                                        <a href="sql_simples_turma_disciplinas.sql" 
                                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition duration-200"
                                           target="_blank">
                                            <i class="fas fa-download mr-1"></i>
                                            Baixar SQL
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Estatísticas -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <?php
                        if ($tabela_existe) {
                            $stats = [
                                'Total de Turmas' => "SELECT COUNT(*) as total FROM turmas",
                                'Turmas com Disciplinas' => "SELECT COUNT(DISTINCT turma_id) as total FROM turma_disciplinas WHERE status = 'ativo'",
                                'Total de Vinculações' => "SELECT COUNT(*) as total FROM turma_disciplinas WHERE status = 'ativo'",
                                'Disciplinas Disponíveis' => "SELECT COUNT(*) as total FROM disciplinas WHERE status = 'ativo' AND carga_horaria > 0"
                            ];
                        } else {
                            $stats = [
                                'Total de Turmas' => "SELECT COUNT(*) as total FROM turmas",
                                'Turmas com Disciplinas' => "SELECT 0 as total",
                                'Total de Vinculações' => "SELECT 0 as total",
                                'Disciplinas Disponíveis' => "SELECT COUNT(*) as total FROM disciplinas WHERE status = 'ativo' AND carga_horaria > 0"
                            ];
                        }
                        
                        foreach ($stats as $label => $sql_stat) {
                            $result = executarConsulta($db, $sql_stat);
                            $valor = $result['total'] ?? 0;
                            ?>
                            <div class="bg-white rounded-lg shadow-md p-6">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-gray-700"><?= $label ?></h3>
                                        <p class="text-3xl font-bold text-blue-600"><?= $valor ?></p>
                                    </div>
                                    <div class="text-blue-500">
                                        <i class="fas fa-chart-bar text-2xl"></i>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                    </div>

                    <!-- Lista de Turmas -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-semibold flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    Turmas e suas Disciplinas
                                </h2>
                                <div class="text-sm text-blue-100">
                                    Total: <?= $total_turmas ?> turmas | Página <?= $page ?> de <?= $total_pages ?>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            
                            <?php
                            // Configuração da paginação
                            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                            $per_page = 20; // Turmas por página
                            $offset = ($page - 1) * $per_page;

                            // Conta total de turmas para paginação
                            if ($tabela_existe) {
                                $sql_count = "SELECT COUNT(DISTINCT t.id) as total
                                              FROM turmas t
                                              LEFT JOIN cursos c ON t.curso_id = c.id";
                            } else {
                                $sql_count = "SELECT COUNT(*) as total FROM turmas";
                            }

                            $total_result = executarConsulta($db, $sql_count);
                            $total_turmas = $total_result['total'] ?? 0;
                            $total_pages = ceil($total_turmas / $per_page);

                            // Busca turmas
                            if ($tabela_existe) {
                                $sql = "SELECT
                                            t.id,
                                            t.nome as turma_nome,
                                            t.status as turma_status,
                                            c.nome as curso_nome,
                                            COUNT(td.id) as total_disciplinas_vinculadas,
                                            COUNT(CASE WHEN td.status = 'ativo' THEN 1 END) as disciplinas_ativas,
                                            SUM(CASE WHEN td.status = 'ativo' THEN COALESCE(td.carga_horaria_turma, d.carga_horaria) ELSE 0 END) as carga_total,
                                            COUNT(d_curso.id) as disciplinas_disponiveis_curso
                                        FROM turmas t
                                        LEFT JOIN cursos c ON t.curso_id = c.id
                                        LEFT JOIN turma_disciplinas td ON t.id = td.turma_id
                                        LEFT JOIN disciplinas d ON td.disciplina_id = d.id
                                        LEFT JOIN disciplinas d_curso ON c.id = d_curso.curso_id AND d_curso.status = 'ativo' AND d_curso.carga_horaria > 0
                                        GROUP BY t.id, t.nome, t.status, c.nome
                                        ORDER BY t.nome ASC
                                        LIMIT $per_page OFFSET $offset";
                            } else {
                                $sql = "SELECT
                                            t.id,
                                            t.nome as turma_nome,
                                            t.status as turma_status,
                                            c.nome as curso_nome,
                                            0 as total_disciplinas_vinculadas,
                                            0 as disciplinas_ativas,
                                            0 as carga_total,
                                            COUNT(d_curso.id) as disciplinas_disponiveis_curso
                                        FROM turmas t
                                        LEFT JOIN cursos c ON t.curso_id = c.id
                                        LEFT JOIN disciplinas d_curso ON c.id = d_curso.curso_id AND d_curso.status = 'ativo' AND d_curso.carga_horaria > 0
                                        GROUP BY t.id, t.nome, t.status, c.nome
                                        ORDER BY t.nome ASC
                                        LIMIT $per_page OFFSET $offset";
                            }

                            $turmas = executarConsultaAll($db, $sql);
                            ?>

                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turma</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Curso</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disciplinas</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Carga Total</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disponíveis</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php if ($turmas): ?>
                                            <?php foreach ($turmas as $turma): ?>
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($turma['turma_nome']) ?></div>
                                                        <div class="text-sm text-gray-500">ID: <?= $turma['id'] ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900"><?= htmlspecialchars($turma['curso_nome'] ?? 'N/A') ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <?php
                                                        $status_colors = [
                                                            'em_andamento' => 'bg-green-100 text-green-800',
                                                            'concluida' => 'bg-blue-100 text-blue-800',
                                                            'cancelada' => 'bg-red-100 text-red-800'
                                                        ];
                                                        $status_class = $status_colors[$turma['turma_status']] ?? 'bg-gray-100 text-gray-800';
                                                        ?>
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $status_class ?>">
                                                            <?= ucfirst(str_replace('_', ' ', $turma['turma_status'])) ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="text-sm font-medium text-gray-900"><?= $turma['disciplinas_ativas'] ?></span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="text-sm text-gray-900"><?= $turma['carga_total'] ?? 0 ?>h</span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="text-sm text-gray-500"><?= $turma['disciplinas_disponiveis_curso'] ?></span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <?php if ($tabela_existe): ?>
                                                            <a href="turma_disciplinas.php?action=gerenciar&turma_id=<?= $turma['id'] ?>" 
                                                               class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm transition duration-200 mr-2">
                                                                <i class="fas fa-cog mr-1"></i>
                                                                Gerenciar
                                                            </a>
                                                        <?php else: ?>
                                                            <span class="text-gray-400 text-sm">Criar tabela primeiro</span>
                                                        <?php endif; ?>
                                                        
                                                        <a href="declaracao_grade_curricular.php?turma_id=<?= $turma['id'] ?>" 
                                                           class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm transition duration-200"
                                                           target="_blank">
                                                            <i class="fas fa-file-pdf mr-1"></i>
                                                            Grade
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                                    <i class="fas fa-inbox mr-2"></i>
                                                    Nenhuma turma encontrada
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Paginação -->
                            <?php if ($total_pages > 1): ?>
                            <div class="px-6 py-4 border-t border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-700">
                                        Mostrando <?= ($offset + 1) ?> a <?= min($offset + $per_page, $total_turmas) ?> de <?= $total_turmas ?> turmas
                                    </div>

                                    <div class="pagination">
                                        <?php
                                        // Botão Anterior
                                        if ($page > 1) {
                                            echo '<a href="?page=' . ($page - 1) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">';
                                            echo '<i class="fas fa-chevron-left"></i> Anterior</a>';
                                        } else {
                                            echo '<span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-l-md cursor-not-allowed">';
                                            echo '<i class="fas fa-chevron-left"></i> Anterior</span>';
                                        }

                                        // Números das páginas
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);

                                        // Primeira página
                                        if ($start_page > 1) {
                                            echo '<a href="?page=1" class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300 hover:bg-gray-50">1</a>';
                                            if ($start_page > 2) {
                                                echo '<span class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300">...</span>';
                                            }
                                        }

                                        // Páginas do meio
                                        for ($i = $start_page; $i <= $end_page; $i++) {
                                            if ($i == $page) {
                                                echo '<span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600">' . $i . '</span>';
                                            } else {
                                                echo '<a href="?page=' . $i . '" class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300 hover:bg-gray-50">' . $i . '</a>';
                                            }
                                        }

                                        // Última página
                                        if ($end_page < $total_pages) {
                                            if ($end_page < $total_pages - 1) {
                                                echo '<span class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300">...</span>';
                                            }
                                            echo '<a href="?page=' . $total_pages . '" class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300 hover:bg-gray-50">' . $total_pages . '</a>';
                                        }

                                        // Botão Próximo
                                        if ($page < $total_pages) {
                                            echo '<a href="?page=' . ($page + 1) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">';
                                            echo 'Próximo <i class="fas fa-chevron-right"></i></a>';
                                        } else {
                                            echo '<span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-r-md cursor-not-allowed">';
                                            echo 'Próximo <i class="fas fa-chevron-right"></i></span>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Links Úteis -->
                    <div class="mt-6 bg-blue-50 border-l-4 border-blue-400 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Links Úteis:</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li><a href="verificar_e_criar_tabela.php" class="hover:underline">🔧 Configurar Sistema</a></li>
                                        <li><a href="vincular_disciplinas_existentes.php" class="hover:underline">🔗 Vincular Disciplinas Existentes</a></li>
                                        <li><a href="declaracao_grade_curricular.php" class="hover:underline">📄 Declaração de Grade Curricular</a></li>
                                        <li><a href="teste_turma_disciplinas.php" class="hover:underline">🧪 Teste do Sistema</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </main>

        <!-- Footer -->
        <?php include '../includes/footer.php'; ?>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
