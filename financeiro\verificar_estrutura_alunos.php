<?php
try {
    $pdo = new PDO("mysql:host=localhost;dbname=u682219090_faciencia_erp;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Estrutura da tabela alunos:\n";
    $result = $pdo->query("SHOW COLUMNS FROM alunos");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
?>
