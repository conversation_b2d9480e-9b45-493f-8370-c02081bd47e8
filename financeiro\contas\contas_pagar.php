<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Incluir configuração básica
require_once 'includes/config.php';

// Conectar ao banco usando função simples
try {
    $pdo = getConnection();
} catch (Exception $e) {
    die("Erro de conexão com banco: " . $e->getMessage());
}

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        return $_SESSION['user_nome'] ?? $_SESSION['user_name'] ?? 'Usuário';
    }
}

// Título da página
$pageTitle = "Contas a Pagar";

// Processar ações (adicionar, editar, excluir, pagar)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'adicionar':
                    $descricao = $_POST['descricao'];
                    $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                    $data_vencimento = $_POST['data_vencimento'];
                    $fornecedor_nome = $_POST['fornecedor_nome'] ?? null;
                    $categoria_id = $_POST['categoria_id'] ?? null;
                    $observacoes = $_POST['observacoes'] ?? null;
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO contas_pagar (descricao, valor, data_vencimento, fornecedor_nome, categoria_id, observacoes, status) 
                        VALUES (?, ?, ?, ?, ?, ?, 'pendente')
                    ");
                    $stmt->execute([$descricao, $valor, $data_vencimento, $fornecedor_nome, $categoria_id, $observacoes]);
                    $_SESSION['msg_success'] = "Conta a pagar adicionada com sucesso!";
                    break;
                    
                case 'pagar':
                    $id = (int)$_POST['id'];
                    $data_pagamento = $_POST['data_pagamento'];
                    $forma_pagamento = $_POST['forma_pagamento'];
                    
                    $stmt = $pdo->prepare("
                        UPDATE contas_pagar 
                        SET status = 'pago', data_pagamento = ?, forma_pagamento = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$data_pagamento, $forma_pagamento, $id]);
                    $_SESSION['msg_success'] = "Conta marcada como paga!";
                    break;
                    
                case 'cancelar':
                    $id = (int)$_POST['id'];
                    $stmt = $pdo->prepare("UPDATE contas_pagar SET status = 'cancelado', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$id]);
                    $_SESSION['msg_success'] = "Conta cancelada!";
                    break;
                    
                case 'excluir':
                    $id = (int)$_POST['id'];
                    $stmt = $pdo->prepare("DELETE FROM contas_pagar WHERE id = ?");
                    $stmt->execute([$id]);
                    $_SESSION['msg_success'] = "Conta excluída!";
                    break;
            }
        }
    } catch (Exception $e) {
        $_SESSION['msg_error'] = "Erro ao processar ação: " . $e->getMessage();
    }
}

// Filtros
$filtro_status = $_GET['status'] ?? 'todos';
$filtro_data = $_GET['data'] ?? 'todos';
$busca = $_GET['busca'] ?? '';

// Construir WHERE clause
$where_conditions = [];
$params = [];

if ($filtro_status !== 'todos') {
    $where_conditions[] = "status = ?";
    $params[] = $filtro_status;
}

if ($filtro_data === 'hoje') {
    $where_conditions[] = "data_vencimento = CURDATE()";
} elseif ($filtro_data === 'semana') {
    $where_conditions[] = "data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
} elseif ($filtro_data === 'mes') {
    $where_conditions[] = "MONTH(data_vencimento) = MONTH(CURDATE()) AND YEAR(data_vencimento) = YEAR(CURDATE())";
} elseif ($filtro_data === 'vencidas') {
    $where_conditions[] = "data_vencimento < CURDATE() AND status = 'pendente'";
}

if (!empty($busca)) {
    $where_conditions[] = "(descricao LIKE ? OR fornecedor_nome LIKE ?)";
    $params[] = "%$busca%";
    $params[] = "%$busca%";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Tentar carregar os dados do dashboard de contas a pagar
try {
    // Dados para o Dashboard
    $stmt = $pdo->query("SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total FROM contas_pagar WHERE status = 'pendente'");
    $contasPendentes = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total FROM contas_pagar WHERE status = 'pago'");
    $contasPagas = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total FROM contas_pagar WHERE data_vencimento < CURDATE() AND status = 'pendente'");
    $contasVencidas = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total FROM contas_pagar WHERE data_vencimento = CURDATE() AND status = 'pendente'");
    $contasHoje = $stmt->fetch();
    
    // Contas a pagar (com filtros)
    $stmt = $pdo->prepare("
        SELECT cp.*, cf.nome as categoria_nome
        FROM contas_pagar cp
        LEFT JOIN categorias_financeiras cf ON cp.categoria_id = cf.id
        $where_clause
        ORDER BY cp.data_vencimento ASC, cp.created_at DESC
        LIMIT 50
    ");
    $stmt->execute($params);
    $contas = $stmt->fetchAll();
    
    // Evolução mensal das contas (últimos 6 meses)
    $stmt = $pdo->query("
        SELECT 
            CONCAT(MONTH(data_vencimento), '/', YEAR(data_vencimento)) as periodo,
            COUNT(*) as total_contas,
            SUM(valor) as valor_total
        FROM contas_pagar 
        WHERE data_vencimento >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY YEAR(data_vencimento), MONTH(data_vencimento)
        ORDER BY YEAR(data_vencimento), MONTH(data_vencimento)
        LIMIT 6
    ");
    $evolucaoMensal = $stmt->fetchAll();
    
    // Categorias com mais gastos
    $stmt = $pdo->query("
        SELECT cf.nome, COUNT(*) as total_contas, SUM(cp.valor) as valor_total
        FROM contas_pagar cp
        LEFT JOIN categorias_financeiras cf ON cp.categoria_id = cf.id
        WHERE cp.status = 'pago' AND cp.data_pagamento >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        GROUP BY cp.categoria_id, cf.nome
        ORDER BY valor_total DESC
        LIMIT 5
    ");
    $categoriasMaisGastos = $stmt->fetchAll();
    
    // Categorias para o select
    $stmt = $pdo->query("SELECT id, nome FROM categorias_financeiras WHERE tipo = 'despesa' AND status = 'ativo' ORDER BY nome");
    $categorias = $stmt->fetchAll();
    
} catch (Exception $e) {
    $_SESSION['msg_error'] = "Erro ao carregar dados: " . $e->getMessage();
    // Definir arrays vazios para evitar erros
    $contasPendentes = ['total' => 0, 'valor_total' => 0];
    $contasPagas = ['total' => 0, 'valor_total' => 0];
    $contasVencidas = ['total' => 0, 'valor_total' => 0];
    $contasHoje = ['total' => 0, 'valor_total' => 0];
    $contas = [];
    $evolucaoMensal = [];
    $categoriasMaisGastos = [];
    $categorias = [];
}

// Incluir o cabeçalho e sidebar padronizados
include_once 'includes/header_padronizado_novo.php';
include_once 'includes/sidebar_padronizado.php';
?>


    <div class="container mx-auto px-4 py-6 max-w-7xl">  </div>
        
        <!-- Header da página -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><?php echo $pageTitle; ?></h1>
                <p class="text-gray-600">Gestão de Contas a Pagar e Fornecedores</p>
            </div>
            <div class="flex space-x-2">
                <button onclick="showModalAdicionar()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Nova Conta a Pagar
                </button>
            </div>
        </div>

        <!-- Exibir mensagens -->
        <?php if (isset($_SESSION['msg_success'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['msg_success']; unset($_SESSION['msg_success']); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['msg_error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['msg_error']; unset($_SESSION['msg_error']); ?>
        </div>
        <?php endif; ?>

        <!-- Dashboard Content - Cards de Contas a Pagar -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Card Contas Pendentes -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
                <div class="p-3 rounded-full bg-yellow-100 mr-4">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 mb-1">Contas Pendentes</p>
                    <p class="text-xl font-bold text-gray-800">
                        <?php echo $contasPendentes['total'] ?? 0; ?>
                    </p>
                    <p class="text-sm text-gray-500">
                        R$ <?php echo number_format($contasPendentes['valor_total'] ?? 0, 2, ',', '.'); ?>
                    </p>
                </div>
            </div>

            <!-- Card Contas Pagas -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
                <div class="p-3 rounded-full bg-green-100 mr-4">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 mb-1">Contas Pagas</p>
                    <p class="text-xl font-bold text-gray-800">
                        <?php echo $contasPagas['total'] ?? 0; ?>
                    </p>
                    <p class="text-sm text-gray-500">
                        R$ <?php echo number_format($contasPagas['valor_total'] ?? 0, 2, ',', '.'); ?>
                    </p>
                </div>
            </div>

            <!-- Card Contas Vencidas -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
                <div class="p-3 rounded-full bg-red-100 mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 mb-1">Contas Vencidas</p>
                    <p class="text-xl font-bold text-gray-800">
                        <?php echo $contasVencidas['total'] ?? 0; ?>
                    </p>
                    <p class="text-sm text-gray-500">
                        R$ <?php echo number_format($contasVencidas['valor_total'] ?? 0, 2, ',', '.'); ?>
                    </p>
                </div>
            </div>

            <!-- Card Vencimento Hoje -->
            <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
                <div class="p-3 rounded-full bg-blue-100 mr-4">
                    <i class="fas fa-calendar-day text-blue-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 mb-1">Vencem Hoje</p>
                    <p class="text-xl font-bold text-gray-800">
                        <?php echo $contasHoje['total'] ?? 0; ?>
                    </p>
                    <p class="text-sm text-gray-500">
                        R$ <?php echo number_format($contasHoje['valor_total'] ?? 0, 2, ',', '.'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200 mb-6">
            <form method="GET" class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
                    <input type="text" name="busca" value="<?php echo htmlspecialchars($busca); ?>" 
                           placeholder="Descrição ou fornecedor..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="todos" <?php echo $filtro_status === 'todos' ? 'selected' : ''; ?>>Todos</option>
                        <option value="pendente" <?php echo $filtro_status === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                        <option value="pago" <?php echo $filtro_status === 'pago' ? 'selected' : ''; ?>>Pago</option>
                        <option value="cancelado" <?php echo $filtro_status === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Período</label>
                    <select name="data" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="todos" <?php echo $filtro_data === 'todos' ? 'selected' : ''; ?>>Todos</option>
                        <option value="hoje" <?php echo $filtro_data === 'hoje' ? 'selected' : ''; ?>>Hoje</option>
                        <option value="semana" <?php echo $filtro_data === 'semana' ? 'selected' : ''; ?>>Esta Semana</option>
                        <option value="mes" <?php echo $filtro_data === 'mes' ? 'selected' : ''; ?>>Este Mês</option>
                        <option value="vencidas" <?php echo $filtro_data === 'vencidas' ? 'selected' : ''; ?>>Vencidas</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filtrar
                </button>
                <a href="contas_pagar.php" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                    <i class="fas fa-times mr-2"></i>Limpar
                </a>
            </form>
        </div>

        <!-- Gráficos e Tabelas -->
        <div class="grid grid-cols-1 lg:grid-cols-7 gap-6 mb-8">
            <!-- Gráfico Evolução Mensal -->
            <div class="lg:col-span-4 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Evolução Mensal das Contas</h2>
                <div class="h-80">
                    <canvas id="chartEvolucao"></canvas>
                </div>
            </div>
            
            <!-- Categorias com Mais Gastos -->
            <div class="lg:col-span-3 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Categorias (Últimos 3 Meses)</h2>
                <?php if (empty($categoriasMaisGastos)): ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-chart-pie text-3xl mb-2"></i>
                    <p>Nenhum dado disponível</p>
                </div>
                <?php else: ?>
                <div class="space-y-3">
                    <?php foreach ($categoriasMaisGastos as $categoria): ?>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900"><?php echo htmlspecialchars($categoria['nome'] ?? 'Sem categoria'); ?></div>
                            <div class="text-sm text-gray-500"><?php echo $categoria['total_contas']; ?> contas</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">R$ <?php echo number_format($categoria['valor_total'], 2, ',', '.'); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Lista de Contas a Pagar -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">Lista de Contas a Pagar</h2>
            </div>
            
            <?php if (empty($contas)): ?>
            <div class="text-center py-12 text-gray-500">
                <i class="fas fa-file-invoice text-4xl mb-4"></i>
                <p class="text-lg">Nenhuma conta encontrada</p>
                <p>Clique em "Nova Conta a Pagar" para adicionar</p>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fornecedor</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categoria</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($contas as $conta): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($conta['descricao']); ?></div>
                                <?php if ($conta['observacoes']): ?>
                                <div class="text-sm text-gray-500"><?php echo htmlspecialchars($conta['observacoes']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($conta['fornecedor_nome'] ?? '-'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($conta['categoria_nome'] ?? '-'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php 
                                $vencimento = date('d/m/Y', strtotime($conta['data_vencimento']));
                                $hoje = date('Y-m-d');
                                $is_vencida = $conta['data_vencimento'] < $hoje && $conta['status'] === 'pendente';
                                $is_hoje = $conta['data_vencimento'] === $hoje;
                                ?>
                                <span class="<?php echo $is_vencida ? 'text-red-600 font-medium' : ($is_hoje ? 'text-blue-600 font-medium' : ''); ?>">
                                    <?php echo $vencimento; ?>
                                </span>
                                <?php if ($conta['data_pagamento']): ?>
                                <div class="text-xs text-gray-500">
                                    Pago em: <?php echo date('d/m/Y', strtotime($conta['data_pagamento'])); ?>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php 
                                $statusColors = [
                                    'pendente' => 'bg-yellow-100 text-yellow-800',
                                    'pago' => 'bg-green-100 text-green-800',
                                    'cancelado' => 'bg-red-100 text-red-800'
                                ];
                                $color = $statusColors[$conta['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $color; ?>">
                                    <?php echo ucfirst($conta['status']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <?php if ($conta['status'] === 'pendente'): ?>
                                <button onclick="showModalPagar(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['descricao']); ?>', <?php echo $conta['valor']; ?>)" 
                                        class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button onclick="showModalCancelar(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['descricao']); ?>')" 
                                        class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php endif; ?>
                                <button onclick="showModalExcluir(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['descricao']); ?>')" 
                                        class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    
    </div> <!-- Fim do container -->
</div> <!-- Fim do main-content -->

<!-- Modal Adicionar Conta -->
    <div id="modalAdicionar" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999]">
        <div class="flex items-center justify-center min-h-screen p-4 overflow-y-auto">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto my-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Nova Conta a Pagar</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="adicionar">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Descrição *</label>
                        <input type="text" name="descricao" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Valor *</label>
                        <input type="text" name="valor" required placeholder="0,00"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Data de Vencimento *</label>
                        <input type="date" name="data_vencimento" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Fornecedor</label>
                        <input type="text" name="fornecedor_nome" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
                        <select name="categoria_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">Selecione uma categoria</option>
                            <?php foreach ($categorias as $categoria): ?>
                            <option value="<?php echo $categoria['id']; ?>"><?php echo htmlspecialchars($categoria['nome']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                        <textarea name="observacoes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideModalAdicionar()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                            <i class="fas fa-plus mr-2"></i>Adicionar
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Pagar Conta -->
    <div id="modalPagar" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999]">
        <div class="flex items-center justify-center min-h-screen p-4 overflow-y-auto">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto my-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Marcar como Pago</h3>
                <form method="POST" id="formPagar">
                    <input type="hidden" name="action" value="pagar">
                    <input type="hidden" name="id" id="pagarId">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                        <p id="pagarDescricao" class="text-gray-900"></p>
                        <p id="pagarValor" class="text-gray-600 text-sm"></p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Data do Pagamento *</label>
                        <input type="date" name="data_pagamento" required value="<?php echo date('Y-m-d'); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Forma de Pagamento</label>
                        <select name="forma_pagamento" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="dinheiro">Dinheiro</option>
                            <option value="pix">PIX</option>
                            <option value="transferencia">Transferência Bancária</option>
                            <option value="cartao_debito">Cartão de Débito</option>
                            <option value="cartao_credito">Cartão de Crédito</option>
                            <option value="boleto">Boleto</option>
                            <option value="outros">Outros</option>
                        </select>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideModalPagar()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                            <i class="fas fa-check mr-2"></i>Confirmar Pagamento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Cancelar Conta -->
    <div id="modalCancelar" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999]">
        <div class="flex items-center justify-center min-h-screen p-4 overflow-y-auto">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto my-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cancelar Conta</h3>
                <form method="POST" id="formCancelar">
                    <input type="hidden" name="action" value="cancelar">
                    <input type="hidden" name="id" id="cancelarId">
                    
                    <p class="text-gray-600 mb-4">Tem certeza que deseja cancelar esta conta?</p>
                    <p id="cancelarDescricao" class="font-medium text-gray-900 mb-6"></p>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideModalCancelar()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            Não, manter
                        </button>
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                            <i class="fas fa-times mr-2"></i>Sim, cancelar
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Excluir Conta -->
    <div id="modalExcluir" class="fixed inset-0 bg-black bg-opacity-50 hidden z-[9999]">
        <div class="flex items-center justify-center min-h-screen p-4 overflow-y-auto">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto my-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Excluir Conta</h3>
                <form method="POST" id="formExcluir">
                    <input type="hidden" name="action" value="excluir">
                    <input type="hidden" name="id" id="excluirId">
                    
                    <p class="text-gray-600 mb-4">Tem certeza que deseja excluir esta conta? Esta ação não pode ser desfeita.</p>
                    <p id="excluirDescricao" class="font-medium text-gray-900 mb-6"></p>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideModalExcluir()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                            <i class="fas fa-trash mr-2"></i>Sim, excluir
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts para gráficos e interações -->
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Dados para o gráfico de evolução
    const periodos = <?php echo json_encode(array_column($evolucaoMensal ?? [], 'periodo')); ?>;
    const valores = <?php echo json_encode(array_column($evolucaoMensal ?? [], 'valor_total')); ?>;
    
    if (periodos.length > 0) {
        const ctx = document.getElementById('chartEvolucao').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: periodos,
                datasets: [{
                    label: 'Valor das Contas',
                    data: valores,
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'R$ ' + context.raw.toLocaleString('pt-BR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        }
                    }
                }
            }
        });
    }
});

// Funções dos modais
function showModalAdicionar() {
    document.getElementById('modalAdicionar').classList.remove('hidden');
}

function hideModalAdicionar() {
    document.getElementById('modalAdicionar').classList.add('hidden');
}

function showModalPagar(id, descricao, valor) {
    document.getElementById('pagarId').value = id;
    document.getElementById('pagarDescricao').textContent = descricao;
    document.getElementById('pagarValor').textContent = 'Valor: R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2});
    document.getElementById('modalPagar').classList.remove('hidden');
}

function hideModalPagar() {
    document.getElementById('modalPagar').classList.add('hidden');
}

function showModalCancelar(id, descricao) {
    document.getElementById('cancelarId').value = id;
    document.getElementById('cancelarDescricao').textContent = descricao;
    document.getElementById('modalCancelar').classList.remove('hidden');
}

function hideModalCancelar() {
    document.getElementById('modalCancelar').classList.add('hidden');
}

function showModalExcluir(id, descricao) {
    document.getElementById('excluirId').value = id;
    document.getElementById('excluirDescricao').textContent = descricao;
    document.getElementById('modalExcluir').classList.remove('hidden');
}

function hideModalExcluir() {
    document.getElementById('modalExcluir').classList.add('hidden');
}

// Fechar modais clicando fora
document.querySelectorAll('[id^="modal"]').forEach(modal => {
    modal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.classList.add('hidden');
        }
    });
});

// Máscara para valor monetário
document.querySelector('input[name="valor"]').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value) {
        value = (parseInt(value) / 100).toLocaleString('pt-BR', {minimumFractionDigits: 2});
        e.target.value = value;
    }
});
</script>

<?php include 'includes/footer_padronizado.php'; ?>