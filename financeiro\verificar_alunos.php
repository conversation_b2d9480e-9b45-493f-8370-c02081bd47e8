<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== ESTRUTURA DA TABELA ALUNOS ===\n";
    $estrutura = $db->fetchAll("DESCRIBE alunos");
    foreach ($estrutura as $campo) {
        echo "{$campo['Field']} - {$campo['Type']} - {$campo['Null']} - {$campo['Default']}\n";
    }
    
    echo "\n=== VERIFICANDO CAMPO STATUS ===\n";
    $statusCount = $db->fetchAll("SELECT status, COUNT(*) as total FROM alunos GROUP BY status");
    foreach ($statusCount as $status) {
        echo "Status '{$status['status']}': {$status['total']} alunos\n";
    }
    
} catch (Exception $e) {
    echo 'Erro: ' . $e->getMessage() . "\n";
}
?>
