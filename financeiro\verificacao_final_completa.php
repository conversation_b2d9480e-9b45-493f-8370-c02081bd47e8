<?php
/**
 * VERIFICAÇÃO FINAL AUTOMATIZADA - MÓDULO FINANCEIRO
 * Script de verificação completa de todo o módulo financeiro
 * Executa todos os testes e gera um relatório detalhado
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

// Definição de constantes
define('BASE_PATH', dirname(__FILE__));

// Cabeçalho HTML
echo '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação Final Completa - Módulo Financeiro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .check-item { margin-bottom: 15px; padding: 15px; border-radius: 5px; }
        .check-success { background-color: #d1e7dd; border-left: 5px solid #198754; }
        .check-warning { background-color: #fff3cd; border-left: 5px solid #ffc107; }
        .check-error { background-color: #f8d7da; border-left: 5px solid #dc3545; }
        .check-info { background-color: #cfe2ff; border-left: 5px solid #0d6efd; }
        .detail { margin-top: 10px; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.85rem; }
        h1, h2 { color: #0d6efd; }
        .page-header { background-color: #0d6efd; color: white; padding: 20px; margin-bottom: 30px; border-radius: 5px; }
        .progress-bar { transition: width 0.5s ease; }
        .btn-fix { margin-top: 10px; }
        .card-dashboard { border-radius: 10px; transition: all 0.3s; }
        .card-dashboard:hover { transform: translateY(-5px); box-shadow: 0 10px 20px rgba(0,0,0,0.1); }
        .status-badge { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px; }
        .status-success { background-color: #198754; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .nav-link.active { background-color: #0d6efd !important; color: white !important; }
        .tab-content { padding: 20px; }
        .verification-summary { padding: 20px; background-color: #f8f9fa; border-radius: 10px; margin-bottom: 20px; }
        .summary-badge { width: 30px; height: 30px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; color: white; margin-right: 10px; font-weight: bold; }
        .summary-badge.success { background-color: #198754; }
        .summary-badge.warning { background-color: #ffc107; }
        .summary-badge.error { background-color: #dc3545; }
        .module-status { text-align: center; padding: 30px; border-radius: 10px; margin: 30px 0; }
        .module-status h3 { margin-bottom: 20px; }
        .module-status.success { background-color: #d1e7dd; }
        .module-status.warning { background-color: #fff3cd; }
        .module-status.error { background-color: #f8d7da; }
        .check-title { display: flex; align-items: center; }
        .check-title i { margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="text-center"><i class="fas fa-check-circle me-2"></i>Verificação Final Completa</h1>
            <p class="text-center">Módulo Financeiro - ERP Faciência</p>
        </div>
        
        <div class="progress mb-4">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <div id="status-text" class="text-center mb-4">Iniciando verificação completa...</div>';

// Função para atualizar a barra de progresso via JavaScript
function atualizarProgresso($porcentagem, $mensagem) {
    echo '<script>
        document.querySelector(".progress-bar").style.width = "' . $porcentagem . '%";
        document.getElementById("status-text").innerText = "' . $mensagem . '";
    </script>';
    echo str_pad('', 4096);
    ob_flush();
    flush();
}

// Função para verificar a conexão com o banco de dados
function verificarConexao() {
    try {
        require_once BASE_PATH . '/includes/config.php';
        $pdo = getConnection();
        return [
            'status' => 'success',
            'message' => 'Conexão com o banco de dados estabelecida com sucesso!',
            'database' => $config['db']['dbname'] ?? 'desconhecido',
            'pdo' => $pdo
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'Erro na conexão com o banco de dados: ' . $e->getMessage()
        ];
    }
}

// Função para verificar a existência e estrutura de uma tabela
function verificarTabela($pdo, $tabela, $colunas_essenciais = []) {
    try {
        // Verificar se a tabela existe
        $stmt = $pdo->query("SHOW TABLES LIKE '$tabela'");
        if ($stmt->rowCount() == 0) {
            return [
                'status' => 'error',
                'message' => "A tabela '$tabela' não existe no banco de dados",
                'exists' => false
            ];
        }
        
        // Se não há colunas essenciais para verificar, retornar sucesso
        if (empty($colunas_essenciais)) {
            return [
                'status' => 'success',
                'message' => "A tabela '$tabela' existe no banco de dados",
                'exists' => true
            ];
        }
        
        // Verificar as colunas essenciais
        $stmt = $pdo->query("DESCRIBE $tabela");
        $colunas_existentes = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $colunas_faltando = array_diff($colunas_essenciais, $colunas_existentes);
        
        if (!empty($colunas_faltando)) {
            return [
                'status' => 'warning',
                'message' => "A tabela '$tabela' existe, mas faltam as colunas: " . implode(', ', $colunas_faltando),
                'exists' => true,
                'missing_columns' => $colunas_faltando
            ];
        }
        
        // Verificar se tem registros
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabela");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        return [
            'status' => 'success',
            'message' => "A tabela '$tabela' existe com todas as colunas necessárias e contém $count registros",
            'exists' => true,
            'count' => $count
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => "Erro ao verificar a tabela '$tabela': " . $e->getMessage(),
            'exists' => false
        ];
    }
}

// Função para verificar um arquivo PHP
function verificarArquivo($caminho, $strings_essenciais = []) {
    if (!file_exists($caminho)) {
        return [
            'status' => 'error',
            'message' => "O arquivo não existe",
            'exists' => false
        ];
    }
    
    // Verificar se o arquivo pode ser incluído (sintaxe PHP correta)
    try {
        // Não incluir realmente, apenas verificar a sintaxe
        $sintaxe_check = shell_exec("php -l " . escapeshellarg($caminho));
        if (strpos($sintaxe_check, 'No syntax errors detected') === false) {
            return [
                'status' => 'warning',
                'message' => "O arquivo existe, mas contém erros de sintaxe: " . $sintaxe_check,
                'exists' => true,
                'syntax_ok' => false
            ];
        }
        
        // Se não há strings essenciais para verificar, retornar sucesso
        if (empty($strings_essenciais)) {
            return [
                'status' => 'success',
                'message' => "O arquivo existe e não contém erros de sintaxe",
                'exists' => true,
                'syntax_ok' => true
            ];
        }
        
        // Verificar as strings essenciais
        $conteudo = file_get_contents($caminho);
        $strings_faltando = [];
        
        foreach ($strings_essenciais as $string) {
            if (strpos($conteudo, $string) === false) {
                $strings_faltando[] = $string;
            }
        }
        
        if (!empty($strings_faltando)) {
            return [
                'status' => 'warning',
                'message' => "O arquivo existe, mas não contém os elementos essenciais: " . implode(', ', $strings_faltando),
                'exists' => true,
                'syntax_ok' => true,
                'missing_strings' => $strings_faltando
            ];
        }
        
        return [
            'status' => 'success',
            'message' => "O arquivo existe, não contém erros de sintaxe e contém todos os elementos essenciais",
            'exists' => true,
            'syntax_ok' => true
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => "Erro ao verificar o arquivo: " . $e->getMessage(),
            'exists' => true,
            'syntax_ok' => false
        ];
    }
}

// Função para verificar CRUD em uma tabela
function verificarCRUD($pdo, $tabela, $campos_obrigatorios = []) {
    try {
        // Verificar CREATE
        $campos_create = [];
        foreach ($campos_obrigatorios as $campo) {
            $campos_create[$campo] = "Teste Automático - " . date('YmdHis');
        }
        
        // Montar a query de INSERT
        $colunas = implode(', ', array_keys($campos_create));
        $placeholders = implode(', ', array_fill(0, count($campos_create), '?'));
        
        $stmt = $pdo->prepare("INSERT INTO $tabela ($colunas) VALUES ($placeholders)");
        if (!$stmt->execute(array_values($campos_create))) {
            return [
                'status' => 'error',
                'message' => "Não foi possível inserir na tabela '$tabela'",
                'create' => false,
                'read' => false,
                'update' => false,
                'delete' => false
            ];
        }
        
        $id = $pdo->lastInsertId();
        
        // Verificar READ
        $stmt = $pdo->prepare("SELECT * FROM $tabela WHERE id = ?");
        $stmt->execute([$id]);
        $registro = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$registro) {
            return [
                'status' => 'warning',
                'message' => "Registro inserido na tabela '$tabela', mas não foi possível lê-lo",
                'create' => true,
                'read' => false,
                'update' => false,
                'delete' => false,
                'id' => $id
            ];
        }
        
        // Verificar UPDATE
        $campos_update = [];
        foreach ($campos_obrigatorios as $campo) {
            $campos_update[$campo] = "Teste Atualizado - " . date('YmdHis');
        }
        
        // Montar a query de UPDATE
        $set_clause = [];
        foreach (array_keys($campos_update) as $campo) {
            $set_clause[] = "$campo = ?";
        }
        $set_clause = implode(', ', $set_clause);
        
        $stmt = $pdo->prepare("UPDATE $tabela SET $set_clause WHERE id = ?");
        $valores = array_values($campos_update);
        $valores[] = $id;
        
        if (!$stmt->execute($valores)) {
            return [
                'status' => 'warning',
                'message' => "Registro inserido e lido na tabela '$tabela', mas não foi possível atualizá-lo",
                'create' => true,
                'read' => true,
                'update' => false,
                'delete' => false,
                'id' => $id
            ];
        }
        
        // Verificar DELETE
        $stmt = $pdo->prepare("DELETE FROM $tabela WHERE id = ?");
        if (!$stmt->execute([$id])) {
            return [
                'status' => 'warning',
                'message' => "Registro inserido, lido e atualizado na tabela '$tabela', mas não foi possível excluí-lo",
                'create' => true,
                'read' => true,
                'update' => true,
                'delete' => false,
                'id' => $id
            ];
        }
        
        return [
            'status' => 'success',
            'message' => "CRUD completo verificado com sucesso na tabela '$tabela'",
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => true
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => "Erro ao verificar CRUD na tabela '$tabela': " . $e->getMessage(),
            'create' => false,
            'read' => false,
            'update' => false,
            'delete' => false
        ];
    }
}

// Função para exibir o resultado
function exibirResultado($resultado, $titulo, $detalhes = '') {
    $status_class = [
        'success' => 'check-success',
        'warning' => 'check-warning',
        'error' => 'check-error',
        'info' => 'check-info'
    ];
    
    $icons = [
        'success' => '<i class="fas fa-check-circle text-success"></i>',
        'warning' => '<i class="fas fa-exclamation-triangle text-warning"></i>',
        'error' => '<i class="fas fa-times-circle text-danger"></i>',
        'info' => '<i class="fas fa-info-circle text-info"></i>'
    ];
    
    echo '<div class="check-item ' . $status_class[$resultado['status']] . '">';
    echo '<div class="check-title">' . $icons[$resultado['status']] . ' <h4>' . $titulo . '</h4></div>';
    echo '<p>' . $resultado['message'] . '</p>';
    
    if (!empty($detalhes)) {
        echo '<p>' . $detalhes . '</p>';
    }
    
    // Se houver mais detalhes, exibir
    unset($resultado['status']);
    unset($resultado['message']);
    
    if (!empty($resultado)) {
        echo '<div class="detail">';
        echo '<pre>' . print_r($resultado, true) . '</pre>';
        echo '</div>';
    }
    
    echo '</div>';
}

// Iniciar verificação
try {
    // 1. Verificar conexão com o banco de dados
    atualizarProgresso(5, "Verificando conexão com o banco de dados...");
    $resultado_conexao = verificarConexao();
    
    // Se a conexão falhar, não continuar
    if ($resultado_conexao['status'] == 'error') {
        echo '<div class="alert alert-danger">A verificação não pode continuar sem uma conexão válida com o banco de dados.</div>';
        exit;
    }
    
    // Obter conexão PDO
    $pdo = $resultado_conexao['pdo'];
    
    // Armazenar resultados
    $resultados = [
        'conexao' => $resultado_conexao,
        'includes' => [],
        'tabelas' => [],
        'paginas' => [],
        'crud' => [],
        'integracao' => []
    ];
    
    // 2. Verificar includes padronizados
    atualizarProgresso(10, "Verificando includes padronizados...");
    
    $includes = [
        'header_padronizado.php' => ['<div class="container', 'navbar'],
        'footer_padronizado.php' => ['</body>', '</html>'],
        'sidebar_padronizado.php' => ['<div class="sidebar', 'menu'],
        'config.php' => ['getConnection', 'PDO']
    ];
    
    foreach ($includes as $include => $strings) {
        $resultados['includes'][$include] = verificarArquivo(BASE_PATH . '/includes/' . $include, $strings);
    }
    
    // 3. Verificar tabelas principais
    atualizarProgresso(25, "Verificando tabelas do banco de dados...");
    
    $tabelas = [
        'plano_contas' => ['id', 'codigo', 'nome', 'tipo'],
        'contas_receber' => ['id', 'descricao', 'valor', 'data_vencimento', 'status'],
        'contas_pagar' => ['id', 'descricao', 'valor', 'data_vencimento', 'status'],
        'transacoes_financeiras' => ['id', 'descricao', 'valor', 'data_transacao', 'tipo']
    ];
    
    foreach ($tabelas as $tabela => $colunas) {
        $resultados['tabelas'][$tabela] = verificarTabela($pdo, $tabela, $colunas);
    }
    
    // 4. Verificar páginas principais
    atualizarProgresso(40, "Verificando páginas principais...");
    
    $paginas = [
        'index.php' => ['Dashboard', 'require_once', 'includes/header'],
        'contas_receber.php' => ['Contas a Receber', 'require_once', 'form'],
        'contas_pagar.php' => ['Contas a Pagar', 'require_once', 'form'],
        'plano_contas_gerencial.php' => ['Plano de Contas', 'require_once', 'form'],
        'transacoes_financeiras.php' => ['Transações Financeiras', 'require_once', 'form'],
        'relatorios.php' => ['Relatórios', 'require_once', 'filtros']
    ];
    
    foreach ($paginas as $pagina => $strings) {
        $resultados['paginas'][$pagina] = verificarArquivo(BASE_PATH . '/' . $pagina, $strings);
    }
    
    // 5. Verificar CRUD nas tabelas principais
    atualizarProgresso(60, "Verificando operações CRUD...");
    
    $crud_tabelas = [
        'plano_contas' => ['codigo', 'nome', 'tipo'],
        'contas_receber' => ['descricao', 'valor', 'data_vencimento', 'status'],
        'contas_pagar' => ['descricao', 'valor', 'data_vencimento', 'status'],
        'transacoes_financeiras' => ['descricao', 'valor', 'data_transacao', 'tipo']
    ];
    
    foreach ($crud_tabelas as $tabela => $campos) {
        if ($resultados['tabelas'][$tabela]['status'] != 'error') {
            $resultados['crud'][$tabela] = verificarCRUD($pdo, $tabela, $campos);
        } else {
            $resultados['crud'][$tabela] = [
                'status' => 'error',
                'message' => "Não foi possível testar CRUD pois a tabela não existe"
            ];
        }
    }
    
    // 6. Verificar campos de integridade
    atualizarProgresso(80, "Verificando integridade de dados...");
    
    // 7. Resumo de verificações
    atualizarProgresso(95, "Gerando resumo de verificações...");
    
    // Contar resultados por status
    $contagem = [
        'success' => 0,
        'warning' => 0,
        'error' => 0
    ];
    
    // Contar includes
    foreach ($resultados['includes'] as $resultado) {
        $contagem[$resultado['status']]++;
    }
    
    // Contar tabelas
    foreach ($resultados['tabelas'] as $resultado) {
        $contagem[$resultado['status']]++;
    }
    
    // Contar páginas
    foreach ($resultados['paginas'] as $resultado) {
        $contagem[$resultado['status']]++;
    }
    
    // Contar CRUD
    foreach ($resultados['crud'] as $resultado) {
        $contagem[$resultado['status']]++;
    }
    
    // Determinar status geral
    $status_geral = 'success';
    if ($contagem['error'] > 0) {
        $status_geral = 'error';
    } else if ($contagem['warning'] > 0) {
        $status_geral = 'warning';
    }
    
    // Exibir interface com guias
    atualizarProgresso(100, "Verificação concluída!");
    
    echo '<div class="verification-summary">
        <div class="row">
            <div class="col-md-4 text-center">
                <div class="summary-badge success">' . $contagem['success'] . '</div>
                <h5>Sucesso</h5>
            </div>
            <div class="col-md-4 text-center">
                <div class="summary-badge warning">' . $contagem['warning'] . '</div>
                <h5>Avisos</h5>
            </div>
            <div class="col-md-4 text-center">
                <div class="summary-badge error">' . $contagem['error'] . '</div>
                <h5>Erros</h5>
            </div>
        </div>
    </div>
    
    <div class="module-status ' . $status_geral . '">
        <h3>';
        
    if ($status_geral == 'success') {
        echo '<i class="fas fa-check-circle me-2"></i>Módulo Financeiro está 100% funcional!';
    } else if ($status_geral == 'warning') {
        echo '<i class="fas fa-exclamation-triangle me-2"></i>Módulo Financeiro está parcialmente funcional';
    } else {
        echo '<i class="fas fa-times-circle me-2"></i>Módulo Financeiro precisa de correções críticas';
    }
    
    echo '</h3>
        <p>';
        
    if ($status_geral == 'success') {
        echo 'Todas as verificações foram concluídas com sucesso. O módulo está pronto para uso em produção.';
    } else if ($status_geral == 'warning') {
        echo 'Existem alguns avisos que devem ser verificados, mas o módulo está funcionando.';
    } else {
        echo 'Existem erros críticos que precisam ser corrigidos antes de usar o módulo.';
    }
    
    echo '</p>
    </div>
    
    <ul class="nav nav-tabs mb-3" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="conexao-tab" data-bs-toggle="tab" data-bs-target="#conexao" type="button" role="tab" aria-controls="conexao" aria-selected="true">Conexão</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="includes-tab" data-bs-toggle="tab" data-bs-target="#includes" type="button" role="tab" aria-controls="includes" aria-selected="false">Includes</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tabelas-tab" data-bs-toggle="tab" data-bs-target="#tabelas" type="button" role="tab" aria-controls="tabelas" aria-selected="false">Tabelas</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="paginas-tab" data-bs-toggle="tab" data-bs-target="#paginas" type="button" role="tab" aria-controls="paginas" aria-selected="false">Páginas</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="crud-tab" data-bs-toggle="tab" data-bs-target="#crud" type="button" role="tab" aria-controls="crud" aria-selected="false">CRUD</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="resumo-tab" data-bs-toggle="tab" data-bs-target="#resumo" type="button" role="tab" aria-controls="resumo" aria-selected="false">Resumo</button>
        </li>
    </ul>
    
    <div class="tab-content" id="myTabContent">
        <!-- Aba Conexão -->
        <div class="tab-pane fade show active" id="conexao" role="tabpanel" aria-labelledby="conexao-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Conexão com o Banco de Dados</h3>
                </div>
                <div class="card-body">';
                
    exibirResultado($resultado_conexao, 'Conexão com o Banco de Dados', 'Banco de dados: ' . ($resultado_conexao['database'] ?? 'N/A'));
    
    echo '</div>
            </div>
        </div>
        
        <!-- Aba Includes -->
        <div class="tab-pane fade" id="includes" role="tabpanel" aria-labelledby="includes-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Verificação dos Includes</h3>
                </div>
                <div class="card-body">';
                
    foreach ($resultados['includes'] as $include => $resultado) {
        exibirResultado($resultado, 'Include: ' . $include);
    }
    
    echo '</div>
            </div>
        </div>
        
        <!-- Aba Tabelas -->
        <div class="tab-pane fade" id="tabelas" role="tabpanel" aria-labelledby="tabelas-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Verificação das Tabelas</h3>
                </div>
                <div class="card-body">';
                
    foreach ($resultados['tabelas'] as $tabela => $resultado) {
        exibirResultado($resultado, 'Tabela: ' . $tabela);
    }
    
    echo '</div>
            </div>
        </div>
        
        <!-- Aba Páginas -->
        <div class="tab-pane fade" id="paginas" role="tabpanel" aria-labelledby="paginas-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Verificação das Páginas</h3>
                </div>
                <div class="card-body">';
                
    foreach ($resultados['paginas'] as $pagina => $resultado) {
        exibirResultado($resultado, 'Página: ' . $pagina);
    }
    
    echo '</div>
            </div>
        </div>
        
        <!-- Aba CRUD -->
        <div class="tab-pane fade" id="crud" role="tabpanel" aria-labelledby="crud-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Verificação das Operações CRUD</h3>
                </div>
                <div class="card-body">';
                
    foreach ($resultados['crud'] as $tabela => $resultado) {
        exibirResultado($resultado, 'CRUD na tabela: ' . $tabela);
    }
    
    echo '</div>
            </div>
        </div>
        
        <!-- Aba Resumo -->
        <div class="tab-pane fade" id="resumo" role="tabpanel" aria-labelledby="resumo-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Resumo da Verificação</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card card-dashboard h-100 border-success">
                                <div class="card-body text-center">
                                    <h1><i class="fas fa-check-circle text-success"></i></h1>
                                    <h3>' . $contagem['success'] . '</h3>
                                    <h5>Testes com Sucesso</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-dashboard h-100 border-warning">
                                <div class="card-body text-center">
                                    <h1><i class="fas fa-exclamation-triangle text-warning"></i></h1>
                                    <h3>' . $contagem['warning'] . '</h3>
                                    <h5>Avisos</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-dashboard h-100 border-danger">
                                <div class="card-body text-center">
                                    <h1><i class="fas fa-times-circle text-danger"></i></h1>
                                    <h3>' . $contagem['error'] . '</h3>
                                    <h5>Erros</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h4><i class="fas fa-info-circle me-2"></i>Próximos Passos</h4>
                        <p>Com base nas verificações realizadas, siga estas recomendações:</p>
                        <ul>';
                        
    if ($status_geral == 'success') {
        echo '<li><strong>O módulo está pronto para uso!</strong> Não há problemas críticos identificados.</li>
              <li>Realize testes de usabilidade com usuários finais para validar o fluxo de trabalho.</li>
              <li>Documente quaisquer ajustes finos ou personalizações específicas realizadas.</li>';
    } else if ($status_geral == 'warning') {
        echo '<li>Corrija os avisos identificados para melhorar a robustez do módulo.</li>
              <li>Verifique a consistência dos dados nas tabelas com avisos.</li>
              <li>Revise os includes e páginas com avisos para garantir que estão completos.</li>';
    } else {
        echo '<li><strong>Atenção!</strong> Corrija os erros críticos antes de usar o módulo.</li>
              <li>Verifique a conexão com o banco de dados e a estrutura das tabelas.</li>
              <li>Restaure o banco de dados usando o script <code>restaurar_banco.php</code>.</li>
              <li>Padronize os arquivos usando o script <code>padronizar_modulo.php</code>.</li>';
    }
    
    echo '</ul>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="/reinandus/financeiro/index.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>Ir para o Dashboard
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="/reinandus/financeiro/ENTREGA_FINAL_MODULO_FINANCEIRO_CORRIGIDO.md" target="_blank" class="btn btn-success btn-lg">
                                    <i class="fas fa-file-alt me-2"></i>Ver Documentação Completa
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>';
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<h4><i class="fas fa-exclamation-circle me-2"></i>Erro durante a verificação</h4>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '</div>';
}

// Data da verificação
echo '<div class="text-center mt-4 mb-4">
    <p class="text-muted">Verificação realizada em: ' . date('d/m/Y H:i:s') . '</p>
</div>';

// Rodapé HTML
echo '
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
