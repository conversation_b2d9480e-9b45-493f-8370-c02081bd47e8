# Módulo Secretaria - Sistema Faciência ERP

## Visão Geral

O módulo Secretaria é responsável pela gestão acadêmica completa da instituição, incluindo controle de alunos, cursos, disciplinas, matr<PERSON><PERSON>s, notas, professores, documentos e relatórios.

## Estrutura de Pastas

### 📁 **core/** - <PERSON><PERSON><PERSON>los Principais do Sistema
Contém os arquivos PHP principais que implementam as funcionalidades centrais do sistema:

- **Gestão de Alunos**: `alunos.php`, `acessos_alunos.php`, `buscar_alunos.php`
- **Gestão de Cursos**: `cursos.php`, `desvincular_curso.php`
- **Gestão de Disciplinas**: `disciplinas.php`, `disciplinas_por_curso.php`, `disciplinas_simples.php`
- **Gestão de Matrículas**: `matriculas.php`
- **<PERSON>est<PERSON> de Notas**: `notas.php`, `notas_aluno.php`, `notas_turmas.php`
- **<PERSON><PERSON><PERSON> de Professor<PERSON>**: `professores.php`, `buscar_professores.php`
- **Gestão de Turmas**: `turmas.php`, `turmas_por_curso.php`, `turma_disciplinas.php`, `turmas_disciplinas.php`
- **Gestão de <PERSON>s**: `polos.php`, `polos_acesso.php`
- **Relatórios**: `relatorios.php`, `exportar_relatorio.php`
- **Histórico**: `historico.php`, `historicos.php`
- **Autenticação**: `login_alternativo.php`, `recuperar_senha.php`, `redefinir_senha.php`
- **Ferramentas**: `ferramentas.php`
- **Módulos da Secretaria**: `secretaria_*.php` (dashboard, estatísticas, etc.)

### 📁 **ava/** - Ambiente Virtual de Aprendizagem
Módulos de integração com o sistema AVA:

- `ava_alunos.php` - Gestão de alunos no AVA
- `ava_cursos.php` - Gestão de cursos no AVA
- `ava_dashboard_aluno.php` - Dashboard do aluno
- `ava_gerenciar_acesso.php` - Controle de acessos
- `ava_relatorios.php` - Relatórios do AVA
- `ava_visualizar_aula.php` - Visualização de aulas
- `ava_visualizar_curso.php` - Visualização de cursos

### 📁 **documentos/** - Sistema de Documentos
Gestão completa de documentos acadêmicos:

- `declaracoes.php` - Declarações diversas
- `boletim.php` - Boletins acadêmicos
- `documentos.php` - Gestão geral de documentos
- `documentos_aluno.php` - Documentos específicos do aluno
- `documentos_emitidos.php` - Controle de documentos emitidos
- `documentos_pessoais.php` - Documentos pessoais
- `declaracao_grade_curricular.php` - Declaração de grade curricular
- `solicitar_documentos.php` - Solicitação de documentos
- `visualizar_documento.php` - Visualização de documentos
- `ver_solicitacao.php` - Visualizar solicitações
- `responder_solicitacao.php` - Responder solicitações

### 📁 **chamados/** - Sistema de Chamados
Sistema completo de atendimento e suporte:

- `index.php` - Dashboard de chamados
- `novo.php` - Criar novo chamado
- `processar.php` - Processar chamados
- `visualizar.php` - Visualizar chamados
- `historico_emails.php` - Histórico de e-mails
- Outros arquivos de suporte ao sistema

### 📁 **ajax/** - Endpoints AJAX
Arquivos para requisições assíncronas:

- `buscar_alunos.php`, `buscar_cursos.php`, `buscar_professores.php`
- `gerar_documento.php`, `gerar_documentos_massa.php`
- `indicadores_rapidos.php`

### 📁 **api/** - API Endpoints
Interface de programação para integração:

- `alunos.php`, `cursos.php`, `disciplinas.php`
- `matriculas.php`, `turmas.php`, `usuarios.php`
- `documentos.php`, `relatorios.php`
- `dashboard.php`, `configuracoes.php`

### 📁 **assets/** - Recursos Estáticos
- **css/** - Folhas de estilo
- **js/** - Scripts JavaScript
- **images/** - Imagens (assinaturas, logos, etc.)

### 📁 **includes/** - Arquivos de Inclusão
Bibliotecas e funções compartilhadas:

- `init.php` - Inicialização do sistema
- `functions.php` - Funções gerais
- `Auth.php` - Autenticação
- `Database.php` - Conexão com banco de dados
- `DocumentGenerator.php` - Geração de documentos
- `header.php`, `footer.php`, `sidebar.php` - Layout

### 📁 **models/** - Classes de Modelo
Classes PHP para entidades do sistema:

- `Aluno.php`, `Curso.php`, `Disciplina.php`
- `Matricula.php`, `Polo.php`, `Documento.php`

### 📁 **views/** - Templates e Views
Templates organizados por módulo:

- `alunos/`, `cursos/`, `disciplinas/`
- `matriculas/`, `notas/`, `professores/`
- `documentos/`, `relatorios/`

### 📁 **config/** - Configurações
- `config.php` - Configurações gerais
- `database.php` - Configurações do banco

### 📁 **templates/** - Templates de Documentos
- `modelo_importacao_alunos.xlsx`

### 📁 **uploads/** - Arquivos Enviados
- `documentos/` - Documentos enviados
- `ava/` - Arquivos do AVA
- `temp/` - Arquivos temporários

### 📁 **vendor/** - Dependências do Composer
Bibliotecas PHP instaladas via Composer

### 📁 **scripts/** - Scripts Utilitários
Scripts para tarefas específicas

## Arquivos Principais na Raiz

- **index.php** - Dashboard principal da secretaria
- **login.php** - Sistema de login
- **logout.php** - Sistema de logout
- **chamados.php** - Acesso ao sistema de chamados

## Funcionalidades Principais

1. **Gestão Acadêmica Completa**
   - Controle de alunos, cursos, disciplinas
   - Matrículas e rematrículas
   - Lançamento e controle de notas
   - Gestão de turmas e professores

2. **Sistema de Documentos**
   - Geração automática de declarações
   - Boletins acadêmicos
   - Controle de documentos emitidos
   - Solicitação online de documentos

3. **Ambiente Virtual de Aprendizagem**
   - Integração com sistema AVA
   - Controle de acessos
   - Relatórios de desempenho

4. **Sistema de Chamados**
   - Atendimento ao aluno
   - Controle de solicitações
   - Histórico de atendimentos

5. **Relatórios e Estatísticas**
   - Relatórios acadêmicos
   - Estatísticas de desempenho
   - Exportação de dados

## Tecnologias Utilizadas

- **PHP** - Linguagem principal
- **MySQL** - Banco de dados
- **JavaScript/jQuery** - Frontend interativo
- **Bootstrap** - Framework CSS
- **TCPDF** - Geração de PDFs
- **Composer** - Gerenciador de dependências

## Observações Importantes

- Todos os arquivos de teste, debug e temporários foram removidos
- A estrutura foi reorganizada para facilitar manutenção e análise externa
- Funcionalidades existentes foram preservadas
- Código documentado e organizado seguindo boas práticas

## Suporte

Para dúvidas sobre funcionalidades específicas, consulte os comentários nos arquivos PHP ou entre em contato com a equipe de desenvolvimento.
