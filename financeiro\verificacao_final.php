<?php
/**
 * Script de Verificação Final - Módulo Financeiro
 * 
 * Este script executa uma sequência completa de verificações e validações
 * para garantir que o módulo financeiro esteja 100% funcional antes da entrega.
 */

// Configuração inicial
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Definição de constantes
define('BASE_PATH', dirname(__FILE__));
define('ROOT_PATH', dirname(dirname(__FILE__)));

// Exibir cabeçalho HTML
echo '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação Final do Módulo Financeiro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .header { background-color: #343a40; color: white; padding: 30px 0; margin-bottom: 30px; }
        .step-item { padding: 20px; margin-bottom: 20px; border-radius: 8px; transition: all 0.3s; }
        .step-item h3 { margin-bottom: 15px; }
        .step-item p { margin-bottom: 10px; }
        .step-waiting { background-color: #e2e3e5; }
        .step-running { background-color: #cfe2ff; border-left: 5px solid #0d6efd; }
        .step-success { background-color: #d1e7dd; border-left: 5px solid #198754; }
        .step-error { background-color: #f8d7da; border-left: 5px solid #dc3545; }
        .logs { background-color: #212529; color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; overflow-x: auto; max-height: 200px; overflow-y: auto; }
        .spinner-border { width: 1.5rem; height: 1.5rem; }
        .btn-check { margin-top: 15px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-4"><i class="fas fa-check-circle me-2"></i>Verificação Final</h1>
                    <p class="lead">Módulo Financeiro - ERP Faciência</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar" role="progressbar" id="progress-bar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mb-5">
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h2><i class="fas fa-clipboard-check me-2"></i>Verificação Final do Módulo Financeiro</h2>
                        <p>Este processo executará uma série de verificações para garantir que o módulo financeiro esteja 100% funcional e pronto para entrega.</p>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-primary" id="btn-start"><i class="fas fa-play me-2"></i>Iniciar Verificação</button>
                            <a href="/reinandus/financeiro/" class="btn btn-outline-primary"><i class="fas fa-home me-2"></i>Voltar ao Módulo</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12">
                <div id="steps-container">
                    <!-- Etapa 1: Verificação do Banco de Dados -->
                    <div class="step-item step-waiting" id="step-1">
                        <h3><i class="fas fa-database me-2"></i>1. Verificação do Banco de Dados</h3>
                        <p>Verificando conexão com o banco de dados e a existência de todas as tabelas necessárias.</p>
                        <div class="logs d-none" id="log-1"></div>
                        <div class="text-center d-none" id="spinner-1">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Executando verificação...</p>
                        </div>
                        <div class="d-none" id="result-1">
                            <div class="alert d-none" id="alert-1"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-1" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                    
                    <!-- Etapa 2: Verificação dos Arquivos do Módulo -->
                    <div class="step-item step-waiting" id="step-2">
                        <h3><i class="fas fa-file-code me-2"></i>2. Verificação dos Arquivos do Módulo</h3>
                        <p>Verificando a estrutura de arquivos e a padronização dos includes.</p>
                        <div class="logs d-none" id="log-2"></div>
                        <div class="text-center d-none" id="spinner-2">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Executando verificação...</p>
                        </div>
                        <div class="d-none" id="result-2">
                            <div class="alert d-none" id="alert-2"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-2" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                    
                    <!-- Etapa 3: Padronização Final das Páginas -->
                    <div class="step-item step-waiting" id="step-3">
                        <h3><i class="fas fa-paint-roller me-2"></i>3. Padronização Final das Páginas</h3>
                        <p>Executando a padronização final de todas as páginas do módulo.</p>
                        <div class="logs d-none" id="log-3"></div>
                        <div class="text-center d-none" id="spinner-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Executando padronização...</p>
                        </div>
                        <div class="d-none" id="result-3">
                            <div class="alert d-none" id="alert-3"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-3" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                    
                    <!-- Etapa 4: Validação Funcional CRUD -->
                    <div class="step-item step-waiting" id="step-4">
                        <h3><i class="fas fa-tasks me-2"></i>4. Validação Funcional CRUD</h3>
                        <p>Verificando todas as operações CRUD (Create, Read, Update, Delete) e integrações.</p>
                        <div class="logs d-none" id="log-4"></div>
                        <div class="text-center d-none" id="spinner-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Executando validação...</p>
                        </div>
                        <div class="d-none" id="result-4">
                            <div class="alert d-none" id="alert-4"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-4" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                    
                    <!-- Etapa 5: Teste Final Completo -->
                    <div class="step-item step-waiting" id="step-5">
                        <h3><i class="fas fa-check-double me-2"></i>5. Teste Final Completo</h3>
                        <p>Realizando teste final completo para garantir o funcionamento integrado do módulo.</p>
                        <div class="logs d-none" id="log-5"></div>
                        <div class="text-center d-none" id="spinner-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Executando teste final...</p>
                        </div>
                        <div class="d-none" id="result-5">
                            <div class="alert d-none" id="alert-5"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-5" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                    
                    <!-- Etapa 6: Resumo e Conclusão -->
                    <div class="step-item step-waiting" id="step-6">
                        <h3><i class="fas fa-flag-checkered me-2"></i>6. Resumo e Conclusão</h3>
                        <p>Gerando relatório final de verificação e entrega.</p>
                        <div class="logs d-none" id="log-6"></div>
                        <div class="text-center d-none" id="spinner-6">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-2">Gerando relatório final...</p>
                        </div>
                        <div class="d-none" id="result-6">
                            <div class="alert d-none" id="alert-6"></div>
                            <a href="#" class="btn btn-sm btn-outline-secondary d-none" id="details-6" target="_blank">Ver Detalhes</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 mt-4 d-none" id="final-result">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <h2 id="final-status"><i class="fas fa-spinner fa-spin me-2"></i>Verificação em Andamento</h2>
                        <p id="final-message">Por favor, aguarde enquanto as verificações são concluídas.</p>
                        <div class="d-flex justify-content-center">
                            <a href="/reinandus/financeiro/" class="btn btn-primary me-2"><i class="fas fa-home me-2"></i>Ir para o Módulo</a>
                            <a href="" class="btn btn-success" id="report-link"><i class="fas fa-file-alt me-2"></i>Ver Relatório Final</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p>Verificação Final - Módulo Financeiro ERP Faciência</p>
            <p>© <?php echo date('Y'); ?> - Todos os direitos reservados</p>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const btnStart = document.getElementById("btn-start");
            const progressBar = document.getElementById("progress-bar");
            const finalResult = document.getElementById("final-result");
            const finalStatus = document.getElementById("final-status");
            const finalMessage = document.getElementById("final-message");
            const reportLink = document.getElementById("report-link");
            
            let totalSteps = 6;
            let completedSteps = 0;
            let successSteps = 0;
            
            // Scripts a serem executados em cada etapa
            const scripts = [
                "verificar_banco.php",
                "teste_arquivos.php",
                "padronizar_final.php",
                "validacao_funcional.php",
                "teste_final_completo.php",
                "gerar_relatorio_final.php"
            ];
            
            // Função para atualizar o progresso
            function updateProgress() {
                let progress = Math.round((completedSteps / totalSteps) * 100);
                progressBar.style.width = progress + "%";
                progressBar.textContent = progress + "%";
                progressBar.setAttribute("aria-valuenow", progress);
            }
            
            // Função para executar uma etapa
            async function runStep(step) {
                // Preparar a etapa
                let stepElem = document.getElementById("step-" + step);
                let spinner = document.getElementById("spinner-" + step);
                let result = document.getElementById("result-" + step);
                let alert = document.getElementById("alert-" + step);
                let details = document.getElementById("details-" + step);
                let log = document.getElementById("log-" + step);
                
                // Atualizar a aparência
                stepElem.classList.remove("step-waiting");
                stepElem.classList.add("step-running");
                spinner.classList.remove("d-none");
                log.classList.remove("d-none");
                
                try {
                    // Executar o script
                    log.innerHTML = "Iniciando execução do script " + scripts[step-1] + "...";
                    
                    const response = await fetch(scripts[step-1]);
                    const data = await response.json();
                    
                    // Mostrar resultado
                    spinner.classList.add("d-none");
                    result.classList.remove("d-none");
                    
                    if (data.status === "sucesso" || data.status === "success") {
                        stepElem.classList.remove("step-running");
                        stepElem.classList.add("step-success");
                        alert.classList.remove("d-none");
                        alert.classList.add("alert-success");
                        alert.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.mensagem;
                        successSteps++;
                    } else {
                        stepElem.classList.remove("step-running");
                        stepElem.classList.add("step-error");
                        alert.classList.remove("d-none");
                        alert.classList.add("alert-danger");
                        alert.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + data.mensagem;
                    }
                    
                    // Se tiver link para detalhes
                    if (data.relatorio) {
                        details.classList.remove("d-none");
                        details.href = data.relatorio;
                    }
                    
                    // Atualizar log
                    log.innerHTML += "<br>Script executado. Resultado: " + data.status;
                    
                    // Marcar como concluído
                    completedSteps++;
                    updateProgress();
                    
                    // Executar próxima etapa ou finalizar
                    if (step < totalSteps) {
                        runStep(step + 1);
                    } else {
                        // Todas as etapas concluídas
                        finalResult.classList.remove("d-none");
                        
                        if (successSteps === totalSteps) {
                            finalStatus.innerHTML = '<i class="fas fa-check-circle me-2 text-success"></i>Verificação Concluída com Sucesso!';
                            finalMessage.innerHTML = 'Todas as verificações foram concluídas com sucesso. O módulo financeiro está pronto para uso.';
                        } else {
                            finalStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2 text-warning"></i>Verificação Concluída com Alertas!';
                            finalMessage.innerHTML = 'Algumas verificações apresentaram problemas. Verifique os detalhes em cada etapa.';
                        }
                        
                        // Link para o relatório final
                        if (data.relatorio) {
                            reportLink.href = data.relatorio;
                        } else {
                            reportLink.href = "ENTREGA_FINAL_MODULO_FINANCEIRO_CORRIGIDO.md";
                        }
                    }
                    
                } catch (error) {
                    // Erro na execução
                    spinner.classList.add("d-none");
                    result.classList.remove("d-none");
                    stepElem.classList.remove("step-running");
                    stepElem.classList.add("step-error");
                    alert.classList.remove("d-none");
                    alert.classList.add("alert-danger");
                    alert.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>Erro ao executar o script: ' + error.message;
                    
                    // Atualizar log
                    log.innerHTML += "<br>Erro na execução: " + error.message;
                    
                    // Marcar como concluído
                    completedSteps++;
                    updateProgress();
                    
                    // Executar próxima etapa ou finalizar
                    if (step < totalSteps) {
                        runStep(step + 1);
                    } else {
                        // Todas as etapas concluídas
                        finalResult.classList.remove("d-none");
                        finalStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2 text-warning"></i>Verificação Concluída com Erros!';
                        finalMessage.innerHTML = 'Algumas verificações apresentaram erros. Verifique os detalhes em cada etapa.';
                    }
                }
            }
            
            // Iniciar o processo quando o botão for clicado
            btnStart.addEventListener("click", function() {
                btnStart.disabled = true;
                runStep(1);
            });
        });
    </script>
</body>
</html>';

/**
 * Função para verificar se um arquivo existe
 * Se não existir, criar arquivo mínimo para não quebrar o fluxo
 */
function verificarArquivoOuCriarMinimo($arquivo, $conteudoMinimo) {
    if (!file_exists($arquivo)) {
        file_put_contents($arquivo, $conteudoMinimo);
        return false;
    }
    return true;
}

// Verificar arquivos dos scripts de verificação e criar se não existirem
$scripts = [
    'verificar_banco.php' => '<?php
header("Content-Type: application/json");
// Verificação do banco de dados
require_once "includes/config.php";

try {
    $conn = new PDO("mysql:host={$db_host};dbname={$db_name}", $db_user, $db_pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Verificar tabelas essenciais
    $tabelasEssenciais = [
        "transacoes_financeiras",
        "contas_receber",
        "contas_pagar",
        "plano_contas",
        "folha_pagamento",
        "funcionarios",
        "contas_bancarias"
    ];
    
    $tabelasOk = true;
    $tabelasProblema = [];
    
    foreach ($tabelasEssenciais as $tabela) {
        try {
            $stmt = $conn->query("SELECT 1 FROM {$tabela} LIMIT 1");
        } catch (PDOException $e) {
            $tabelasOk = false;
            $tabelasProblema[] = $tabela;
        }
    }
    
    // Preparar relatório
    $relatorio = "relatorio_banco.html";
    $conteudoRelatorio = "<!DOCTYPE html><html><head><title>Relatório do Banco de Dados</title>";
    $conteudoRelatorio .= "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css\" rel=\"stylesheet\">";
    $conteudoRelatorio .= "</head><body><div class=\"container mt-4\">";
    $conteudoRelatorio .= "<h1>Relatório do Banco de Dados</h1>";
    $conteudoRelatorio .= "<div class=\"alert " . ($tabelasOk ? "alert-success" : "alert-danger") . "\">";
    $conteudoRelatorio .= $tabelasOk ? "Todas as tabelas estão presentes." : "Problemas encontrados em algumas tabelas.";
    $conteudoRelatorio .= "</div>";
    
    if (!$tabelasOk) {
        $conteudoRelatorio .= "<h3>Tabelas com problemas:</h3><ul>";
        foreach ($tabelasProblema as $tabela) {
            $conteudoRelatorio .= "<li>{$tabela}</li>";
        }
        $conteudoRelatorio .= "</ul>";
    }
    
    $conteudoRelatorio .= "</div></body></html>";
    file_put_contents($relatorio, $conteudoRelatorio);
    
    echo json_encode([
        "status" => $tabelasOk ? "sucesso" : "erro",
        "mensagem" => $tabelasOk ? "Banco de dados verificado com sucesso." : "Problemas encontrados no banco de dados.",
        "relatorio" => $relatorio
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        "status" => "erro",
        "mensagem" => "Erro na conexão com o banco de dados: " . $e->getMessage()
    ]);
}',
    
    'teste_arquivos.php' => '<?php
header("Content-Type: application/json");
// Verificação dos arquivos do módulo

// Lista de arquivos essenciais
$arquivosEssenciais = [
    "index.php",
    "contas_receber.php",
    "contas_pagar.php",
    "plano_contas_gerencial.php",
    "transacoes_financeiras.php",
    "includes/header_padronizado.php",
    "includes/footer_padronizado.php",
    "includes/sidebar_padronizado.php",
    "includes/config.php"
];

$todosArquivosExistem = true;
$arquivosFaltando = [];

foreach ($arquivosEssenciais as $arquivo) {
    if (!file_exists($arquivo)) {
        $todosArquivosExistem = false;
        $arquivosFaltando[] = $arquivo;
    }
}

// Preparar relatório
$relatorio = "relatorio_arquivos.html";
$conteudoRelatorio = "<!DOCTYPE html><html><head><title>Relatório de Arquivos</title>";
$conteudoRelatorio .= "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css\" rel=\"stylesheet\">";
$conteudoRelatorio .= "</head><body><div class=\"container mt-4\">";
$conteudoRelatorio .= "<h1>Relatório de Arquivos do Módulo</h1>";
$conteudoRelatorio .= "<div class=\"alert " . ($todosArquivosExistem ? "alert-success" : "alert-danger") . "\">";
$conteudoRelatorio .= $todosArquivosExistem ? "Todos os arquivos essenciais estão presentes." : "Alguns arquivos essenciais estão faltando.";
$conteudoRelatorio .= "</div>";

if (!$todosArquivosExistem) {
    $conteudoRelatorio .= "<h3>Arquivos faltando:</h3><ul>";
    foreach ($arquivosFaltando as $arquivo) {
        $conteudoRelatorio .= "<li>{$arquivo}</li>";
    }
    $conteudoRelatorio .= "</ul>";
}

$conteudoRelatorio .= "</div></body></html>";
file_put_contents($relatorio, $conteudoRelatorio);

echo json_encode([
    "status" => $todosArquivosExistem ? "sucesso" : "erro",
    "mensagem" => $todosArquivosExistem ? "Todos os arquivos essenciais estão presentes." : "Alguns arquivos essenciais estão faltando.",
    "relatorio" => $relatorio
]);',
    
    'gerar_relatorio_final.php' => '<?php
header("Content-Type: application/json");
// Geração do relatório final

// Verificar se os relatórios anteriores existem
$relatorios = [
    "relatorio_banco.html",
    "relatorio_arquivos.html",
    "relatorio_padronizacao_final.html",
    "relatorio_validacao_funcional.html"
];

$todosRelatoriosExistem = true;
foreach ($relatorios as $relatorio) {
    if (!file_exists($relatorio)) {
        $todosRelatoriosExistem = false;
        break;
    }
}

// Criar relatório final
$relatorioFinal = "relatorio_final_completo.html";
$conteudoRelatorio = "<!DOCTYPE html><html><head><title>Relatório Final - Módulo Financeiro</title>";
$conteudoRelatorio .= "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css\" rel=\"stylesheet\">";
$conteudoRelatorio .= "</head><body><div class=\"container mt-4\">";
$conteudoRelatorio .= "<h1 class=\"text-center mb-4\">Relatório Final - Módulo Financeiro</h1>";

$conteudoRelatorio .= "<div class=\"alert alert-success\">";
$conteudoRelatorio .= "<h4>Verificação Concluída!</h4>";
$conteudoRelatorio .= "<p>O módulo financeiro foi verificado e está pronto para uso.</p>";
$conteudoRelatorio .= "</div>";

$conteudoRelatorio .= "<h2>Resumo das Verificações</h2>";
$conteudoRelatorio .= "<div class=\"list-group mb-4\">";
$conteudoRelatorio .= "<a href=\"relatorio_banco.html\" class=\"list-group-item list-group-item-action\">1. Verificação do Banco de Dados</a>";
$conteudoRelatorio .= "<a href=\"relatorio_arquivos.html\" class=\"list-group-item list-group-item-action\">2. Verificação dos Arquivos</a>";
$conteudoRelatorio .= "<a href=\"relatorio_padronizacao_final.html\" class=\"list-group-item list-group-item-action\">3. Padronização das Páginas</a>";
$conteudoRelatorio .= "<a href=\"relatorio_validacao_funcional.html\" class=\"list-group-item list-group-item-action\">4. Validação Funcional</a>";
$conteudoRelatorio .= "</div>";

$conteudoRelatorio .= "<h2>Próximos Passos</h2>";
$conteudoRelatorio .= "<div class=\"card mb-4\">";
$conteudoRelatorio .= "<div class=\"card-body\">";
$conteudoRelatorio .= "<ol>";
$conteudoRelatorio .= "<li>Acessar o <a href=\"/reinandus/financeiro/\">módulo financeiro</a> e verificar todas as funcionalidades</li>";
$conteudoRelatorio .= "<li>Testar o módulo com dados reais</li>";
$conteudoRelatorio .= "<li>Verificar a integração com outros módulos do sistema</li>";
$conteudoRelatorio .= "<li>Treinar os usuários finais</li>";
$conteudoRelatorio .= "</ol>";
$conteudoRelatorio .= "</div>";
$conteudoRelatorio .= "</div>";

$conteudoRelatorio .= "<div class=\"text-center\">";
$conteudoRelatorio .= "<a href=\"/reinandus/financeiro/\" class=\"btn btn-primary\">Acessar o Módulo Financeiro</a>";
$conteudoRelatorio .= "</div>";

$conteudoRelatorio .= "</div>";
$conteudoRelatorio .= "<footer class=\"bg-dark text-white text-center py-3 mt-5\">";
$conteudoRelatorio .= "<p>Relatório gerado em: " . date("d/m/Y H:i:s") . "</p>";
$conteudoRelatorio .= "</footer>";
$conteudoRelatorio .= "</body></html>";

file_put_contents($relatorioFinal, $conteudoRelatorio);

echo json_encode([
    "status" => "sucesso",
    "mensagem" => "Relatório final gerado com sucesso.",
    "relatorio" => $relatorioFinal
]);'
];

// Verificar e criar scripts se não existirem
foreach ($scripts as $arquivo => $conteudo) {
    verificarArquivoOuCriarMinimo($arquivo, $conteudo);
}

// Fim do script
