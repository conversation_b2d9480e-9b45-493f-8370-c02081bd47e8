<?php
/**
 * RESTAURAR BANCO DE DADOS ALTERNATIVO
 * Cria e configura o banco de dados para o módulo financeiro
 * Versão com conexão MySQL simplificada
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1 style='color: #dc3545;'>🔧 RESTAURAÇÃO DO BANCO DE DADOS - VERSÃO ALTERNATIVA</h1>";

$banco = 'u682219090_faciencia_erp';

// Criar banco
try {
    // Conectar sem especificar banco de dados
    $mysqli = new mysqli('localhost', 'root', '');
    
    if ($mysqli->connect_error) {
        die("<p style='color: red;'>✗ Erro ao conectar ao MySQL: " . $mysqli->connect_error . "</p>");
    }
    
    // Tentar criar o banco de dados
    if ($mysqli->query("CREATE DATABASE IF NOT EXISTS $banco")) {
        echo "<p style='color: green;'>✓ Banco '$banco' criado ou já existente</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao criar banco: " . $mysqli->error . "</p>";
    }
    
    // Conectar ao banco criado
    $mysqli->select_db($banco);
    
    // Criar tabelas
    $sql_tabelas = "
    CREATE TABLE IF NOT EXISTS plano_contas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo VARCHAR(20),
        nome VARCHAR(255) NOT NULL,
        tipo ENUM('ATIVO', 'PASSIVO', 'RECEITA', 'DESPESA') NOT NULL,
        nivel INT DEFAULT 1,
        pai_id INT NULL,
        ativo TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS contas_receber (
        id INT AUTO_INCREMENT PRIMARY KEY,
        descricao VARCHAR(255) NOT NULL,
        cliente_nome VARCHAR(255),
        valor DECIMAL(10,2) NOT NULL,
        data_vencimento DATE NOT NULL,
        data_recebimento DATE NULL,
        status ENUM('pendente', 'recebido', 'cancelado') DEFAULT 'pendente',
        observacoes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS contas_pagar (
        id INT AUTO_INCREMENT PRIMARY KEY,
        descricao VARCHAR(255) NOT NULL,
        fornecedor_nome VARCHAR(255),
        valor DECIMAL(10,2) NOT NULL,
        data_vencimento DATE NOT NULL,
        data_pagamento DATE NULL,
        status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
        observacoes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS transacoes_financeiras (
        id INT AUTO_INCREMENT PRIMARY KEY,
        descricao VARCHAR(255) NOT NULL,
        tipo ENUM('receita', 'despesa') NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        data_transacao DATE NOT NULL,
        conta_bancaria_id INT NULL,
        centro_custo_id INT NULL,
        observacoes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS contas_bancarias (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        banco VARCHAR(100),
        agencia VARCHAR(20),
        conta VARCHAR(30),
        saldo_atual DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('ativa', 'inativa') DEFAULT 'ativa',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS centros_custo (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo VARCHAR(20),
        nome VARCHAR(255) NOT NULL,
        descricao TEXT,
        ativo TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ";
    
    $statements = explode(';', $sql_tabelas);
    foreach ($statements as $stmt) {
        $stmt = trim($stmt);
        if (!empty($stmt)) {
            if ($mysqli->query($stmt)) {
                echo "<p style='color: green;'>✓ Executado: " . substr($stmt, 0, 50) . "...</p>";
            } else {
                echo "<p style='color: red;'>✗ Erro: " . $mysqli->error . "</p>";
            }
        }
    }
    
    // Inserir dados de exemplo
    echo "<h2>📊 Inserindo Dados de Exemplo</h2>";
    
    // Plano de contas
    $mysqli->query("INSERT IGNORE INTO plano_contas (id, codigo, nome, tipo, nivel) VALUES 
        (1, '1', 'ATIVO', 'ATIVO', 1),
        (2, '1.1', 'ATIVO CIRCULANTE', 'ATIVO', 2),
        (3, '1.1.1', 'Caixa e Bancos', 'ATIVO', 3),
        (4, '2', 'PASSIVO', 'PASSIVO', 1),
        (5, '3', 'RECEITAS', 'RECEITA', 1),
        (6, '4', 'DESPESAS', 'DESPESA', 1)");
    
    // Contas bancárias
    $mysqli->query("INSERT IGNORE INTO contas_bancarias (id, nome, banco, saldo_atual) VALUES 
        (1, 'Conta Corrente Principal', 'Banco do Brasil', 15000.00),
        (2, 'Conta Poupança', 'Caixa Econômica', 8500.00)");
    
    // Dados de exemplo para contas a receber
    $mysqli->query("INSERT IGNORE INTO contas_receber (descricao, cliente_nome, valor, data_vencimento, status) VALUES 
        ('Serviços de Consultoria - Janeiro', 'Empresa ABC Ltda', 2500.00, '" . date('Y-m-d', strtotime('+5 days')) . "', 'pendente'),
        ('Desenvolvimento de Sistema', 'Tech Solutions', 4800.00, '" . date('Y-m-d', strtotime('+15 days')) . "', 'pendente'),
        ('Manutenção Mensal', 'Comércio XYZ', 800.00, '" . date('Y-m-d', strtotime('+3 days')) . "', 'pendente'),
        ('Projeto Finalizado', 'Indústria 123', 3200.00, '" . date('Y-m-d', strtotime('-2 days')) . "', 'recebido')");
    
    // Dados de exemplo para contas a pagar
    $mysqli->query("INSERT IGNORE INTO contas_pagar (descricao, fornecedor_nome, valor, data_vencimento, status) VALUES 
        ('Aluguel do Escritório', 'Imobiliária Central', 1800.00, '" . date('Y-m-d', strtotime('+10 days')) . "', 'pendente'),
        ('Energia Elétrica', 'Companhia de Energia', 450.00, '" . date('Y-m-d', strtotime('+7 days')) . "', 'pendente'),
        ('Internet e Telefone', 'Telecom Brasil', 320.00, '" . date('Y-m-d', strtotime('+12 days')) . "', 'pendente'),
        ('Material de Escritório', 'Papelaria Moderna', 180.00, '" . date('Y-m-d', strtotime('-1 day')) . "', 'pago')");
    
    // Transações financeiras
    $mysqli->query("INSERT IGNORE INTO transacoes_financeiras (descricao, tipo, valor, data_transacao) VALUES 
        ('Recebimento Cliente ABC', 'receita', 2500.00, '" . date('Y-m-d') . "'),
        ('Pagamento Fornecedor', 'despesa', 1200.00, '" . date('Y-m-d', strtotime('-1 day')) . "'),
        ('Venda de Serviços', 'receita', 1800.00, '" . date('Y-m-d', strtotime('-2 days')) . "'),
        ('Despesas Administrativas', 'despesa', 650.00, '" . date('Y-m-d', strtotime('-3 days')) . "')");
    
    echo "<p style='color: green;'>✓ Dados de exemplo inseridos com sucesso</p>";
    
    // Atualizar arquivo de configuração
    $config_novo = '<?php
/**
 * Configuração central do módulo financeiro
 */

// Configuração do banco de dados
$config = [
    "db" => [
        "host" => "localhost",
        "dbname" => "' . $banco . '", // Banco correto detectado automaticamente
        "username" => "root",
        "password" => "",
        "charset" => "utf8"
    ]
];

// Função para conectar ao banco
function getConnection() {
    global $config;
    try {
        $dsn = "mysql:host={$config[\'db\'][\'host\']};dbname={$config[\'db\'][\'dbname\']}";
        $pdo = new PDO($dsn, $config["db"]["username"], $config["db"]["password"], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        return $pdo;
    } catch (PDOException $e) {
        die("Erro de conexão: " . $e->getMessage());
    }
}

// Funções helper
function fetchData($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Erro SQL: " . $e->getMessage());
        return [];
    }
}

function fetchValue($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        return $result ? array_values($result)[0] : 0;
    } catch (Exception $e) {
        error_log("Erro SQL: " . $e->getMessage());
        return 0;
    }
}

function executeQuery($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);
    } catch (Exception $e) {
        error_log("Erro SQL: " . $e->getMessage());
        return false;
    }
}

// Função para formatar valores monetários
function formatMoney($value) {
    return "R$ " . number_format($value, 2, ",", ".");
}

// Função para formatar datas
function formatDate($date) {
    return date("d/m/Y", strtotime($date));
}

// Inicializar sessão
if (session_status() === PHP_SESSION_NONE) {
    @session_start();
}

// Suprimir warnings
error_reporting(E_ERROR | E_PARSE);
ini_set("display_errors", 1);
?>';

    file_put_contents("c:\\xampp\\htdocs\\reinandus\\financeiro\\includes\\config.php", $config_novo);
    echo "<p style='color: green;'>✓ Arquivo config.php atualizado</p>";
    
    echo "<h2>✅ RESTAURAÇÃO CONCLUÍDA!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>🎉 Banco de dados restaurado com sucesso!</h3>";
    echo "<ul>";
    echo "<li>✓ Banco: <strong>$banco</strong></li>";
    echo "<li>✓ Tabelas criadas</li>";
    echo "<li>✓ Configuração atualizada</li>";
    echo "<li>✓ Dados de exemplo inseridos</li>";
    echo "</ul>";
    echo "</div>";

    echo "<p><strong>🔗 Testar agora:</strong></p>";
    echo "<ul>";
    echo "<li><a href='http://localhost/reinandus/financeiro/verificacao_final_alternativa.php' target='_blank' style='color: #007bff;'>Verificação Final Alternativa</a></li>";
    echo "<li><a href='http://localhost/reinandus/financeiro/index.php' target='_blank' style='color: #007bff;'>Dashboard</a></li>";
    echo "</ul>";

    echo "<h3 style='color: #28a745;'>🚀 BANCO RESTAURADO - MÓDULO PRONTO PARA USO!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erro geral: " . $e->getMessage() . "</p>";
}
?>
