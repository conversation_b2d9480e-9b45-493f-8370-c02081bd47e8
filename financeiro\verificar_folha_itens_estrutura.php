<?php
/**
 * Verificar estrutura da tabela folha_pagamento_itens
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';

$db = Database::getInstance();

echo "=== ESTRUTURA DA TABELA FOLHA_PAGAMENTO_ITENS ===\n\n";

try {
    $structure = $db->fetchAll("DESCRIBE folha_pagamento_itens");
    
    echo "📋 CAMPOS DA TABELA:\n";
    foreach ($structure as $field) {
        echo "  - " . $field['Field'] . " (" . $field['Type'] . ")" . 
             ($field['Null'] == 'NO' ? ' NOT NULL' : '') . 
             ($field['Key'] == 'PRI' ? ' PRIMARY KEY' : '') . 
             ($field['Default'] ? ' DEFAULT ' . $field['Default'] : '') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
}

echo "\n============================================================\n";
?>
