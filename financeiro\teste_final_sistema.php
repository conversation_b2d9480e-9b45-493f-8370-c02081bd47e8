<?php
/**
 * Teste final para validar funcionamento do módulo financeiro
 * Especificamente: folha_pagamento.php?action=gerar e mensalidades.php (busca e geração)
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Teste Final - Módulo Financeiro</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔍 Teste Final - Módulo Financeiro</h1>";
echo "<p>Validando funcionamento das páginas corrigidas...</p>";

$db = Database::getInstance();
$erros = [];
$sucessos = [];

try {
    // Teste 1: Verificar se mensalidades.php carrega sem erros de sintaxe
    echo "<h2>📋 Teste 1: Validação de Sintaxe - mensalidades.php</h2>";
    
    $sintaxeMensalidades = shell_exec('c:\xampp\php\php.exe -l "' . __DIR__ . '/mensalidades.php" 2>&1');
    if (strpos($sintaxeMensalidades, 'No syntax errors') !== false) {
        echo "<div class='success'>✅ mensalidades.php: Sem erros de sintaxe</div>";
        $sucessos[] = "Sintaxe mensalidades.php válida";
    } else {
        echo "<div class='error'>❌ mensalidades.php: Erro de sintaxe - $sintaxeMensalidades</div>";
        $erros[] = "Erro de sintaxe em mensalidades.php";
    }

    // Teste 2: Verificar se folha_pagamento.php carrega sem erros de sintaxe
    echo "<h2>💰 Teste 2: Validação de Sintaxe - folha_pagamento.php</h2>";
    
    $sintaxeFolha = shell_exec('c:\xampp\php\php.exe -l "' . __DIR__ . '/folha_pagamento.php" 2>&1');
    if (strpos($sintaxeFolha, 'No syntax errors') !== false) {
        echo "<div class='success'>✅ folha_pagamento.php: Sem erros de sintaxe</div>";
        $sucessos[] = "Sintaxe folha_pagamento.php válida";
    } else {
        echo "<div class='error'>❌ folha_pagamento.php: Erro de sintaxe - $sintaxeFolha</div>";
        $erros[] = "Erro de sintaxe em folha_pagamento.php";
    }

    // Teste 3: Verificar busca de funcionários para folha de pagamento
    echo "<h2>👥 Teste 3: Busca de Funcionários (Folha de Pagamento)</h2>";
      $funcionarios = $db->fetchAll("
        SELECT id, nome, cpf, salario, cargo
        FROM funcionarios
        WHERE status = 'ativo'
        ORDER BY nome
        LIMIT 5
    ");
    
    if (count($funcionarios) > 0) {
        echo "<div class='success'>✅ Encontrados " . count($funcionarios) . " funcionário(s) ativo(s)</div>";
        foreach ($funcionarios as $func) {
            echo "<div class='info'>- {$func['nome']} (CPF: {$func['cpf']}, Salário: R$ " . number_format($func['salario'], 2, ',', '.') . ")</div>";
        }
        $sucessos[] = "Busca de funcionários funcional";
    } else {
        echo "<div class='error'>❌ Nenhum funcionário ativo encontrado</div>";
        $erros[] = "Nenhum funcionário encontrado para folha de pagamento";
    }

    // Teste 4: Verificar busca de alunos para mensalidades
    echo "<h2>🎓 Teste 4: Busca de Alunos (Mensalidades)</h2>";
    
    $alunos = $db->fetchAll("
        SELECT a.id, a.nome, a.cpf, a.email, c.nome as curso_nome 
        FROM alunos a 
        LEFT JOIN cursos c ON a.curso_id = c.id 
        WHERE a.status = 'ativo' 
        ORDER BY a.nome 
        LIMIT 5
    ");
    
    if (count($alunos) > 0) {
        echo "<div class='success'>✅ Encontrados " . count($alunos) . " aluno(s) ativo(s)</div>";
        foreach ($alunos as $aluno) {
            echo "<div class='info'>- {$aluno['nome']} (CPF: {$aluno['cpf']}, Curso: " . ($aluno['curso_nome'] ?: 'N/A') . ")</div>";
        }
        $sucessos[] = "Busca de alunos funcional";
    } else {
        echo "<div class='error'>❌ Nenhum aluno ativo encontrado</div>";
        $erros[] = "Nenhum aluno encontrado para mensalidades";
    }

    // Teste 5: Verificar busca de cursos
    echo "<h2>📚 Teste 5: Busca de Cursos</h2>";
    
    $cursos = $db->fetchAll("
        SELECT id, nome, status 
        FROM cursos 
        WHERE status = 'ativo' 
        ORDER BY nome 
        LIMIT 5
    ");
    
    if (count($cursos) > 0) {
        echo "<div class='success'>✅ Encontrados " . count($cursos) . " curso(s) ativo(s)</div>";
        foreach ($cursos as $curso) {
            echo "<div class='info'>- {$curso['nome']} (Status: {$curso['status']})</div>";
        }
        $sucessos[] = "Busca de cursos funcional";
    } else {
        echo "<div class='error'>❌ Nenhum curso ativo encontrado</div>";
        $erros[] = "Nenhum curso encontrado";
    }

    // Teste 6: Verificar estrutura da tabela mensalidades_alunos
    echo "<h2>🗄️ Teste 6: Estrutura da Tabela mensalidades_alunos</h2>";
    
    $estruturaMensalidades = $db->fetchAll("DESCRIBE mensalidades_alunos");
    if (count($estruturaMensalidades) > 0) {
        echo "<div class='success'>✅ Tabela mensalidades_alunos encontrada com " . count($estruturaMensalidades) . " coluna(s)</div>";
        
        $colunasEssenciais = ['id', 'aluno_id', 'valor', 'data_vencimento', 'status'];
        $colunasEncontradas = array_column($estruturaMensalidades, 'Field');
        
        foreach ($colunasEssenciais as $coluna) {
            if (in_array($coluna, $colunasEncontradas)) {
                echo "<div class='info'>- ✅ Coluna '$coluna' presente</div>";
            } else {
                echo "<div class='error'>- ❌ Coluna '$coluna' ausente</div>";
                $erros[] = "Coluna $coluna ausente em mensalidades_alunos";
            }
        }
        $sucessos[] = "Estrutura mensalidades_alunos validada";
    } else {
        echo "<div class='error'>❌ Tabela mensalidades_alunos não encontrada</div>";
        $erros[] = "Tabela mensalidades_alunos inexistente";
    }

    // Teste 7: Verificar estrutura da tabela folha_pagamento
    echo "<h2>💼 Teste 7: Estrutura da Tabela folha_pagamento</h2>";
    
    $estruturaFolha = $db->fetchAll("DESCRIBE folha_pagamento");
    if (count($estruturaFolha) > 0) {
        echo "<div class='success'>✅ Tabela folha_pagamento encontrada com " . count($estruturaFolha) . " coluna(s)</div>";
        
        $colunasEssenciais = ['id', 'mes_referencia', 'ano_referencia', 'valor_total', 'status'];
        $colunasEncontradas = array_column($estruturaFolha, 'Field');
        
        foreach ($colunasEssenciais as $coluna) {
            if (in_array($coluna, $colunasEncontradas)) {
                echo "<div class='info'>- ✅ Coluna '$coluna' presente</div>";
            } else {
                echo "<div class='error'>- ❌ Coluna '$coluna' ausente</div>";
                $erros[] = "Coluna $coluna ausente em folha_pagamento";
            }
        }
        $sucessos[] = "Estrutura folha_pagamento validada";
    } else {
        echo "<div class='error'>❌ Tabela folha_pagamento não encontrada</div>";
        $erros[] = "Tabela folha_pagamento inexistente";
    }

    // Teste 7a: Verificar estrutura da tabela folha_pagamento_itens
    echo "<h2>💼 Teste 7a: Estrutura da Tabela folha_pagamento_itens</h2>";
    
    $estruturaItens = $db->fetchAll("DESCRIBE folha_pagamento_itens");
    if (count($estruturaItens) > 0) {
        echo "<div class='success'>✅ Tabela folha_pagamento_itens encontrada com " . count($estruturaItens) . " coluna(s)</div>";
        
        $colunasEssenciaisItens = ['id', 'folha_id', 'funcionario_id', 'salario_base', 'valor_liquido'];
        $colunasEncontradasItens = array_column($estruturaItens, 'Field');
        
        foreach ($colunasEssenciaisItens as $coluna) {
            if (in_array($coluna, $colunasEncontradasItens)) {
                echo "<div class='info'>- ✅ Coluna '$coluna' presente</div>";
            } else {
                echo "<div class='error'>- ❌ Coluna '$coluna' ausente</div>";
                $erros[] = "Coluna $coluna ausente em folha_pagamento_itens";
            }
        }
        $sucessos[] = "Estrutura folha_pagamento_itens validada";
    } else {
        echo "<div class='error'>❌ Tabela folha_pagamento_itens não encontrada</div>";
        $erros[] = "Tabela folha_pagamento_itens inexistente";
    }

    // Teste 8: Simular geração de folha de pagamento
    echo "<h2>🎯 Teste 8: Simulação de Geração de Folha de Pagamento</h2>";
    
    if (count($funcionarios) > 0) {
        $funcionarioTeste = $funcionarios[0];
        $mesAtual = date('n'); // mês atual sem zero à esquerda
        $anoAtual = date('Y');
        
        // Verifica se já existe folha para este mês/ano
        $folhaExistente = $db->fetchOne("
            SELECT id FROM folha_pagamento 
            WHERE mes_referencia = ? AND ano_referencia = ?
        ", [$mesAtual, $anoAtual]);
        
        if (!$folhaExistente) {
            echo "<div class='success'>✅ Simulação OK: Poderia gerar folha para mês $mesAtual/$anoAtual</div>";
            $sucessos[] = "Simulação de geração de folha funcional";
        } else {
            // Verifica se o funcionário já tem item na folha
            $itemExistente = $db->fetchOne("
                SELECT id FROM folha_pagamento_itens 
                WHERE folha_id = ? AND funcionario_id = ?
            ", [$folhaExistente['id'], $funcionarioTeste['id']]);
            
            if (!$itemExistente) {
                echo "<div class='success'>✅ Simulação OK: Poderia adicionar {$funcionarioTeste['nome']} à folha existente</div>";
                $sucessos[] = "Simulação de geração de folha funcional";
            } else {
                echo "<div class='info'>ℹ️ {$funcionarioTeste['nome']} já possui folha no mês $mesAtual/$anoAtual</div>";
                $sucessos[] = "Simulação de geração de folha funcional";
            }
        }
    } else {
        echo "<div class='error'>❌ Não foi possível simular - sem funcionários</div>";
        $erros[] = "Simulação de folha impossível";
    }

    // Teste 9: Simular geração de mensalidade
    echo "<h2>🎯 Teste 9: Simulação de Geração de Mensalidade</h2>";
    
    if (count($alunos) > 0) {
        $alunoTeste = $alunos[0];
        $mesReferencia = date('Y-m-01');
        
        // Verifica se já existe mensalidade para este aluno neste mês
        $mensalidadeExistente = $db->fetchOne("
            SELECT id FROM mensalidades_alunos 
            WHERE aluno_id = ? AND mes_referencia = ?
        ", [$alunoTeste['id'], $mesReferencia]);
        
        if (!$mensalidadeExistente) {
            echo "<div class='success'>✅ Simulação OK: Poderia gerar mensalidade para {$alunoTeste['nome']} (mês $mesReferencia)</div>";
            $sucessos[] = "Simulação de geração de mensalidade funcional";
        } else {
            echo "<div class='info'>ℹ️ Mensalidade já existe para {$alunoTeste['nome']} no mês $mesReferencia</div>";
            $sucessos[] = "Verificação de duplicidade funcional";
        }
    } else {
        echo "<div class='error'>❌ Não foi possível simular - sem alunos</div>";
        $erros[] = "Simulação de mensalidade impossível";
    }

    // Teste 10: Verificar dependências e includes
    echo "<h2>🔗 Teste 10: Verificação de Dependências</h2>";
    
    $arquivosEssenciais = [
        '../includes/init.php',
        '../includes/Database.php', 
        '../includes/Auth.php',
        'includes/header.php',
        'includes/footer.php',
        'includes/sidebar.php'
    ];
    
    foreach ($arquivosEssenciais as $arquivo) {
        $caminhoCompleto = __DIR__ . '/' . $arquivo;
        if (file_exists($caminhoCompleto)) {
            echo "<div class='success'>✅ $arquivo existe</div>";
        } else {
            echo "<div class='error'>❌ $arquivo não encontrado</div>";
            $erros[] = "Arquivo $arquivo ausente";
        }
    }
    
    if (count($erros) === 0) {
        $sucessos[] = "Todas as dependências encontradas";
    }

} catch (Exception $e) {
    echo "<div class='error'>❌ Erro durante os testes: " . $e->getMessage() . "</div>";
    $erros[] = "Erro geral: " . $e->getMessage();
}

// Relatório final
echo "<h2>📊 Relatório Final</h2>";
echo "<div style='background:#f0f0f0;padding:20px;border-radius:5px;'>";

if (count($erros) === 0) {
    echo "<div class='success'><h3>🎉 TODOS OS TESTES PASSARAM!</h3>";
    echo "<p>O módulo financeiro está funcionando corretamente.</p>";
    echo "<p><strong>Sucessos (" . count($sucessos) . "):</strong></p>";
    echo "<ul>";
    foreach ($sucessos as $sucesso) {
        echo "<li>✅ $sucesso</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='error'><h3>⚠️ ALGUNS PROBLEMAS ENCONTRADOS</h3>";
    echo "<p><strong>Erros (" . count($erros) . "):</strong></p>";
    echo "<ul>";
    foreach ($erros as $erro) {
        echo "<li>❌ $erro</li>";
    }
    echo "</ul>";
    
    if (count($sucessos) > 0) {
        echo "<p><strong>Sucessos (" . count($sucessos) . "):</strong></p>";
        echo "<ul>";
        foreach ($sucessos as $sucesso) {
            echo "<li>✅ $sucesso</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

echo "<h3>🔗 Links para Teste Manual</h3>";
echo "<p>Para testar as funcionalidades manualmente:</p>";
echo "<ul>";
echo "<li><a href='mensalidades.php' target='_blank'>📋 Testar Mensalidades</a></li>";
echo "<li><a href='folha_pagamento.php' target='_blank'>💰 Testar Folha de Pagamento</a></li>";
echo "<li><a href='folha_pagamento.php?action=gerar' target='_blank'>🎯 Testar Geração de Folha</a></li>";
echo "<li><a href='index.php' target='_blank'>🏠 Dashboard Financeiro</a></li>";
echo "</ul>";

echo "</div>";

echo "<p><small>Teste executado em: " . date('d/m/Y H:i:s') . "</small></p>";
echo "</body></html>";
?>
