<?php
/**
 * Relatórios Financeiros Avançados
 * Sistema completo de relatórios analíticos e sincronizados
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once 'includes/SaldoManager.php';
require_once 'includes/RelatorioManager.php';

// Verifica autenticação e permissão
Auth::requireLogin();
$userType = Auth::getUserType();
if (!in_array($userType, ['financeiro', 'admin_master'])) {
    $_SESSION['error'] = 'Você não tem permissão para acessar o módulo financeiro.';
    header('Location: ../index.php');
    exit;
}

$db = Database::getInstance();
$saldoManager = new SaldoManager($db);
$relatorioManager = new RelatorioManager($db, $saldoManager);

// Parâmetros
$relatorio = $_GET['relatorio'] ?? 'dashboard';
$dataInicio = $_GET['data_inicio'] ?? date('Y-m-01');
$dataFim = $_GET['data_fim'] ?? date('Y-m-t');
$status = $_GET['status'] ?? 'todos';
$exportar = $_GET['exportar'] ?? '';

// Processa exportação
if ($exportar) {
    switch ($relatorio) {
        case 'receitas_despesas':
            $dados = $relatorioManager->getReceitasDespesas($dataInicio, $dataFim, true);
            $relatorioManager->exportarRelatorio('receitas_despesas', $dados, 'csv');
            break;
        case 'contas_pagar':
            $dados = $relatorioManager->getRelatorioContasPagar($dataInicio, $dataFim, $status);
            $relatorioManager->exportarRelatorio('contas_pagar', $dados, 'csv');
            break;
        case 'contas_receber':
            $dados = $relatorioManager->getRelatorioContasReceber($dataInicio, $dataFim, $status);
            $relatorioManager->exportarRelatorio('contas_receber', $dados, 'csv');
            break;
    }
    exit;
}

// Carrega dados do relatório
$dados = [];
$erro = null;

try {
    // Atualiza saldos antes de gerar relatórios
    $saldoManager->atualizarTodosSaldos();
    
    switch ($relatorio) {
        case 'receitas_despesas':
            $dados = $relatorioManager->getReceitasDespesas($dataInicio, $dataFim, true);
            break;
        case 'contas_pagar':
            $dados = $relatorioManager->getRelatorioContasPagar($dataInicio, $dataFim, $status);
            break;
        case 'contas_receber':
            $dados = $relatorioManager->getRelatorioContasReceber($dataInicio, $dataFim, $status);
            break;
        case 'contas_bancarias':
            $dados = $relatorioManager->getAnaliseContasBancarias($dataInicio, $dataFim);
            break;
        default:
            // Dashboard dos relatórios
            $dados = [
                'resumo_financeiro' => $saldoManager->getResumoFinanceiro($dataInicio, $dataFim),
                'receitas_despesas' => $relatorioManager->getReceitasDespesas($dataInicio, $dataFim, false),
                'validacao' => $saldoManager->validarConsistencia()
            ];
    }
} catch (Exception $e) {
    $erro = $e->getMessage();
    error_log("Erro nos relatórios: " . $e->getMessage());
}

$pageTitle = 'Relatórios Financeiros';
?>
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Faciência ERP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/financeiro.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col ml-64">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>
            
            <!-- Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    
                    <!-- Mensagens de Erro -->
                    <?php if ($erro): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Erro ao gerar relatório: <?php echo htmlspecialchars($erro); ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Header da página -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Relatórios Financeiros</h1>
                        <p class="text-gray-600 mt-2">Análises detalhadas e sincronizadas do sistema financeiro</p>
                    </div>

                    <!-- Filtros e Controles -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <form method="GET" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Relatório</label>
                                    <select name="relatorio" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="dashboard" <?php echo $relatorio === 'dashboard' ? 'selected' : ''; ?>>Dashboard Geral</option>
                                        <option value="receitas_despesas" <?php echo $relatorio === 'receitas_despesas' ? 'selected' : ''; ?>>Receitas x Despesas</option>
                                        <option value="contas_pagar" <?php echo $relatorio === 'contas_pagar' ? 'selected' : ''; ?>>Contas a Pagar</option>
                                        <option value="contas_receber" <?php echo $relatorio === 'contas_receber' ? 'selected' : ''; ?>>Contas a Receber</option>
                                        <option value="contas_bancarias" <?php echo $relatorio === 'contas_bancarias' ? 'selected' : ''; ?>>Análise Contas Bancárias</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Data Início</label>
                                    <input type="date" name="data_inicio" value="<?php echo $dataInicio; ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Data Fim</label>
                                    <input type="date" name="data_fim" value="<?php echo $dataFim; ?>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                <?php if (in_array($relatorio, ['contas_pagar', 'contas_receber'])): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="todos" <?php echo $status === 'todos' ? 'selected' : ''; ?>>Todos</option>
                                        <option value="pendente" <?php echo $status === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                                        <option value="<?php echo $relatorio === 'contas_pagar' ? 'pago' : 'recebido'; ?>" 
                                                <?php echo $status === ($relatorio === 'contas_pagar' ? 'pago' : 'recebido') ? 'selected' : ''; ?>>
                                            <?php echo $relatorio === 'contas_pagar' ? 'Pago' : 'Recebido'; ?>
                                        </option>
                                        <option value="cancelado" <?php echo $status === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                                    </select>
                                </div>
                                <?php endif; ?>
                                <div class="flex items-end space-x-2">
                                    <button type="submit" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        <i class="fas fa-chart-bar mr-2"></i>Gerar
                                    </button>
                                    <?php if ($relatorio !== 'dashboard' && !empty($dados)): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['exportar' => 'csv'])); ?>" 
                                       class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>

                    <?php if ($relatorio === 'dashboard'): ?>
                        <!-- Dashboard dos Relatórios -->
                        <?php if (isset($dados['resumo_financeiro'])): ?>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-university text-blue-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Saldo Total</p>
                                        <p class="text-2xl font-semibold text-blue-600">
                                            R$ <?php echo number_format($dados['resumo_financeiro']['saldo_total_contas'], 2, ',', '.'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-arrow-up text-green-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Receitas do Período</p>
                                        <p class="text-2xl font-semibold text-green-600">
                                            R$ <?php echo number_format($dados['resumo_financeiro']['receitas_periodo'], 2, ',', '.'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-arrow-down text-red-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Despesas do Período</p>
                                        <p class="text-2xl font-semibold text-red-600">
                                            R$ <?php echo number_format($dados['resumo_financeiro']['despesas_periodo'], 2, ',', '.'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 <?php echo $dados['resumo_financeiro']['resultado_periodo'] >= 0 ? 'bg-green-100' : 'bg-red-100'; ?> rounded-lg flex items-center justify-center">
                                        <i class="fas fa-chart-line <?php echo $dados['resumo_financeiro']['resultado_periodo'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Resultado</p>
                                        <p class="text-2xl font-semibold <?php echo $dados['resumo_financeiro']['resultado_periodo'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                            R$ <?php echo number_format($dados['resumo_financeiro']['resultado_periodo'], 2, ',', '.'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gráficos do Dashboard -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                            <!-- Gráfico de Receitas vs Despesas -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Receitas vs Despesas</h3>
                                <canvas id="receitasDespesasChart" width="400" height="200"></canvas>
                            </div>
                            
                            <!-- Gráfico por Categoria -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Por Categoria</h3>
                                <canvas id="categoriasChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Validação do Sistema -->
                        <?php if (!$dados['validacao']['consistente']): ?>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Atenção: Inconsistências Detectadas</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <ul class="list-disc list-inside">
                                            <?php foreach ($dados['validacao']['inconsistencias'] as $inconsistencia): ?>
                                                <li><?php echo htmlspecialchars($inconsistencia); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                    <div class="mt-4">
                                        <a href="reconciliacao.php" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200">
                                            Corrigir Inconsistências
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>

                    <?php elseif ($relatorio === 'receitas_despesas' && !empty($dados)): ?>
                        <!-- Relatório de Receitas e Despesas -->
                        <div class="space-y-6">
                            <!-- Resumo -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Resumo do Período</h3>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-600">R$ <?php echo number_format($dados['resumo']['total_receitas'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Total Receitas (<?php echo $dados['resumo']['qtd_receitas']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-red-600">R$ <?php echo number_format($dados['resumo']['total_despesas'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Total Despesas (<?php echo $dados['resumo']['qtd_despesas']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold <?php echo $dados['resumo']['resultado'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                            R$ <?php echo number_format($dados['resumo']['resultado'], 2, ',', '.'); ?>
                                        </div>
                                        <div class="text-sm text-gray-600">Resultado</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600"><?php echo number_format($dados['resumo']['margem'], 1); ?>%</div>
                                        <div class="text-sm text-gray-600">Margem</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Por Categoria -->
                            <?php if (!empty($dados['por_categoria'])): ?>
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Por Categoria</h3>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quantidade</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Média</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($dados['por_categoria'] as $categoria): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($categoria['categoria']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $categoria['tipo'] === 'receita' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                        <?php echo ucfirst($categoria['tipo']); ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo number_format($categoria['quantidade']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium <?php echo $categoria['tipo'] === 'receita' ? 'text-green-600' : 'text-red-600'; ?>">
                                                    R$ <?php echo number_format($categoria['total'], 2, ',', '.'); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    R$ <?php echo number_format($categoria['media'], 2, ',', '.'); ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Detalhes (se solicitado) -->
                            <?php if (!empty($dados['detalhes'])): ?>
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Detalhes das Transações</h3>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Data</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descrição</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Origem</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach (array_slice($dados['detalhes'], 0, 50) as $detalhe): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo date('d/m/Y', strtotime($detalhe['data_mov'])); ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($detalhe['descricao']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $detalhe['tipo'] === 'receita' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                        <?php echo ucfirst($detalhe['tipo']); ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium <?php echo $detalhe['tipo'] === 'receita' ? 'text-green-600' : 'text-red-600'; ?>">
                                                    R$ <?php echo number_format($detalhe['valor'], 2, ',', '.'); ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($detalhe['categoria']); ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($detalhe['origem']); ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php if (count($dados['detalhes']) > 50): ?>
                                <div class="px-6 py-3 bg-gray-50 text-center text-sm text-gray-500">
                                    Mostrando 50 de <?php echo count($dados['detalhes']); ?> transações. 
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['exportar' => 'csv'])); ?>" class="text-blue-600 hover:text-blue-800">
                                        Baixar relatório completo
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                    <?php elseif ($relatorio === 'contas_pagar' && !empty($dados)): ?>
                        <!-- Relatório de Contas a Pagar -->
                        <div class="space-y-6">
                            <!-- Resumo de Contas a Pagar -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Resumo - Contas a Pagar</h3>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-red-600">R$ <?php echo number_format($dados['resumo']['total_pendente'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Pendente (<?php echo $dados['resumo']['qtd_pendente']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-600">R$ <?php echo number_format($dados['resumo']['total_pago'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Pago (<?php echo $dados['resumo']['qtd_pago']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-yellow-600">R$ <?php echo number_format($dados['resumo']['total_vencido'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Vencido (<?php echo $dados['resumo']['qtd_vencido']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600">R$ <?php echo number_format($dados['resumo']['total_geral'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Total (<?php echo $dados['resumo']['qtd_total']; ?>)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Lista de Contas -->
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Detalhes das Contas</h3>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descrição</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Vencimento</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Dias</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($dados['contas'] as $conta): ?>
                                            <tr class="<?php echo $conta['dias_vencimento'] > 0 ? 'bg-red-50' : ''; ?>">
                                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($conta['descricao']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                                                    R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <?php 
                                                    $statusClasses = [
                                                        'pendente' => 'bg-yellow-100 text-yellow-800',
                                                        'pago' => 'bg-green-100 text-green-800',
                                                        'cancelado' => 'bg-gray-100 text-gray-800'
                                                    ];
                                                    ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusClasses[$conta['status']]; ?>">
                                                        <?php echo ucfirst($conta['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($conta['categoria']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo $conta['dias_vencimento'] > 0 ? 'text-red-600 font-medium' : 'text-gray-900'; ?>">
                                                    <?php 
                                                    if ($conta['dias_vencimento'] > 0) {
                                                        echo $conta['dias_vencimento'] . ' dias vencido';
                                                    } elseif ($conta['dias_vencimento'] < 0) {
                                                        echo abs($conta['dias_vencimento']) . ' dias';
                                                    } else {
                                                        echo 'Vence hoje';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    <?php elseif ($relatorio === 'contas_receber' && !empty($dados)): ?>
                        <!-- Relatório de Contas a Receber -->
                        <div class="space-y-6">
                            <!-- Resumo de Contas a Receber -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Resumo - Contas a Receber</h3>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-yellow-600">R$ <?php echo number_format($dados['resumo']['total_pendente'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Pendente (<?php echo $dados['resumo']['qtd_pendente']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-600">R$ <?php echo number_format($dados['resumo']['total_recebido'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Recebido (<?php echo $dados['resumo']['qtd_recebido']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-red-600">R$ <?php echo number_format($dados['resumo']['total_vencido'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Vencido (<?php echo $dados['resumo']['qtd_vencido']; ?>)</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600">R$ <?php echo number_format($dados['resumo']['total_geral'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Total (<?php echo $dados['resumo']['qtd_total']; ?>)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Lista de Contas -->
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Detalhes das Contas</h3>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cliente/Descrição</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Vencimento</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Dias</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($dados['contas'] as $conta): ?>
                                            <tr class="<?php echo $conta['dias_vencimento'] > 0 ? 'bg-red-50' : ''; ?>">
                                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($conta['descricao']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                                    R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <?php 
                                                    $statusClasses = [
                                                        'pendente' => 'bg-yellow-100 text-yellow-800',
                                                        'recebido' => 'bg-green-100 text-green-800',
                                                        'cancelado' => 'bg-gray-100 text-gray-800'
                                                    ];
                                                    ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusClasses[$conta['status']]; ?>">
                                                        <?php echo ucfirst($conta['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($conta['categoria']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo $conta['dias_vencimento'] > 0 ? 'text-red-600 font-medium' : 'text-gray-900'; ?>">
                                                    <?php 
                                                    if ($conta['dias_vencimento'] > 0) {
                                                        echo $conta['dias_vencimento'] . ' dias vencido';
                                                    } elseif ($conta['dias_vencimento'] < 0) {
                                                        echo abs($conta['dias_vencimento']) . ' dias';
                                                    } else {
                                                        echo 'Vence hoje';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    <?php elseif ($relatorio === 'contas_bancarias' && !empty($dados)): ?>
                        <!-- Análise de Contas Bancárias -->
                        <div class="space-y-6">
                            <!-- Resumo das Contas -->
                            <div class="bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise das Contas Bancárias</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600">R$ <?php echo number_format($dados['resumo']['saldo_total'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Saldo Total</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-600">R$ <?php echo number_format($dados['resumo']['entradas'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Entradas no Período</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-red-600">R$ <?php echo number_format($dados['resumo']['saidas'], 2, ',', '.'); ?></div>
                                        <div class="text-sm text-gray-600">Saídas no Período</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Detalhes por Conta -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <?php foreach ($dados['contas'] as $conta): ?>
                                <div class="bg-white rounded-lg shadow p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($conta['nome']); ?></h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $conta['tipo'] === 'corrente' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'; ?>">
                                            <?php echo ucfirst($conta['tipo']); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="space-y-3">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Saldo Atual:</span>
                                            <span class="text-sm font-medium <?php echo $conta['saldo_atual'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                R$ <?php echo number_format($conta['saldo_atual'], 2, ',', '.'); ?>
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Entradas:</span>
                                            <span class="text-sm font-medium text-green-600">
                                                R$ <?php echo number_format($conta['entradas'], 2, ',', '.'); ?>
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Saídas:</span>
                                            <span class="text-sm font-medium text-red-600">
                                                R$ <?php echo number_format($conta['saidas'], 2, ',', '.'); ?>
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Movimentações:</span>
                                            <span class="text-sm text-gray-900"><?php echo $conta['total_movimentacoes']; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                    <?php elseif (empty($dados) && $relatorio !== 'dashboard'): ?>
                        <!-- Nenhum dado encontrado -->
                        <div class="text-center py-12">
                            <i class="fas fa-chart-line text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum dado encontrado</h3>
                            <p class="text-gray-600">Não há dados para o período e filtros selecionados.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts para gráficos -->
    <script>
        // Gráficos do Dashboard
        <?php if ($relatorio === 'dashboard' && isset($dados['receitas_despesas'])): ?>
        // Dados para os gráficos
        const receitasDespesasData = <?php echo json_encode($dados['receitas_despesas']); ?>;
        
        // Gráfico Receitas vs Despesas
        const ctxReceitas = document.getElementById('receitasDespesasChart').getContext('2d');
        new Chart(ctxReceitas, {
            type: 'bar',
            data: {
                labels: receitasDespesasData.evolucao_diaria ? 
                    receitasDespesasData.evolucao_diaria.map(item => item.data) : [],
                datasets: [{
                    label: 'Receitas',
                    data: receitasDespesasData.evolucao_diaria ? 
                        receitasDespesasData.evolucao_diaria.map(item => item.receitas) : [],
                    backgroundColor: 'rgba(34, 197, 94, 0.8)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 1
                }, {
                    label: 'Despesas',
                    data: receitasDespesasData.evolucao_diaria ? 
                        receitasDespesasData.evolucao_diaria.map(item => item.despesas) : [],
                    backgroundColor: 'rgba(239, 68, 68, 0.8)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + 
                                    context.parsed.y.toLocaleString('pt-BR', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    });
                            }
                        }
                    }
                }
            }
        });

        // Gráfico por Categoria
        const ctxCategorias = document.getElementById('categoriasChart').getContext('2d');
        new Chart(ctxCategorias, {
            type: 'doughnut',
            data: {
                labels: receitasDespesasData.por_categoria ? 
                    receitasDespesasData.por_categoria.map(item => item.categoria) : [],
                datasets: [{
                    data: receitasDespesasData.por_categoria ? 
                        receitasDespesasData.por_categoria.map(item => Math.abs(item.total)) : [],
                    backgroundColor: [
                        '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
                        '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': R$ ' + 
                                    context.parsed.toLocaleString('pt-BR', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    });
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    </script>

    <script src="js/financeiro.js"></script>
</body>
</html>
