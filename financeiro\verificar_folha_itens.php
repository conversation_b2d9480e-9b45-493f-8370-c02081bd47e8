<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== ESTRUTURA DA TABELA FOLHA_PAGAMENTO_ITENS ===\n";
    $estrutura = $db->fetchAll("DESCRIBE folha_pagamento_itens");
    foreach ($estrutura as $campo) {
        echo "{$campo['Field']} - {$campo['Type']} - {$campo['Null']} - {$campo['Default']}\n";
    }
    
    echo "\n=== DADOS DE EXEMPLO ===\n";
    $dados = $db->fetchAll("SELECT * FROM folha_pagamento_itens LIMIT 3");
    print_r($dados);
    
} catch (Exception $e) {
    echo 'Erro: ' . $e->getMessage() . "\n";
}
?>
