<?php
/**
 * Pagamentos - Módulo Financeiro
 * Sistema Faciência ERP - Gestão de Pagamentos
 */

// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definir diretório base
$baseDir = dirname(__DIR__);

// Incluir os arquivos de configuração e funções
require_once 'includes/config.php';
require_once '../includes/Database.php';

// Inicializar conexão
try {
    $db = Database::getInstance();
    if (!$db) {
        throw new Exception("Falha ao conectar com o banco de dados");
    }
} catch (Exception $e) {
    die("Erro de conexão: " . $e->getMessage());
}

// Variáveis de controle
$action = $_GET["action"] ?? "listar";
$id = $_GET["id"] ?? null;
$mensagem = "";
$erro = "";

// Processamento de formulários
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        switch ($_POST["action"]) {
            case "adicionar":
                $stmt = $db->prepare("
                    INSERT INTO pagamentos (funcionario_id, tipo, valor, data_pagamento, data_competencia, forma_pagamento, observacoes, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente')
                ");
                
                $funcionario_id = (int)$_POST['funcionario_id'];
                $tipo = $_POST['tipo'];
                $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                $valor = floatval($valor);
                $data_pagamento = $_POST['data_pagamento'];
                $data_competencia = $_POST['data_competencia'];
                $forma_pagamento = $_POST['forma_pagamento'];
                $observacoes = $_POST['observacoes'] ?? null;
                
                $stmt->execute([$funcionario_id, $tipo, $valor, $data_pagamento, $data_competencia, $forma_pagamento, $observacoes]);
                $mensagem = "Pagamento adicionado com sucesso!";
                break;
                
            case "editar":
                $stmt = $db->prepare("
                    UPDATE pagamentos 
                    SET funcionario_id = ?, tipo = ?, valor = ?, data_pagamento = ?, data_competencia = ?, forma_pagamento = ?, observacoes = ? 
                    WHERE id = ?
                ");
                
                $funcionario_id = (int)$_POST['funcionario_id'];
                $tipo = $_POST['tipo'];
                $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                $valor = floatval($valor);
                $data_pagamento = $_POST['data_pagamento'];
                $data_competencia = $_POST['data_competencia'];
                $forma_pagamento = $_POST['forma_pagamento'];
                $observacoes = $_POST['observacoes'] ?? null;
                $id = (int)$_POST['id'];
                
                $stmt->execute([$funcionario_id, $tipo, $valor, $data_pagamento, $data_competencia, $forma_pagamento, $observacoes, $id]);
                $mensagem = "Pagamento atualizado com sucesso!";
                break;
                
            case "confirmar_pagamento":
                $stmt = $db->prepare("UPDATE pagamentos SET status = 'pago' WHERE id = ?");
                $stmt->execute([(int)$_POST['id']]);
                $mensagem = "Pagamento confirmado com sucesso!";
                break;
                
            case "cancelar_pagamento":
                $stmt = $db->prepare("UPDATE pagamentos SET status = 'cancelado' WHERE id = ?");
                $stmt->execute([(int)$_POST['id']]);
                $mensagem = "Pagamento cancelado com sucesso!";
                break;
                
            case "excluir":
                $stmt = $db->prepare("DELETE FROM pagamentos WHERE id = ?");
                $stmt->execute([(int)$_POST['id']]);
                $mensagem = "Pagamento excluído com sucesso!";
                break;
        }
    } catch (Exception $e) {
        $erro = "Erro: " . $e->getMessage();
    }
}

// Buscar dados
$filtroStatus = $_GET['status'] ?? 'todos';
$filtroTipo = $_GET['tipo'] ?? 'todos';
$filtroMes = $_GET['mes'] ?? date('Y-m');

$whereConditions = [];
$params = [];

if ($filtroStatus !== 'todos') {
    $whereConditions[] = "p.status = ?";
    $params[] = $filtroStatus;
}

if ($filtroTipo !== 'todos') {
    $whereConditions[] = "p.tipo = ?";
    $params[] = $filtroTipo;
}

if ($filtroMes) {
    $whereConditions[] = "DATE_FORMAT(p.data_competencia, '%Y-%m') = ?";
    $params[] = $filtroMes;
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

$sql = "
    SELECT p.*, f.nome as funcionario_nome, f.cargo
    FROM pagamentos p
    LEFT JOIN funcionarios f ON p.funcionario_id = f.id
    $whereClause
    ORDER BY p.data_pagamento DESC, p.id DESC
";

$pagamentos = fetchData($db, $sql, $params);

// Buscar funcionários para o select
$funcionarios = fetchData($db, "SELECT id, nome, cargo FROM funcionarios WHERE status = 'ativo' ORDER BY nome");

// Estatísticas do mês
$statsSql = "
    SELECT 
        COUNT(*) as total_pagamentos,
        SUM(CASE WHEN status = 'pendente' THEN valor ELSE 0 END) as valor_pendente,
        SUM(CASE WHEN status = 'pago' THEN valor ELSE 0 END) as valor_pago,
        SUM(valor) as valor_total
    FROM pagamentos 
    WHERE DATE_FORMAT(data_competencia, '%Y-%m') = ?
";
$stats = fetchData($db, $statsSql, [$filtroMes])[0] ?? [
    'total_pagamentos' => 0,
    'valor_pendente' => 0,
    'valor_pago' => 0,
    'valor_total' => 0
];

$pageTitle = "Pagamentos";
include "includes/header_padronizado_novo.php";
include "includes/sidebar_padronizado.php";
?>

<!-- CSS Específico para Pagamentos -->
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .payments-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        overflow: hidden;
    }
    
    .payments-header {
        background: linear-gradient(135deg, #059669, #10b981);
        color: white;
        padding: 24px;
        text-align: center;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }
    
    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
        border-top: 4px solid;
    }
    
    .stat-card.total { border-top-color: #3b82f6; }
    .stat-card.pendente { border-top-color: #f59e0b; }
    .stat-card.pago { border-top-color: #10b981; }
    .stat-card.valor { border-top-color: #8b5cf6; }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .stat-label {
        color: #6b7280;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-group label {
        font-weight: 600;
        margin-bottom: 6px;
        color: #374151;
    }
    
    .filter-input {
        padding: 10px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s;
    }
    
    .filter-input:focus {
        outline: none;
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    
    .table-responsive {
        overflow-x: auto;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }
    
    .table th {
        background: #f8fafc;
        padding: 12px;
        font-weight: 600;
        color: #374151;
        border-bottom: 2px solid #e5e7eb;
        text-align: left;
    }
    
    .table td {
        padding: 12px;
        border-bottom: 1px solid #f3f4f6;
        color: #6b7280;
    }
    
    .table tr:hover {
        background: #f8fafc;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pendente {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-pago {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-cancelado {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .tipo-badge {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .tipo-salario { background: #dbeafe; color: #1e40af; }
    .tipo-adiantamento { background: #fef3c7; color: #92400e; }
    .tipo-bonus { background: #f3e8ff; color: #7c3aed; }
    .tipo-ferias { background: #dcfce7; color: #166534; }
    .tipo-13_salario { background: #fed7d7; color: #c53030; }
    .tipo-outros { background: #f3f4f6; color: #374151; }
    
    .btn-primary {
        background: #10b981;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary:hover {
        background: #059669;
        color: white;
    }
    
    .btn-action {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
        margin-right: 4px;
    }
    
    .btn-edit {
        background: #3b82f6;
        color: white;
    }
    
    .btn-edit:hover {
        background: #2563eb;
    }
    
    .btn-confirm {
        background: #10b981;
        color: white;
    }
    
    .btn-confirm:hover {
        background: #059669;
    }
    
    .btn-cancel {
        background: #f59e0b;
        color: white;
    }
    
    .btn-cancel:hover {
        background: #d97706;
    }
    
    .btn-delete {
        background: #ef4444;
        color: white;
    }
    
    .btn-delete:hover {
        background: #dc2626;
    }
    
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        padding: 20px;
        overflow-y: auto;
    }
    
    .modal-content {
        background: white;
        border-radius: 12px;
        max-width: 600px;
        margin: 50px auto;
        padding: 0;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
    }
    
    .modal-header {
        background: #f8fafc;
        padding: 20px 24px;
        border-bottom: 2px solid #e5e7eb;
        border-radius: 12px 12px 0 0;
    }
    
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }
    
    .modal-body {
        padding: 24px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: 600;
        margin-bottom: 6px;
        color: #374151;
    }
    
    .form-input {
        padding: 10px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    
    .modal-footer {
        background: #f8fafc;
        padding: 16px 24px;
        border-top: 1px solid #e5e7eb;
        border-radius: 0 0 12px 12px;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
    
    @media (max-width: 768px) {
        .page-container {
            padding: 16px;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .payments-header {
            padding: 16px;
        }
        
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .modal-content {
            margin: 20px auto;
        }
        
        .table th,
        .table td {
            padding: 8px;
            font-size: 14px;
        }
    }
</style>

<div class="page-container">

<!-- Header da página -->
<div class="payments-card">
    <div class="payments-header">
        <h1><i class="fas fa-money-bill-wave me-3"></i>Gestão de Pagamentos</h1>
        <p class="mt-2 opacity-90">Controle de pagamentos a funcionários • <?php echo date("d/m/Y H:i"); ?></p>
    </div>
</div>

<!-- Mensagens -->
<?php if ($mensagem): ?>
    <div class="payments-card">
        <div class="alert alert-success d-flex align-items-center p-3 m-0">
            <i class="fas fa-check-circle me-2"></i>
            <span><?php echo $mensagem; ?></span>
        </div>
    </div>
<?php endif; ?>

<?php if ($erro): ?>
    <div class="payments-card">
        <div class="alert alert-danger d-flex align-items-center p-3 m-0">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span><?php echo $erro; ?></span>
        </div>
    </div>
<?php endif; ?>

<!-- Estatísticas -->
<div class="stats-grid">
    <div class="stat-card total">
        <div class="stat-value"><?php echo $stats['total_pagamentos']; ?></div>
        <div class="stat-label">Total de Pagamentos</div>
    </div>
    <div class="stat-card pendente">
        <div class="stat-value">R$ <?php echo number_format($stats['valor_pendente'], 2, ',', '.'); ?></div>
        <div class="stat-label">Valor Pendente</div>
    </div>
    <div class="stat-card pago">
        <div class="stat-value">R$ <?php echo number_format($stats['valor_pago'], 2, ',', '.'); ?></div>
        <div class="stat-label">Valor Pago</div>
    </div>
    <div class="stat-card valor">
        <div class="stat-value">R$ <?php echo number_format($stats['valor_total'], 2, ',', '.'); ?></div>
        <div class="stat-label">Valor Total</div>
    </div>
</div>

<!-- Filtros e Ações -->
<div class="payments-card">
    <div class="p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtros e Controles</h5>
            <button type="button" class="btn-primary" onclick="openModal('modalAdicionar')">
                <i class="fas fa-plus"></i>Novo Pagamento
            </button>
        </div>
        
        <form method="GET" class="filters-grid">
            <div class="filter-group">
                <label>Status</label>
                <select name="status" class="filter-input">
                    <option value="todos" <?php echo $filtroStatus === 'todos' ? 'selected' : ''; ?>>Todos os Status</option>
                    <option value="pendente" <?php echo $filtroStatus === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                    <option value="pago" <?php echo $filtroStatus === 'pago' ? 'selected' : ''; ?>>Pago</option>
                    <option value="cancelado" <?php echo $filtroStatus === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Tipo</label>
                <select name="tipo" class="filter-input">
                    <option value="todos" <?php echo $filtroTipo === 'todos' ? 'selected' : ''; ?>>Todos os Tipos</option>
                    <option value="salario" <?php echo $filtroTipo === 'salario' ? 'selected' : ''; ?>>Salário</option>
                    <option value="adiantamento" <?php echo $filtroTipo === 'adiantamento' ? 'selected' : ''; ?>>Adiantamento</option>
                    <option value="bonus" <?php echo $filtroTipo === 'bonus' ? 'selected' : ''; ?>>Bônus</option>
                    <option value="ferias" <?php echo $filtroTipo === 'ferias' ? 'selected' : ''; ?>>Férias</option>
                    <option value="13_salario" <?php echo $filtroTipo === '13_salario' ? 'selected' : ''; ?>>13º Salário</option>
                    <option value="outros" <?php echo $filtroTipo === 'outros' ? 'selected' : ''; ?>>Outros</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Mês de Competência</label>
                <input type="month" name="mes" value="<?php echo $filtroMes; ?>" class="filter-input">
            </div>
            <div class="filter-group d-flex align-items-end">
                <button type="submit" class="btn-primary me-2">
                    <i class="fas fa-search"></i>Filtrar
                </button>
                <a href="pagamentos.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>Limpar
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Pagamentos -->
<div class="payments-card">
    <div class="p-4">
        <h5 class="mb-4"><i class="fas fa-list me-2"></i>Lista de Pagamentos</h5>
        
        <?php if (empty($pagamentos)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">Nenhum pagamento encontrado com os filtros aplicados</p>
                <button type="button" class="btn-primary" onclick="openModal('modalAdicionar')">
                    <i class="fas fa-plus"></i>Adicionar Primeiro Pagamento
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Funcionário</th>
                            <th>Tipo</th>
                            <th>Valor</th>
                            <th>Data Pagamento</th>
                            <th>Competência</th>
                            <th>Forma</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pagamentos as $pagamento): ?>
                        <tr>
                            <td>#<?php echo $pagamento['id']; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($pagamento['funcionario_nome']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($pagamento['cargo']); ?></small>
                            </td>
                            <td>
                                <span class="tipo-badge tipo-<?php echo $pagamento['tipo']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $pagamento['tipo'])); ?>
                                </span>
                            </td>
                            <td class="fw-bold">R$ <?php echo number_format($pagamento['valor'], 2, ',', '.'); ?></td>
                            <td><?php echo date('d/m/Y', strtotime($pagamento['data_pagamento'])); ?></td>
                            <td><?php echo date('m/Y', strtotime($pagamento['data_competencia'])); ?></td>
                            <td><?php echo ucfirst($pagamento['forma_pagamento']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $pagamento['status']; ?>">
                                    <?php echo ucfirst($pagamento['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($pagamento['status'] === 'pendente'): ?>
                                    <button onclick="editarPagamento(<?= $pagamento['id'] ?>, '<?= htmlspecialchars($pagamento['funcionario_nome']) ?>', <?= $pagamento['funcionario_id'] ?>, '<?= $pagamento['tipo'] ?>', <?= $pagamento['valor'] ?>, '<?= $pagamento['data_pagamento'] ?>', '<?= $pagamento['data_competencia'] ?>', '<?= $pagamento['forma_pagamento'] ?>', '<?= htmlspecialchars($pagamento['observacoes']) ?>')" 
                                            class="btn-action btn-edit" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="confirmarPagamento(<?= $pagamento['id'] ?>, '<?= htmlspecialchars($pagamento['funcionario_nome']) ?>')" 
                                            class="btn-action btn-confirm" title="Confirmar Pagamento">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button onclick="cancelarPagamento(<?= $pagamento['id'] ?>, '<?= htmlspecialchars($pagamento['funcionario_nome']) ?>')" 
                                            class="btn-action btn-cancel" title="Cancelar">
                                        <i class="fas fa-times"></i>
                                    </button>
                                <?php endif; ?>
                                <button onclick="excluirPagamento(<?= $pagamento['id'] ?>, '<?= htmlspecialchars($pagamento['funcionario_nome']) ?>')" 
                                        class="btn-action btn-delete" title="Excluir">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

</div>

<!-- Modal Adicionar Pagamento -->
<div id="modalAdicionar" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">
                <i class="fas fa-plus me-2"></i>Novo Pagamento
            </h5>
        </div>
        <form method="POST">
            <div class="modal-body">
                <input type="hidden" name="action" value="adicionar">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>Funcionário *</label>
                        <select name="funcionario_id" class="form-input" required>
                            <option value="">Selecione um funcionário</option>
                            <?php foreach ($funcionarios as $funcionario): ?>
                                <option value="<?= $funcionario['id'] ?>"><?= htmlspecialchars($funcionario['nome']) ?> - <?= htmlspecialchars($funcionario['cargo']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tipo *</label>
                        <select name="tipo" class="form-input" required>
                            <option value="salario">Salário</option>
                            <option value="adiantamento">Adiantamento</option>
                            <option value="bonus">Bônus</option>
                            <option value="ferias">Férias</option>
                            <option value="13_salario">13º Salário</option>
                            <option value="outros">Outros</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Valor *</label>
                        <input type="text" name="valor" class="form-input" placeholder="0,00" required 
                               onkeyup="formatarMoeda(this)">
                    </div>
                    <div class="form-group">
                        <label>Data do Pagamento *</label>
                        <input type="date" name="data_pagamento" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label>Competência *</label>
                        <input type="month" name="data_competencia" class="form-input" value="<?= date('Y-m') ?>" required>
                    </div>
                    <div class="form-group">
                        <label>Forma de Pagamento *</label>
                        <select name="forma_pagamento" class="form-input" required>
                            <option value="transferencia">Transferência</option>
                            <option value="pix">PIX</option>
                            <option value="cheque">Cheque</option>
                            <option value="dinheiro">Dinheiro</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Observações</label>
                    <textarea name="observacoes" rows="3" class="form-input" placeholder="Observações sobre o pagamento"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal('modalAdicionar')" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save me-1"></i>Salvar Pagamento
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Editar Pagamento -->
<div id="modalEditar" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">
                <i class="fas fa-edit me-2"></i>Editar Pagamento
            </h5>
        </div>
        <form method="POST">
            <div class="modal-body">
                <input type="hidden" name="action" value="editar">
                <input type="hidden" name="id" id="editar_id">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>Funcionário *</label>
                        <select name="funcionario_id" id="editar_funcionario_id" class="form-input" required>
                            <option value="">Selecione um funcionário</option>
                            <?php foreach ($funcionarios as $funcionario): ?>
                                <option value="<?= $funcionario['id'] ?>"><?= htmlspecialchars($funcionario['nome']) ?> - <?= htmlspecialchars($funcionario['cargo']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tipo *</label>
                        <select name="tipo" id="editar_tipo" class="form-input" required>
                            <option value="salario">Salário</option>
                            <option value="adiantamento">Adiantamento</option>
                            <option value="bonus">Bônus</option>
                            <option value="ferias">Férias</option>
                            <option value="13_salario">13º Salário</option>
                            <option value="outros">Outros</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Valor *</label>
                        <input type="text" name="valor" id="editar_valor" class="form-input" required 
                               onkeyup="formatarMoeda(this)">
                    </div>
                    <div class="form-group">
                        <label>Data do Pagamento *</label>
                        <input type="date" name="data_pagamento" id="editar_data_pagamento" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label>Competência *</label>
                        <input type="month" name="data_competencia" id="editar_data_competencia" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label>Forma de Pagamento *</label>
                        <select name="forma_pagamento" id="editar_forma_pagamento" class="form-input" required>
                            <option value="transferencia">Transferência</option>
                            <option value="pix">PIX</option>
                            <option value="cheque">Cheque</option>
                            <option value="dinheiro">Dinheiro</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Observações</label>
                    <textarea name="observacoes" id="editar_observacoes" rows="3" class="form-input"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal('modalEditar')" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save me-1"></i>Salvar Alterações
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Confirmar Pagamento -->
<div id="modalConfirmar" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">
                <i class="fas fa-check me-2"></i>Confirmar Pagamento
            </h5>
        </div>
        <form method="POST">
            <div class="modal-body">
                <input type="hidden" name="action" value="confirmar_pagamento">
                <input type="hidden" name="id" id="confirmar_id">
                <p>Tem certeza que deseja confirmar o pagamento para:</p>
                <div id="confirmar_info" class="p-3 bg-light rounded"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal('modalConfirmar')" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check me-1"></i>Confirmar Pagamento
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Cancelar Pagamento -->
<div id="modalCancelar" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">
                <i class="fas fa-times me-2"></i>Cancelar Pagamento
            </h5>
        </div>
        <form method="POST">
            <div class="modal-body">
                <input type="hidden" name="action" value="cancelar_pagamento">
                <input type="hidden" name="id" id="cancelar_id">
                <p>Tem certeza que deseja cancelar o pagamento para:</p>
                <div id="cancelar_info" class="p-3 bg-light rounded"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal('modalCancelar')" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Voltar
                </button>
                <button type="submit" class="btn btn-warning">
                    <i class="fas fa-times me-1"></i>Cancelar Pagamento
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Excluir Pagamento -->
<div id="modalExcluir" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">
                <i class="fas fa-trash me-2"></i>Excluir Pagamento
            </h5>
        </div>
        <form method="POST">
            <div class="modal-body">
                <input type="hidden" name="action" value="excluir">
                <input type="hidden" name="id" id="excluir_id">
                <p class="text-danger"><strong>Atenção:</strong> Esta ação não pode ser desfeita!</p>
                <p>Tem certeza que deseja excluir o pagamento para:</p>
                <div id="excluir_info" class="p-3 bg-light rounded"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal('modalExcluir')" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i>Excluir Pagamento
                </button>
            </div>
        </form>
    </div>
</div>

<?php include "includes/footer_padronizado.php"; ?>

<!-- Scripts Específicos -->
<script>
// Controle de Modais
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Fechar modal clicando fora
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        closeModal(event.target.id);
    }
});

// Fechar modal com ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (modal.style.display === 'block') {
                closeModal(modal.id);
            }
        });
    }
});

// Formatação de moeda
function formatarMoeda(campo) {
    let valor = campo.value.replace(/\D/g, '');
    valor = (valor / 100).toFixed(2) + '';
    valor = valor.replace(".", ",");
    valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
    campo.value = valor;
}

// Funções para ações
function editarPagamento(id, funcionarioNome, funcionarioId, tipo, valor, dataPagamento, dataCompetencia, formaPagamento, observacoes) {
    document.getElementById('editar_id').value = id;
    document.getElementById('editar_funcionario_id').value = funcionarioId;
    document.getElementById('editar_tipo').value = tipo;
    document.getElementById('editar_valor').value = valor.toLocaleString('pt-BR', {minimumFractionDigits: 2});
    document.getElementById('editar_data_pagamento').value = dataPagamento;
    document.getElementById('editar_data_competencia').value = dataCompetencia.substring(0, 7); // YYYY-MM
    document.getElementById('editar_forma_pagamento').value = formaPagamento;
    document.getElementById('editar_observacoes').value = observacoes || '';
    openModal('modalEditar');
}

function confirmarPagamento(id, funcionarioNome) {
    document.getElementById('confirmar_id').value = id;
    document.getElementById('confirmar_info').innerHTML = `<strong>${funcionarioNome}</strong>`;
    openModal('modalConfirmar');
}

function cancelarPagamento(id, funcionarioNome) {
    document.getElementById('cancelar_id').value = id;
    document.getElementById('cancelar_info').innerHTML = `<strong>${funcionarioNome}</strong>`;
    openModal('modalCancelar');
}

function excluirPagamento(id, funcionarioNome) {
    document.getElementById('excluir_id').value = id;
    document.getElementById('excluir_info').innerHTML = `<strong>${funcionarioNome}</strong>`;
    openModal('modalExcluir');
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    console.log('Página de Pagamentos carregada');
    
    // Definir data padrão para hoje
    const hoje = new Date().toISOString().split('T')[0];
    const camposData = document.querySelectorAll('input[name="data_pagamento"]');
    camposData.forEach(campo => {
        if (!campo.value) {
            campo.value = hoje;
        }
    });
});
</script>
