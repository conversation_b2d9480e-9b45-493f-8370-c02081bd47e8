<?php
/**
 * ================================================================
 *                    SISTEMA FACIÊNCIA ERP
 * ================================================================
 * 
 * Módulo: Gerenciamento de Disciplinas
 * Descrição: Interface principal para gerenciamento de disciplinas acadêmicas
 * Versão: 2.0
 * Data de Atualização: 2024-12-19
 * 
 * Funcionalidades:
 * - Cadastro, edição e exclusão de disciplinas
 * - Visualização detalhada de disciplinas
 * - Vinculação com cursos e professores
 * - Controle de carga horária e período
 * - Gerenciamento de status (ativo/inativo)
 * - Associação com turmas
 * - Busca avançada e filtros
 * - Paginação de resultados
 * 
 * Estrutura de Navegação:
 * - listar: Listagem paginada de disciplinas
 * - nova: Formulário para criar nova disciplina
 * - editar: Formulário para editar disciplina existente
 * - visualizar: Detalhes completos da disciplina
 * - salvar: Processamento de dados do formulário
 * - excluir: Remoção de disciplina (com validações)
 * - buscar: Pesquisa avançada de disciplinas
 * - cadastrar_professor: Cadastro rápido de professor via AJAX
 * 
 * ================================================================
 */

// ================================================================
// CONFIGURAÇÕES INICIAIS E CONSTANTES
// ================================================================

// Carregamento do sistema base
require_once __DIR__ . '/../includes/init.php';

// Configurações específicas do módulo
ini_set('memory_limit', '256M'); // Aumenta limite de memória para relatórios

// ================================================================
// VERIFICAÇÃO DE AUTENTICAÇÃO E PERMISSÕES
// ================================================================

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica se o usuário tem permissão para acessar o módulo de disciplinas
exigirPermissao('disciplinas');

// ================================================================
// INICIALIZAÇÃO DE COMPONENTES
// ================================================================

// Instancia o banco de dados
$db = Database::getInstance();

// Define a ação atual baseada no parâmetro GET
$action = $_GET['action'] ?? 'listar';

// Função para executar consultas com tratamento de erro
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        // Registra o erro no log
        error_log('Erro na consulta SQL: ' . $e->getMessage() . ' - SQL: ' . $sql);
        return $default;
    }
}

function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        error_log("Executando consulta fetchAll: " . $sql);
        error_log("Parâmetros: " . json_encode($params));

        $result = $db->fetchAll($sql, $params);

        if ($result === false) {
            error_log("A consulta retornou false, usando valor padrão");
            return $default;
        }

        if (empty($result)) {
            error_log("A consulta retornou um array vazio");
            return $default;
        }

        error_log("A consulta retornou " . count($result) . " resultados");
        return $result;
    } catch (Exception $e) {
        // Registra o erro no log
        error_log('Erro na consulta SQL: ' . $e->getMessage() . ' - SQL: ' . $sql);
        error_log('Stack trace: ' . $e->getTraceAsString());
        return $default;
    }
}

// Processa a ação
switch ($action) {
    // ============================================================
    // NOVA DISCIPLINA - FORMULÁRIO DE CRIAÇÃO
    // ============================================================
    case 'nova':
        // Exibe o formulário para adicionar uma nova disciplina
        $titulo_pagina = 'Nova Disciplina';
        $view = 'form';
        $disciplina = []; // Inicializa uma disciplina vazia

        // Se foi passado um curso_id, pré-seleciona o curso
        if (isset($_GET['curso_id'])) {
            $disciplina['curso_id'] = $_GET['curso_id'];

            // Busca o curso para exibir informações
            $sql = "SELECT * FROM cursos WHERE id = ?";
            $curso = executarConsulta($db, $sql, [$disciplina['curso_id']]);

            if ($curso) {
                $titulo_pagina = 'Nova Disciplina - ' . $curso['nome'];
            }
        }

        // Carrega os cursos para o formulário
        $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
        $cursos = executarConsultaAll($db, $sql);

        // Verifica se a tabela professores existe e cria se necessário
        try {
            $sql = "SHOW TABLES LIKE 'professores'";
            $tabela_existe = $db->fetchOne($sql);

            if (!$tabela_existe) {
                // Cria a tabela professores
                $sql = "CREATE TABLE professores (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nome VARCHAR(150) NOT NULL,
                    email VARCHAR(100),
                    cpf VARCHAR(20),
                    telefone VARCHAR(20),
                    formacao VARCHAR(100),
                    titulacao ENUM('graduacao', 'especializacao', 'mestrado', 'doutorado', 'pos_doutorado'),
                    area_atuacao VARCHAR(100),
                    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                    id_legado VARCHAR(50),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (status)
                )";
                $db->query($sql);
            }
        } catch (Exception $e) {
            error_log('Erro ao verificar/criar tabela professores: ' . $e->getMessage());
        }

        // Carrega os professores para o formulário
        $sql = "SELECT id, nome FROM professores WHERE status = 'ativo' ORDER BY nome ASC";
        $professores = executarConsultaAll($db, $sql);

        // Não carrega turmas por padrão - serão carregadas dinamicamente via AJAX
        // quando o usuário selecionar um curso no formulário
        $turmas = [];
        break;

    // ============================================================
    // EDITAR DISCIPLINA - FORMULÁRIO DE EDIÇÃO    // ============================================================
    case 'editar':
        // Exibe o formulário para editar uma disciplina existente
        $id = $_GET['id'] ?? 0;
        $vincular_curso = $_GET['vincular_curso'] ?? null;
        $atualizar_dados = $_GET['atualizar'] ?? false;

        // Busca a disciplina pelo ID
        $sql = "SELECT * FROM disciplinas WHERE id = ?";
        $disciplina = executarConsulta($db, $sql, [$id], []);

        if (!$disciplina) {
            // Disciplina não encontrada, redireciona para a listagem
            setMensagem('erro', 'Disciplina não encontrada.');
            redirect('disciplinas.php');
        }        // Se está vinculando a um novo curso, atualiza os dados
        if ($vincular_curso) {
            $disciplina['curso_id'] = $vincular_curso;
            
            // Se foi solicitado atualizar dados, aplica as mudanças
            if ($atualizar_dados) {
                if (isset($_GET['nova_carga_horaria']) && !empty($_GET['nova_carga_horaria'])) {
                    $disciplina['carga_horaria'] = $_GET['nova_carga_horaria'];
                }
                if (isset($_GET['novo_periodo'])) {
                    $disciplina['periodo'] = $_GET['novo_periodo'];
                }
                if (isset($_GET['novo_professor']) && !empty($_GET['novo_professor'])) {
                    $disciplina['professor_padrao_id'] = $_GET['novo_professor'];
                }
                if (isset($_GET['nova_titulacao']) && !empty($_GET['nova_titulacao'])) {
                    // Atualiza a titulação do professor se foi especificada
                    $disciplina['_nova_titulacao'] = $_GET['nova_titulacao'];
                }
            }
            
            // Busca informações do novo curso
            $sql = "SELECT nome FROM cursos WHERE id = ?";
            $novo_curso = executarConsulta($db, $sql, [$vincular_curso]);
            
            if ($novo_curso) {
                $titulo_pagina = 'Vincular Disciplina - ' . $novo_curso['nome'];
                // Adiciona uma flag para indicar que é uma vinculação
                $disciplina['_vinculando_curso'] = true;
                $disciplina['_novo_curso_nome'] = $novo_curso['nome'];
            }
        } else {
            $titulo_pagina = 'Editar Disciplina';
        }        // Carrega os cursos para o formulário
        $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
        $cursos = executarConsultaAll($db, $sql);
        
        // Carrega apenas alguns professores para o formulário inicial (o restante será carregado via AJAX)
        $sql = "SELECT id, nome FROM professores WHERE status = 'ativo' ORDER BY nome ASC LIMIT 20";
        $professores = executarConsultaAll($db, $sql);

        // IMPORTANTE: Se a disciplina tem um professor associado (principalmente em vinculações), 
        // garante que ele esteja na lista independentemente da ordem ou limite
        if (!empty($disciplina['professor_padrao_id'])) {
            $professor_encontrado = false;
            foreach ($professores as $professor) {
                if ($professor['id'] == $disciplina['professor_padrao_id']) {
                    $professor_encontrado = true;
                    break;
                }
            }

            if (!$professor_encontrado) {
                $sql = "SELECT id, nome FROM professores WHERE id = ?";
                $professor_especifico = executarConsulta($db, $sql, [$disciplina['professor_padrao_id']]);

                if ($professor_especifico) {
                    // Adiciona no início da lista para garantir que aparece
                    array_unshift($professores, $professor_especifico);
                    
                    // Log para debug (pode ser removido em produção)
                    error_log("Professor ID {$disciplina['professor_padrao_id']} adicionado à lista para vinculação");
                } else {
                    // Log de erro se professor não foi encontrado
                    error_log("ERRO: Professor ID {$disciplina['professor_padrao_id']} não encontrado na base de dados");
                }
            } else {
                // Log para confirmar que professor já estava na lista
                error_log("Professor ID {$disciplina['professor_padrao_id']} já estava na lista inicial");
            }
        }

        // Busca o curso para exibir informações
        if (!empty($disciplina['curso_id'])) {
            $sql = "SELECT * FROM cursos WHERE id = ?";
            $curso = executarConsulta($db, $sql, [$disciplina['curso_id']]);
        }

        // Não carrega turmas por padrão - serão carregadas dinamicamente via AJAX
        // quando necessário. Para edição, o JavaScript carregará as turmas do curso atual
        $turmas = [];

        // Busca as turmas já associadas a esta disciplina
        $sql = "SELECT turma_id FROM turma_disciplinas WHERE disciplina_id = ?";
        $turmas_associadas = executarConsultaAll($db, $sql, [$id]);
        $disciplina['turmas_selecionadas'] = array_column($turmas_associadas, 'turma_id');

        $titulo_pagina = 'Editar Disciplina';
        $view = 'form';
        break;

    // ============================================================
    // SALVAR DISCIPLINA - PROCESSAMENTO DE DADOS
    // ============================================================
    case 'salvar':
        // DEBUG: Interceptor para monitorar salvamento
        $debug_file = __DIR__ . '/../debug_salvamento_disciplina.log';
        $debug_data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'],
            'post_data' => $_POST,
            'user_id' => $_SESSION['user_id'] ?? 'N/A',
            'referer' => $_SERVER['HTTP_REFERER'] ?? 'N/A'
        ];
        file_put_contents($debug_file, json_encode($debug_data, JSON_PRETTY_PRINT) . "\n" . str_repeat('-', 80) . "\n", FILE_APPEND | LOCK_EX);
        error_log("DEBUG SALVAMENTO DISCIPLINA: " . json_encode($_POST));
        error_log("Professor enviado: " . ($_POST['professor_padrao_id'] ?? 'NULL'));
        
        // Salva os dados da disciplina (nova ou existente)
        if (!isPost()) {
            // Método não permitido
            setMensagem('erro', 'Método não permitido.');
            redirect('disciplinas.php');
        }        // Obtém os dados do formulário (apenas campos essenciais)
        $id = $_POST['id'] ?? null;
        $nome = $_POST['nome'] ?? '';
        $curso_id = $_POST['curso_id'] ?? null;
        $carga_horaria = $_POST['carga_horaria'] ?? 0;
        $status = $_POST['status'] ?? 'ativo';
        $periodo = $_POST['periodo'] ?? '';
        $professor_padrao_id = $_POST['professor_padrao_id'] ?? null;
        $professor_backup = $_POST['professor_backup'] ?? null;
        $id_legado = $_POST['id_legado'] ?? '';
        
        // Se professor_padrao_id está vazio mas temos backup, usa o backup
        if (empty($professor_padrao_id) && !empty($professor_backup)) {
            $professor_padrao_id = $professor_backup;
            error_log("USANDO PROFESSOR DO CAMPO BACKUP: $professor_padrao_id");
        }
        
        error_log("PROFESSOR FINAL DETERMINADO: " . ($professor_padrao_id ?? 'NULL'));

        // Valida os dados
        $erros = [];

        if (empty($nome)) {
            $erros[] = 'O nome é obrigatório.';
        }

        if (empty($curso_id)) {
            $erros[] = 'O curso é obrigatório.';
        }

        if (empty($carga_horaria) || $carga_horaria <= 0) {
            $erros[] = 'A carga horária é obrigatória e deve ser maior que zero.';
        }

        if (!empty($erros)) {
            // Há erros de validação, exibe o formulário novamente
            $titulo_pagina = $id ? 'Editar Disciplina' : 'Nova Disciplina';
            $view = 'form';
            $disciplina = $_POST;            $mensagens_erro = $erros;

            // Carrega os cursos para o formulário
            $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
            $cursos = executarConsultaAll($db, $sql);
            
            // Carrega apenas alguns professores para o formulário inicial (o restante será carregado via AJAX)
            $sql = "SELECT id, nome FROM professores WHERE status = 'ativo' ORDER BY nome ASC LIMIT 20";            $professores = executarConsultaAll($db, $sql);

            // Se foi informado um professor_padrao_id e ele não está nos professores recentes, busca especificamente esse professor
            if (!empty($professor_padrao_id)) {
                $professor_encontrado = false;
                foreach ($professores as $professor) {
                    if ($professor['id'] == $professor_padrao_id) {
                        $professor_encontrado = true;
                        break;
                    }
                }

                if (!$professor_encontrado) {
                    $sql = "SELECT id, nome FROM professores WHERE id = ?";
                    $professor_especifico = executarConsulta($db, $sql, [$professor_padrao_id]);

                    if ($professor_especifico) {
                        array_unshift($professores, $professor_especifico);
                    }
                }
            }

            break;
        }

        // Obtém as turmas selecionadas (se houver)
        $turmas_selecionadas = $_POST['turmas'] ?? [];

        // Valida se o professor existe (se informado)
        if (!empty($professor_padrao_id)) {
            $sql_check_prof = "SELECT id FROM professores WHERE id = ?";
            $professor_existe = executarConsulta($db, $sql_check_prof, [$professor_padrao_id]);

            if (!$professor_existe) {
                $erros[] = 'Professor selecionado não encontrado.';
            }
        }

        if (!empty($erros)) {
            // Há erros de validação, exibe o formulário novamente
            $titulo_pagina = $id ? 'Editar Disciplina' : 'Nova Disciplina';
            $view = 'form';
            $disciplina = $_POST;
            $mensagens_erro = $erros;            // Carrega os dados necessários para o formulário
            $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
            $cursos = executarConsultaAll($db, $sql);
            
            $sql = "SELECT id, nome FROM professores WHERE status = 'ativo' ORDER BY nome ASC LIMIT 20";
            $professores = executarConsultaAll($db, $sql);

            // Carrega as turmas (filtra por curso se especificado)
            if (!empty($_POST['curso_id'])) {
                $sql = "SELECT t.id, t.nome, c.nome as curso_nome
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        WHERE t.status IN ('ativo', 'em_andamento') AND t.curso_id = ?
                        ORDER BY t.nome ASC";
                $turmas = executarConsultaAll($db, $sql, [$_POST['curso_id']]);
            } else {
                $sql = "SELECT t.id, t.nome, c.nome as curso_nome
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        WHERE t.status IN ('ativo', 'em_andamento')
                        ORDER BY c.nome ASC, t.nome ASC";
                $turmas = executarConsultaAll($db, $sql);
            }

            break;
        }

        // Prepara os dados para salvar (apenas campos essenciais)
        $dados = [
            'nome' => $nome,
            'curso_id' => $curso_id,
            'professor_padrao_id' => !empty($professor_padrao_id) ? $professor_padrao_id : null,
            'carga_horaria' => $carga_horaria,
            'periodo' => $periodo,
            'status' => $status,
            'id_legado' => $id_legado,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            // Inicia uma transação
            $db->beginTransaction();            if ($id) {
                // DEBUG: Log antes do update
                error_log("ANTES DO UPDATE - Disciplina ID: $id");
                error_log("Dados para update: " . json_encode($dados));
                error_log("Professor padrão ID: " . ($dados['professor_padrao_id'] ?? 'NULL'));
                
                // Verifica estado atual antes do update
                $estado_antes = $db->fetchOne("SELECT * FROM disciplinas WHERE id = ?", [$id]);
                error_log("Estado antes do update: " . json_encode($estado_antes));
                
                // Atualiza uma disciplina existente
                $result_update = $db->update('disciplinas', $dados, 'id = ?', [$id]);
                
                // DEBUG: Log após o update
                error_log("Resultado do update: " . ($result_update ? 'SUCCESS' : 'FAILED'));
                
                // Verifica estado após o update
                $estado_depois = $db->fetchOne("SELECT * FROM disciplinas WHERE id = ?", [$id]);
                error_log("Estado depois do update: " . json_encode($estado_depois));
                
                if (!$result_update) {
                    error_log("ERRO: Update falhou para disciplina ID: $id");
                }

                // Registra o log
                registrarLog(
                    'disciplinas',
                    'editar',
                    "Disciplina ID: {$id} atualizada",
                    $id,
                    'disciplinas'
                );

                setMensagem('sucesso', 'Disciplina atualizada com sucesso.');
            } else {
                // Adiciona a data de criação
                $dados['created_at'] = date('Y-m-d H:i:s');

                // Insere uma nova disciplina
                $id = $db->insert('disciplinas', $dados);

                // Registra o log
                registrarLog(
                    'disciplinas',
                    'criar',
                    "Disciplina ID: {$id} criada",
                    $id,
                    'disciplinas'
                );

                setMensagem('sucesso', 'Disciplina adicionada com sucesso.');
            }

            // Processa as turmas selecionadas
            if (!empty($turmas_selecionadas) && is_array($turmas_selecionadas)) {
                // Remove associações existentes se estiver editando
                if ($id) {
                    $db->delete('turma_disciplinas', 'disciplina_id = ?', [$id]);
                }                // Adiciona as novas associações
                foreach ($turmas_selecionadas as $turma_id) {
                    if (!empty($turma_id)) {
                        // Verifica se a associação já existe
                        $sql_check = "SELECT id FROM turma_disciplinas WHERE turma_id = ? AND disciplina_id = ?";
                        $existe = executarConsulta($db, $sql_check, [$turma_id, $id]);

                        if (!$existe) {
                            $dados_turma_disciplina = [
                                'turma_id' => $turma_id,
                                'disciplina_id' => $id,
                                'professor_id' => !empty($professor_padrao_id) ? $professor_padrao_id : null,
                                'status' => 'planejada',
                                'created_at' => date('Y-m-d H:i:s')
                            ];

                            $db->insert('turma_disciplinas', $dados_turma_disciplina);
                        }
                    }
                }            }

            // Confirma a transação
            $db->commit();

            // Atualiza a titulação do professor se foi especificada
            if (!empty($professor_padrao_id) && isset($_POST['nova_titulacao']) && !empty($_POST['nova_titulacao'])) {
                try {
                    $db->update('professores', 
                        ['formacao' => $_POST['nova_titulacao'], 'updated_at' => date('Y-m-d H:i:s')],
                        'id = ?', 
                        [$professor_padrao_id]
                    );
                    
                    registrarLog(
                        'professores',
                        'editar',
                        "Titulação do professor ID: {$professor_padrao_id} atualizada para: " . $_POST['nova_titulacao'],
                        $professor_padrao_id,
                        'disciplinas'
                    );
                } catch (Exception $e) {
                    // Log do erro mas não interrompe o fluxo
                    error_log("Erro ao atualizar titulação do professor: " . $e->getMessage());
                }
            }

            // Verifica se deve continuar cadastrando
            if (isset($_POST['continuar_cadastrando']) && $_POST['continuar_cadastrando'] === 'on') {
                // Mantém o curso fixado se houver
                $curso_fixado = isset($_POST['curso_id']) ? $_POST['curso_id'] : null;
                $redirect_url = 'disciplinas.php?action=nova';
                if ($curso_fixado) {
                    $redirect_url .= '&curso_id=' . $curso_fixado;
                }
                redirect($redirect_url);
            } else {
                // Redireciona para a visualização da disciplina
                redirect('disciplinas.php?action=visualizar&id=' . $id);
            }
        } catch (Exception $e) {
            // Desfaz a transação em caso de erro
            $db->rollBack();

            // Erro ao salvar
            $titulo_pagina = $id ? 'Editar Disciplina' : 'Nova Disciplina';
            $view = 'form';
            $disciplina = $_POST;
            $mensagens_erro = ['Erro ao salvar a disciplina: ' . $e->getMessage()];            // Carrega os cursos para o formulário
            $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
            $cursos = executarConsultaAll($db, $sql);
            
            // Carrega apenas alguns professores para o formulário inicial (o restante será carregado via AJAX)
            $sql = "SELECT id, nome FROM professores WHERE status = 'ativo' ORDER BY nome ASC LIMIT 20";
            $professores = executarConsultaAll($db, $sql);            
            
            // Carrega as turmas ativas (filtra por curso se especificado)
            if (!empty($_POST['curso_id'])) {
                $sql = "SELECT t.id, t.nome, c.nome as curso_nome
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        WHERE t.status IN ('ativo', 'em_andamento') AND t.curso_id = ?
                        ORDER BY t.nome ASC";
                $turmas = executarConsultaAll($db, $sql, [$_POST['curso_id']]);
            } else {
                $sql = "SELECT t.id, t.nome, c.nome as curso_nome
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        WHERE t.status IN ('ativo', 'em_andamento')
                        ORDER BY c.nome ASC, t.nome ASC";
                $turmas = executarConsultaAll($db, $sql);
            }
        }
        break;

    // ============================================================
    // EXCLUIR DISCIPLINA - REMOÇÃO COM VALIDAÇÕES
    // ============================================================
    case 'excluir':
        // Exclui uma disciplina
        $id = $_GET['id'] ?? 0;

        // Verifica se o usuário tem permissão para excluir
        exigirPermissao('disciplinas', 'excluir');

        // Busca a disciplina pelo ID
        $sql = "SELECT * FROM disciplinas WHERE id = ?";
        $disciplina = executarConsulta($db, $sql, [$id], []);

        if (!$disciplina) {
            // Disciplina não encontrada, redireciona para a listagem
            setMensagem('erro', 'Disciplina não encontrada.');
            redirect('disciplinas.php');
        }

        try {
            // Inicia uma transação
            $db->beginTransaction();

            // Exclui a disciplina
            $db->delete('disciplinas', 'id = ?', [$id]);

            // Registra o log
            registrarLog(
                'disciplinas',
                'excluir',
                "Disciplina ID: {$id} excluída",
                $id,
                'disciplinas'
            );

            // Confirma a transação
            $db->commit();

            setMensagem('sucesso', 'Disciplina excluída com sucesso.');
        } catch (Exception $e) {
            // Desfaz a transação em caso de erro
            $db->rollBack();

            // Erro ao excluir
            setMensagem('erro', 'Erro ao excluir a disciplina: ' . $e->getMessage());
        }

        // Redireciona para a listagem
        redirect('disciplinas.php');
        break;

    // ============================================================
    // CADASTRAR PROFESSOR - AJAX PARA CRIAÇÃO RÁPIDA
    // ============================================================
    case 'cadastrar_professor':
        // Cadastra um novo professor via AJAX
        if (!isPost()) {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Método não permitido']);
            exit;
        }

        $nome = $_POST['nome'] ?? '';
        $email = $_POST['email'] ?? '';
        $formacao = $_POST['formacao'] ?? '';

        if (empty($nome)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Nome é obrigatório']);
            exit;
        }

        try {
            $dados_professor = [
                'nome' => $nome,
                'email' => $email,
                'formacao' => $formacao,
                'status' => 'ativo',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $professor_id = $db->insert('professores', $dados_professor);

            echo json_encode([
                'success' => true,
                'professor' => [
                    'id' => $professor_id,
                    'nome' => $nome
                ]
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erro ao cadastrar professor: ' . $e->getMessage()]);
        }
        exit;

    // ============================================================
    // VISUALIZAR DISCIPLINA - DETALHES COMPLETOS
    // ============================================================
    case 'visualizar':
        // Exibe os detalhes de uma disciplina
        $id = $_GET['id'] ?? 0;

        // Busca a disciplina pelo ID
        $sql = "SELECT d.*,
                       c.nome as curso_nome,
                       p.nome as professor_nome
                FROM disciplinas d
                LEFT JOIN cursos c ON d.curso_id = c.id
                LEFT JOIN professores p ON d.professor_padrao_id = p.id
                WHERE d.id = ?";
        $disciplina = executarConsulta($db, $sql, [$id], []);

        if (!$disciplina) {
            // Disciplina não encontrada, redireciona para a listagem
            setMensagem('erro', 'Disciplina não encontrada.');
            redirect('disciplinas.php');
        }

        $titulo_pagina = 'Detalhes da Disciplina';
        $view = 'visualizar';
        break;

    // ============================================================
    // BUSCAR DISCIPLINAS - PESQUISA AVANÇADA
    // ============================================================
    case 'buscar':
        // Busca disciplinas por termo
        $termo = $_GET['termo'] ?? '';
        $campo = $_GET['campo'] ?? 'nome';
        $status = $_GET['status'] ?? 'todos';
        $curso_id = $_GET['curso_id'] ?? null;

        // Se não houver nenhum filtro, mantém a busca mas sem filtros
        // Não redirecionamos para permitir que o formulário funcione mesmo sem termo

        // Define os campos permitidos para busca
        $campos_permitidos = ['nome', 'codigo', 'id_legado'];

        if (!in_array($campo, $campos_permitidos)) {
            $campo = 'nome';
        }

        // Monta a consulta SQL
        $where = [];
        $params = [];

        if (!empty($termo)) {
            switch ($campo) {
                case 'nome':
                    $where[] = "d.nome LIKE ?";
                    $params[] = "%{$termo}%";
                    break;
                case 'codigo':
                    $where[] = "d.codigo LIKE ?";
                    $params[] = "%{$termo}%";
                    break;
                case 'id_legado':
                    $where[] = "d.id_legado LIKE ?";
                    $params[] = "%{$termo}%";
                    break;
            }
        }

        if ($status !== 'todos') {
            $where[] = "d.status = ?";
            $params[] = $status;
        }

        if (!empty($curso_id)) {
            $where[] = "d.curso_id = ?";
            $params[] = $curso_id;
        }

        // Monta a cláusula WHERE
        $whereClause = "WHERE " . implode(" AND ", $where);

        // Consulta principal
        $sql = "SELECT d.*,
                       c.nome as curso_nome,
                       p.nome as professor_nome
                FROM disciplinas d
                LEFT JOIN cursos c ON d.curso_id = c.id
                LEFT JOIN professores p ON d.professor_padrao_id = p.id
                {$whereClause}
                ORDER BY d.nome ASC";
        $disciplinas = executarConsultaAll($db, $sql, $params);

        // Carrega os cursos para o filtro
        $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
        $cursos = executarConsultaAll($db, $sql);

        $titulo_pagina = 'Resultado da Busca';
        $view = 'listar';

        // Garante que as variáveis necessárias estejam disponíveis na view
        if (!isset($status)) {
            $status = 'todos';
        }

        if (!isset($curso_id)) {
            $curso_id = null;        }
        break;

    // ============================================================
    // BUSCAR TURMAS DISPONÍVEIS - AJAX PARA MODAL DE VINCULAÇÃO
    // ============================================================
    case 'buscar_turmas_disponiveis':
        header('Content-Type: application/json');        $disciplina_id = $_GET['disciplina_id'] ?? 0;        try {
            // Verifica se a tabela turma_disciplinas existe
            $sql_check = "SHOW TABLES LIKE 'turma_disciplinas'";
            $tabela_existe = executarConsulta($db, $sql_check);

            if ($tabela_existe) {
                // Busca turmas e verifica quais já estão vinculadas
                $sql = "SELECT
                            t.id,
                            t.nome,
                            c.nome as curso_nome,
                            CASE WHEN td.id IS NOT NULL THEN 1 ELSE 0 END as ja_vinculada
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        LEFT JOIN turma_disciplinas td ON t.id = td.turma_id AND td.disciplina_id = ? AND td.status = 'ativo'
                        WHERE t.status IN ('ativo', 'em_andamento')
                        ORDER BY t.nome ASC";

                $turmas = executarConsultaAll($db, $sql, [$disciplina_id]);
            } else {
                // Se não existe a tabela, mostra todas as turmas como disponíveis
                $sql = "SELECT
                            t.id,
                            t.nome,
                            c.nome as curso_nome,
                            0 as ja_vinculada
                        FROM turmas t
                        LEFT JOIN cursos c ON t.curso_id = c.id
                        WHERE t.status IN ('ativo', 'em_andamento')
                        ORDER BY t.nome ASC";

                $turmas = executarConsultaAll($db, $sql);
            }

            echo json_encode([
                'success' => true,
                'turmas' => $turmas
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao buscar turmas: ' . $e->getMessage()
            ]);
        }
        exit;

    // ============================================================
    // BUSCAR PROFESSORES - AJAX PARA MODAL DE VINCULAÇÃO
    // ============================================================
    case 'buscar_professores':
        header('Content-Type: application/json');

        try {
            $sql = "SELECT id, nome, titulacao
                    FROM professores
                    WHERE status = 'ativo'
                    ORDER BY nome ASC
                    LIMIT 100";

            $professores = executarConsultaAll($db, $sql);

            echo json_encode([
                'success' => true,
                'professores' => $professores
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao buscar professores: ' . $e->getMessage()
            ]);
        }
        exit;

    // ============================================================
    // VINCULAR DISCIPLINA A TURMAS - AJAX PARA MODAL
    // ============================================================
    case 'vincular_disciplina_turmas':
        header('Content-Type: application/json');

        if (!isPost()) {
            echo json_encode(['success' => false, 'message' => 'Método não permitido']);
            exit;
        }

        $disciplina_id = $_POST['disciplina_id'] ?? 0;
        $turmas_selecionadas = $_POST['turmas'] ?? [];
        $professor_id = $_POST['professor_id'] ?? null;
        $carga_horaria_turma = $_POST['carga_horaria_turma'] ?? null;
        $periodo_turma = $_POST['periodo_turma'] ?? null;

        if (empty($disciplina_id) || empty($turmas_selecionadas)) {
            echo json_encode(['success' => false, 'message' => 'Disciplina e turmas são obrigatórias']);
            exit;
        }        try {
            // Verifica se a tabela turma_disciplinas existe
            $sql_check = "SHOW TABLES LIKE 'turma_disciplinas'";
            $tabela_existe = executarConsulta($db, $sql_check);

            if (!$tabela_existe) {
                echo json_encode(['success' => false, 'message' => 'Tabela turma_disciplinas não existe. Execute o script de criação primeiro.']);
                exit;
            }

            // Busca dados da disciplina
            $sql = "SELECT nome, carga_horaria, professor_padrao_id FROM disciplinas WHERE id = ?";
            $disciplina = executarConsulta($db, $sql, [$disciplina_id]);

            if (!$disciplina) {
                echo json_encode(['success' => false, 'message' => 'Disciplina não encontrada']);
                exit;
            }

            $db->beginTransaction();

            $vinculacoes_criadas = 0;
            $vinculacoes_existentes = 0;            foreach ($turmas_selecionadas as $turma_id) {
                // Verifica se já existe vinculação
                $sql_check = "SELECT id FROM turma_disciplinas WHERE turma_id = ? AND disciplina_id = ?";
                $existe = executarConsulta($db, $sql_check, [$turma_id, $disciplina_id]);

                if ($existe) {
                    $vinculacoes_existentes++;
                    continue;
                }

                // Busca próxima ordem para a turma
                $sql_ordem = "SELECT COALESCE(MAX(ordem), 0) + 1 as proxima_ordem FROM turma_disciplinas WHERE turma_id = ?";
                $ordem_result = executarConsulta($db, $sql_ordem, [$turma_id]);
                $ordem = $ordem_result['proxima_ordem'] ?? 1;

                // Cria a vinculação
                $dados_vinculacao = [
                    'turma_id' => $turma_id,
                    'disciplina_id' => $disciplina_id,
                    'professor_id' => $professor_id ?: $disciplina['professor_padrao_id'],
                    'carga_horaria_turma' => $carga_horaria_turma ?: $disciplina['carga_horaria'],
                    'periodo_turma' => $periodo_turma,
                    'ordem' => $ordem,
                    'status' => 'ativo',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $db->insert('turma_disciplinas', $dados_vinculacao);
                $vinculacoes_criadas++;
            }

            $db->commit();

            $message = "Disciplina vinculada com sucesso! ";
            $message .= "Criadas: $vinculacoes_criadas vinculações";
            if ($vinculacoes_existentes > 0) {
                $message .= ", $vinculacoes_existentes já existiam";
            }

            echo json_encode([
                'success' => true,
                'message' => $message,
                'vinculacoes_criadas' => $vinculacoes_criadas,
                'vinculacoes_existentes' => $vinculacoes_existentes
            ]);

        } catch (Exception $e) {
            $db->rollback();
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao vincular disciplina: ' . $e->getMessage()
            ]);
        }
        exit;

    // ============================================================
    // BUSCAR TURMAS POR CURSO - AJAX PARA FORMULÁRIO
    // ============================================================
    case 'buscar_turmas_por_curso':
        header('Content-Type: application/json');

        $curso_id = $_GET['curso_id'] ?? 0;

        if (empty($curso_id)) {
            echo json_encode(['success' => false, 'message' => 'Curso ID é obrigatório']);
            exit;
        }

        try {
            $sql = "SELECT t.id, t.nome,
                           (SELECT COUNT(*) FROM matriculas m WHERE m.turma_id = t.id) as total_alunos
                    FROM turmas t
                    WHERE t.curso_id = ? AND t.status IN ('ativo', 'em_andamento')
                    ORDER BY t.nome ASC";

            $turmas = $db->fetchAll($sql, [$curso_id]);            // Adiciona informação se a turma já está selecionada (para edição)
            $disciplina_id = $_GET['disciplina_id'] ?? null;
            if ($disciplina_id) {
                $sql_selecionadas = "SELECT turma_id FROM turma_disciplinas WHERE disciplina_id = ?";
                $turmas_selecionadas = $db->fetchAll($sql_selecionadas, [$disciplina_id]);
                $ids_selecionadas = array_column($turmas_selecionadas, 'turma_id');

                foreach ($turmas as &$turma) {
                    $turma['ja_selecionada'] = in_array($turma['id'], $ids_selecionadas);
                }
            } else {
                foreach ($turmas as &$turma) {
                    $turma['ja_selecionada'] = false;
                }
            }

            echo json_encode([
                'success' => true,
                'turmas' => $turmas
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao buscar turmas: ' . $e->getMessage()
            ]);
        }
        exit;

    // ============================================================
    // LISTAR DISCIPLINAS - LISTAGEM PAGINADA COM FILTROS
    // ============================================================
    case 'listar':
    default:
        // === CONFIGURAÇÃO DE PAGINAÇÃO ===
        $status = $_GET['status'] ?? 'todos';
        $curso_id = $_GET['curso_id'] ?? null;
        $ordenar = $_GET['ordenar'] ?? null;
        $pagina = isset($_GET['pagina']) ? max(1, (int)$_GET['pagina']) : 1;
        $por_pagina = 20;
        $offset = ($pagina - 1) * $por_pagina;

        // === CONSTRUÇÃO DE FILTROS ===
        $where = [];
        $params = [];

        if ($status !== 'todos') {
            $where[] = "d.status = ?";
            $params[] = $status;
        }

        if (!empty($curso_id)) {
            $where[] = "d.curso_id = ?";
            $params[] = $curso_id;
        }

        $whereClause = '';
        if (!empty($where)) {
            $whereClause = "WHERE " . implode(" AND ", $where);
        }

        // === DEFINIÇÃO DE ORDENAÇÃO ===
        $orderBy = "d.nome ASC";
        if ($ordenar === 'recentes') {
            $orderBy = "d.created_at DESC";
        }

        // === CONSULTA PRINCIPAL COM PAGINAÇÃO ===
        try {
            $sql = "SELECT d.*,
                           c.nome as curso_nome,
                           p.nome as professor_nome
                    FROM disciplinas d
                    LEFT JOIN cursos c ON d.curso_id = c.id
                    LEFT JOIN professores p ON d.professor_padrao_id = p.id
                    {$whereClause}
                    ORDER BY {$orderBy}
                    LIMIT {$offset}, {$por_pagina}";

            error_log("Executando consulta principal: " . $sql);
            error_log("Parâmetros: " . json_encode($params));
            
            $disciplinas = executarConsultaAll($db, $sql, $params);
            error_log("Disciplinas encontradas: " . count($disciplinas));

        } catch (Exception $e) {
            error_log("Erro na consulta principal: " . $e->getMessage());
            $disciplinas = [];
        }

        // === CONTAGEM TOTAL PARA PAGINAÇÃO ===
        try {
            $sql_count = "SELECT COUNT(*) as total
                          FROM disciplinas d
                          {$whereClause}";
            $resultado = executarConsulta($db, $sql_count, $params);
            $total_disciplinas = $resultado['total'] ?? 0;
            
            error_log("Total de disciplinas para paginação: " . $total_disciplinas);
        } catch (Exception $e) {
            error_log("Erro ao contar disciplinas: " . $e->getMessage());
            $total_disciplinas = 0;
        }

        // === CÁLCULO DE PAGINAÇÃO ===
        $total_paginas = $total_disciplinas > 0 ? ceil($total_disciplinas / $por_pagina) : 1;
        
        // Ajusta página atual se estiver fora do range
        if ($pagina > $total_paginas && $total_paginas > 0) {
            $pagina = $total_paginas;
            $offset = ($pagina - 1) * $por_pagina;
            
            // Executa novamente a consulta com a página corrigida
            $sql = "SELECT d.*,
                           c.nome as curso_nome,
                           p.nome as professor_nome
                    FROM disciplinas d
                    LEFT JOIN cursos c ON d.curso_id = c.id
                    LEFT JOIN professores p ON d.professor_padrao_id = p.id
                    {$whereClause}
                    ORDER BY {$orderBy}
                    LIMIT {$offset}, {$por_pagina}";
            $disciplinas = executarConsultaAll($db, $sql, $params);
        }

        // === DADOS AUXILIARES ===
        // Carrega os cursos para o filtro
        $sql = "SELECT id, nome FROM cursos ORDER BY nome ASC";
        $cursos = executarConsultaAll($db, $sql);

        // === ESTATÍSTICAS DO DASHBOARD ===
        try {
            // Total de disciplinas por status
            $sql = "SELECT status, COUNT(*) as total FROM disciplinas GROUP BY status";
            $status_counts = executarConsultaAll($db, $sql);

            $total_ativas = 0;
            $total_inativas = 0;

            foreach ($status_counts as $status_count) {
                switch ($status_count['status']) {
                    case 'ativo':
                        $total_ativas = $status_count['total'];
                        break;
                    case 'inativo':
                        $total_inativas = $status_count['total'];
                        break;
                }
            }

            // Disciplinas recentes (dos últimos 30 dias)
            $sql = "SELECT COUNT(*) as total FROM disciplinas WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $recentes_result = executarConsulta($db, $sql);
            $total_recentes = $recentes_result['total'] ?? 0;

            // Busca as disciplinas mais recentes para exibir no dashboard
            $sql = "SELECT d.*,
                           c.nome as curso_nome,
                           p.nome as professor_nome
                    FROM disciplinas d
                    LEFT JOIN cursos c ON d.curso_id = c.id
                    LEFT JOIN professores p ON d.professor_padrao_id = p.id
                    ORDER BY d.created_at DESC LIMIT 5";
            $disciplinas_recentes = executarConsultaAll($db, $sql);

            // Busca os cursos com mais disciplinas
            $sql = "SELECT c.id, c.nome, COUNT(*) as total
                   FROM disciplinas d
                   JOIN cursos c ON d.curso_id = c.id
                   GROUP BY c.id
                   ORDER BY total DESC
                   LIMIT 5";
            $cursos_populares_raw = executarConsultaAll($db, $sql);

            // Calcula a porcentagem para cada curso popular
            $cursos_populares = [];
            if (!empty($cursos_populares_raw)) {
                $max_disciplinas = $cursos_populares_raw[0]['total'];

                foreach ($cursos_populares_raw as $curso) {
                    $curso['porcentagem'] = ($curso['total'] / $max_disciplinas) * 100;
                    $cursos_populares[] = $curso;
                }
            }
        } catch (Exception $e) {
            error_log('Erro ao buscar estatísticas para o dashboard: ' . $e->getMessage());
        }

        $titulo_pagina = 'Disciplinas';
        $view = 'listar';
        break;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <?php include '../includes/header.php'; ?>

            <!-- Main -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>

                        <div class="flex space-x-2">
                            <?php if ($view === 'listar'): ?>
                            <a href="disciplinas.php?action=nova" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i> Nova Disciplina
                            </a>
                            <?php endif; ?>

                            <?php if ($view === 'visualizar'): ?>
                            <a href="disciplinas.php?action=editar&id=<?php echo $disciplina['id']; ?>" class="btn-secondary">
                                <i class="fas fa-edit mr-2"></i> Editar
                            </a>
                            <a href="javascript:void(0);" onclick="confirmarExclusao(<?php echo $disciplina['id']; ?>)" class="btn-danger">
                                <i class="fas fa-trash mr-2"></i> Excluir
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (isset($mensagens_erro) && !empty($mensagens_erro)): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                        <ul class="list-disc list-inside">
                            <?php foreach ($mensagens_erro as $erro): ?>
                            <li><?php echo $erro; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['mensagem'])): ?>
                    <div class="bg-<?php echo $_SESSION['mensagem_tipo'] === 'sucesso' ? 'green' : 'red'; ?>-100 border-l-4 border-<?php echo $_SESSION['mensagem_tipo'] === 'sucesso' ? 'green' : 'red'; ?>-500 text-<?php echo $_SESSION['mensagem_tipo'] === 'sucesso' ? 'green' : 'red'; ?>-700 p-4 mb-6">
                        <?php echo $_SESSION['mensagem']; ?>
                    </div>
                    <?php
                    // Limpa a mensagem da sessão
                    unset($_SESSION['mensagem']);
                    unset($_SESSION['mensagem_tipo']);
                    endif;
                    ?>

                    <?php
                    // Inclui a view correspondente
                    switch ($view) {
                        case 'form':
                            include 'views/disciplinas/form.php';
                            break;
                        case 'visualizar':
                            include 'views/disciplinas/visualizar.php';
                            break;
                        case 'listar':
                        default:
                            include 'views/disciplinas/listar.php';
                            break;
                    }
                    ?>
                </div>
            </main>

            <!-- Footer -->
            <?php include '../includes/footer.php'; ?>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div id="modal-exclusao" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Confirmar Exclusão
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500" id="modal-message">
                                    Tem certeza que deseja excluir esta disciplina?
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <a href="#" id="btn-confirmar-exclusao" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Confirmar
                    </a>
                    <button type="button" onclick="fecharModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancelar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        function confirmarExclusao(id) {
            document.getElementById('btn-confirmar-exclusao').href = `disciplinas.php?action=excluir&id=${id}`;
            document.getElementById('modal-exclusao').classList.remove('hidden');
        }

        function fecharModal() {
            document.getElementById('modal-exclusao').classList.add('hidden');
        }
    </script>
</body>
</html>