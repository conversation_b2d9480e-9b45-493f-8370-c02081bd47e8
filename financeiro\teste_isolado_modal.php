<?php
/**
 * Teste simplificado para verificar problema de redirecionamento
 */

// Simula dados de conta
$conta = [
    'id' => 123,
    'descricao' => 'Mensalidade de <PERSON>',
    'valor' => 150.50,
    'status' => 'pendente'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Isolado - Modal Recebimento</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <h1 class="text-2xl font-bold mb-6">🔧 Teste Isolado - Modal Recebimento</h1>
    
    <!-- Status do teste -->
    <div id="status" class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
        <strong>Status:</strong> Aguardando teste...
    </div>
    
    <!-- Simula a estrutura da tabela exata -->
    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-green-600">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Descrição</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Valor</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($conta['descricao']); ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            Pendente
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <!-- Link de editar (que pode estar causando o problema) -->
                        <a href="contas_receber.php?action=editar&id=<?php echo $conta['id']; ?>"
                           class="text-green-600 hover:text-green-900 mr-3"
                           onclick="updateStatus('Link de EDITAR clicado - redirecionamento normal')">
                            <i class="fas fa-edit"></i>
                        </a>
                        
                        <!-- Botão de recebimento (que deveria abrir modal) -->
                        <button onclick="event.preventDefault(); event.stopPropagation(); updateStatus('Botão de RECEBIMENTO clicado - abrindo modal...'); abrirModalRecebimento(<?php echo $conta['id']; ?>, '<?php echo htmlspecialchars($conta['descricao']); ?>', <?php echo $conta['valor']; ?>)"
                                class="text-blue-600 hover:text-blue-900 mr-3"
                                title="Registrar Recebimento"
                                type="button">
                            <i class="fas fa-hand-holding-usd"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Teste adicional com botões isolados -->
    <div class="bg-white p-6 rounded-lg shadow mb-6">
        <h2 class="text-lg font-bold mb-4">🧪 Testes Isolados</h2>
        
        <div class="space-x-4">
            <a href="contas_receber.php?action=editar&id=<?php echo $conta['id']; ?>"
               class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
               onclick="updateStatus('Link direto de EDITAR clicado')">
                <i class="fas fa-edit mr-2"></i>Editar (Link Direto)
            </a>
            
            <button onclick="updateStatus('Botão direto de RECEBIMENTO clicado'); abrirModalRecebimento(<?php echo $conta['id']; ?>, '<?php echo htmlspecialchars($conta['descricao']); ?>', <?php echo $conta['valor']; ?>)"
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    type="button">
                <i class="fas fa-hand-holding-usd mr-2"></i>Receber (Botão Direto)
            </button>
        </div>
    </div>
    
    <!-- Log de eventos -->
    <div class="bg-gray-100 p-4 rounded">
        <h3 class="font-bold mb-2">📋 Log de Eventos:</h3>
        <div id="log" class="text-sm font-mono bg-black text-green-400 p-2 rounded min-h-32">
            Sistema iniciado...<br>
        </div>
    </div>

    <script>
    function updateStatus(message) {
        document.getElementById('status').innerHTML = '<strong>Status:</strong> ' + message;
        document.getElementById('log').innerHTML += new Date().toLocaleTimeString() + ' - ' + message + '<br>';
        console.log('STATUS:', message);
    }

    // Objeto Financeiro para funcionalidades modais
    const Financeiro = {
        Modal: {
            show: function(title, content, options = {}) {
                updateStatus('Modal sendo criado...');
                
                // Remove modal existente se houver
                const existingModal = document.getElementById('financeiro-modal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                const confirmText = options.confirmText || 'Confirmar';
                const onConfirm = options.onConfirm || '';
                
                const modalHTML = `
                    <div id="financeiro-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                            <div class="mt-3">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">${title}</h3>
                                <div class="mt-2">
                                    ${content}
                                </div>
                                <div class="items-center px-4 py-3 mt-4">
                                    <div class="flex justify-end space-x-2">
                                        <button id="modal-cancel" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                            Cancelar
                                        </button>
                                        <button id="modal-confirm" onclick="${onConfirm}" class="px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                            ${confirmText}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                updateStatus('Modal criado e exibido!');
                
                // Eventos de fechamento
                document.getElementById('modal-cancel').onclick = () => {
                    updateStatus('Modal cancelado pelo usuário');
                    document.getElementById('financeiro-modal').remove();
                };
                
                document.getElementById('financeiro-modal').onclick = (e) => {
                    if (e.target.id === 'financeiro-modal') {
                        updateStatus('Modal fechado (clique fora)');
                        document.getElementById('financeiro-modal').remove();
                    }
                };
            }
        }
    };

    function abrirModalRecebimento(id, descricao, valor) {
        updateStatus(`abrirModalRecebimento(${id}, "${descricao}", ${valor})`);
        
        // Simula contas bancárias
        const contasBancariasOptions = [
            {id: 1, nome: 'Banco do Brasil - CC'},
            {id: 2, nome: 'Itaú - Poupança'},
            {id: 3, nome: 'Caixa - CC'}
        ];
        
        let contasBancariasHtml = '<option value="">Selecione uma conta</option>';
        contasBancariasOptions.forEach(function(cb) {
            contasBancariasHtml += `<option value="${cb.id}">${cb.nome}</option>`;
        });
        
        updateStatus('Criando formulário modal...');
        
        const modal = Financeiro.Modal.show('Registrar Recebimento', `
            <form id="form-recebimento" method="POST" action="contas_receber.php">
                <input type="hidden" name="action" value="receber">
                <input type="hidden" name="id" value="${id}">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Conta</label>
                        <p class="text-sm text-gray-900 font-medium">${descricao}</p>
                        <p class="text-sm text-gray-600">R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Data do Recebimento *</label>
                        <input type="date" name="data_recebimento" value="${new Date().toISOString().split('T')[0]}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Conta Bancária *</label>
                        <select name="conta_bancaria_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            ${contasBancariasHtml}
                        </select>
                    </div>
                </div>
            </form>
        `, {
            confirmText: 'Registrar Recebimento',
            onConfirm: `
                updateStatus('Processando formulário...');
                const form = document.getElementById('form-recebimento');
                if (form.checkValidity()) {
                    updateStatus('✅ Formulário válido - enviaria para processamento');
                    updateStatus('🚫 Submit desabilitado para teste');
                    // form.submit(); // Desabilitado para teste
                } else {
                    updateStatus('❌ Formulário inválido');
                    form.reportValidity();
                }
            `
        });
    }

    // Log todos os cliques na página
    document.addEventListener('click', function(e) {
        updateStatus(`Click detectado em: ${e.target.tagName} (${e.target.className})`);
    });
    </script>
</body>
</html>
