<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    session_start();

    // Registrar logout no log do sistema (opcional)
    if (isset($_SESSION['aluno_id'])) {
        error_log("Portal Aluno - Logout: Aluno ID " . $_SESSION['aluno_id']);
    }

    // Destruir todas as variáveis de sessão
    $_SESSION = array();

    // Destruir o cookie de sessão se existir
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destruir a sessão
    session_destroy();

    // Redirecionar para a página de login principal com mensagem de sucesso
    header('Location: ../login.php?logout=success');
    exit;

} catch (Exception $e) {
    echo "<h1>Erro no Logout</h1>";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><a href='../login.php'>Ir para Login</a></p>";
    exit;
}
?>
