<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    echo "<h2>TESTE DE FOLHA DE PAGAMENTO</h2>";
    
    // Teste 1: Buscar funcionários
    echo "<h3>1. Funcionários Ativos:</h3>";
    $funcionarios = $db->fetchAll("SELECT * FROM funcionarios WHERE status = 'ativo' ORDER BY nome");
    if (empty($funcionarios)) {
        echo "❌ Nenhum funcionário ativo encontrado<br>";
        // Verificar se existem funcionários com outros status
        $todosFuncionarios = $db->fetchAll("SELECT status, COUNT(*) as total FROM funcionarios GROUP BY status");
        foreach ($todosFuncionarios as $func) {
            echo "- Status '{$func['status']}': {$func['total']} funcionários<br>";
        }
    } else {
        echo "✅ " . count($funcionarios) . " funcionário(s) ativo(s) encontrado(s)<br>";
        foreach ($funcionarios as $func) {
            echo "- {$func['nome']} ({$func['cargo']}) - R$ {$func['salario']}<br>";
        }
    }
    
    echo "<hr>";
    echo "<h2>TESTE DE MENSALIDADES</h2>";
    
    // Teste 2: Buscar alunos
    echo "<h3>2. Alunos:</h3>";
    $alunos = $db->fetchAll("
        SELECT a.id, a.nome, c.nome as curso_nome 
        FROM alunos a 
        LEFT JOIN cursos c ON a.curso_id = c.id 
        ORDER BY a.nome 
        LIMIT 5
    ");
    if (empty($alunos)) {
        echo "❌ Nenhum aluno encontrado<br>";
    } else {
        echo "✅ " . count($alunos) . " aluno(s) encontrado(s) (mostrando 5 primeiros)<br>";
        foreach ($alunos as $aluno) {
            echo "- {$aluno['nome']} ({$aluno['curso_nome']})<br>";
        }
    }
    
    // Teste 3: Buscar cursos
    echo "<h3>3. Cursos:</h3>";
    $cursos = $db->fetchAll("SELECT id, nome, valor_mensalidade FROM cursos ORDER BY nome LIMIT 5");
    if (empty($cursos)) {
        echo "❌ Nenhum curso encontrado<br>";
    } else {
        echo "✅ " . count($cursos) . " curso(s) encontrado(s) (mostrando 5 primeiros)<br>";
        foreach ($cursos as $curso) {
            echo "- {$curso['nome']} - R$ {$curso['valor_mensalidade']}<br>";
        }
    }
    
    // Teste 4: Buscar mensalidades existentes
    echo "<h3>4. Mensalidades Existentes:</h3>";
    $mensalidades = $db->fetchAll("
        SELECT ma.*, a.nome as aluno_nome, c.nome as curso_nome
        FROM mensalidades_alunos ma
        JOIN alunos a ON ma.aluno_id = a.id
        LEFT JOIN cursos c ON ma.curso_id = c.id
        ORDER BY ma.created_at DESC
        LIMIT 5
    ");
    if (empty($mensalidades)) {
        echo "❌ Nenhuma mensalidade encontrada<br>";
    } else {
        echo "✅ " . count($mensalidades) . " mensalidade(s) encontrada(s) (mostrando 5 mais recentes)<br>";
        foreach ($mensalidades as $mens) {
            echo "- {$mens['aluno_nome']} ({$mens['curso_nome']}) - R$ {$mens['valor']} - {$mens['status']}<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>ERRO:</h2>";
    echo $e->getMessage();
}
?>
