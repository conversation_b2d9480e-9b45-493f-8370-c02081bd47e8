<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Incluir configuração básica
require_once 'includes/config.php';

// Conectar ao banco usando função simples
try {
    $pdo = getConnection();
} catch (Exception $e) {
    die("Erro de conexão com banco: " . $e->getMessage());
}

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        return $_SESSION['user_nome'] ?? $_SESSION['user_name'] ?? 'Usuário';
    }
}

// Título da página
$pageTitle = "Gestão de Funcionários";

// Tentar carregar os dados do dashboard de funcionários
try {
    // Dados para o Dashboard
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM funcionarios WHERE status = 'ativo'");
    $totalFuncionarios = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM funcionarios WHERE status = 'inativo'");
    $totalInativos = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COALESCE(SUM(salario), 0) as total FROM funcionarios WHERE status = 'ativo'");
    $folhaMensalTotal = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM funcionarios WHERE MONTH(data_nascimento) = MONTH(CURDATE()) AND status = 'ativo'");
    $aniversariantesMes = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM funcionarios WHERE MONTH(data_admissao) = MONTH(CURDATE()) AND YEAR(data_admissao) = YEAR(CURDATE()) AND status = 'ativo'");
    $admissoesMes = $stmt->fetch();
    
    // Funcionários por departamento
    $stmt = $pdo->query("
        SELECT 
            COALESCE(departamento, 'Não Informado') as departamento,
            COUNT(*) as total
        FROM funcionarios 
        WHERE status = 'ativo'
        GROUP BY departamento
        ORDER BY total DESC
        LIMIT 5
    ");
    $funcionariosPorDepartamento = $stmt->fetchAll();
    
    // Funcionários por cargo
    $stmt = $pdo->query("
        SELECT 
            cargo,
            COUNT(*) as total,
            AVG(salario) as salario_medio
        FROM funcionarios 
        WHERE status = 'ativo'
        GROUP BY cargo
        ORDER BY total DESC
        LIMIT 10
    ");
    $funcionariosPorCargo = $stmt->fetchAll();
    
    // Funcionários recém admitidos
    $stmt = $pdo->query("
        SELECT id, nome, cargo, departamento, data_admissao, salario, status
        FROM funcionarios 
        WHERE status = 'ativo'
        ORDER BY data_admissao DESC
        LIMIT 5
    ");
    $funcionariosRecentes = $stmt->fetchAll();
    
} catch (Exception $e) {
    $_SESSION['msg_error'] = "Erro ao carregar dados: " . $e->getMessage();
    // Definir arrays vazios para evitar erros
    $totalFuncionarios = ['total' => 0];
    $folhaMensalTotal = ['total' => 0];
    $aniversariantesMes = ['total' => 0];
    $admissoesMes = ['total' => 0];
    $funcionariosPorDepartamento = [];
    $funcionariosPorCargo = [];
    $funcionariosRecentes = [];
}

// Incluir o cabeçalho e sidebar padronizados
include_once 'includes/header_padronizado_novo.php';
include_once 'includes/sidebar_padronizado.php';
?>

<div class="main-content" style="padding-left: 0.25rem; margin-left: 16rem;">
    <!-- Header da página -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900"><?php echo $pageTitle; ?></h1>
            <p class="text-gray-600">Dashboard de Recursos Humanos</p>
        </div>
        <div class="flex space-x-2">
            <a href="funcionarios_novo.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Novo Funcionário
            </a>
        </div>
    </div>

    <!-- Dashboard Content - Cards de Funcionários -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Card Total de Funcionários Ativos -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-green-100 mr-4">
                <i class="fas fa-users text-green-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Funcionários Ativos</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $totalFuncionarios['total'] ?? 0; ?>
                </p>
            </div>
        </div>

        <!-- Card Folha de Pagamento -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-blue-100 mr-4">
                <i class="fas fa-money-check-alt text-blue-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Folha Mensal</p>
                <p class="text-xl font-bold text-gray-800">
                    R$ <?php echo number_format($folhaMensalTotal['total'] ?? 0, 2, ',', '.'); ?>
                </p>
            </div>
        </div>

        <!-- Card Aniversariantes do Mês -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-purple-100 mr-4">
                <i class="fas fa-birthday-cake text-purple-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Aniversariantes do Mês</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $aniversariantesMes['total'] ?? 0; ?>
                </p>
            </div>
        </div>

        <!-- Card Admissões do Mês -->
        <div class="bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200">
            <div class="p-3 rounded-full bg-yellow-100 mr-4">
                <i class="fas fa-user-plus text-yellow-600 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">Admissões do Mês</p>
                <p class="text-xl font-bold text-gray-800">
                    <?php echo $admissoesMes['total'] ?? 0; ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Gráficos e Tabelas -->
    <div class="grid grid-cols-1 lg:grid-cols-7 gap-6 mb-8">
        <!-- Gráfico Funcionários por Departamento -->
        <div class="lg:col-span-4 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Funcionários por Departamento</h2>
            <div class="h-80">
                <canvas id="chartDepartamentos"></canvas>
            </div>
        </div>
        
        <!-- Funcionários Recentes -->
        <div class="lg:col-span-3 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Funcionários Recentes</h2>
            <?php if (empty($funcionariosRecentes)): ?>
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-user-tie text-3xl mb-2"></i>
                <p>Nenhum funcionário registrado</p>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left pb-3 text-sm font-medium text-gray-500">Nome</th>
                            <th class="text-left pb-3 text-sm font-medium text-gray-500">Cargo</th>
                            <th class="text-left pb-3 text-sm font-medium text-gray-500">Admissão</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($funcionariosRecentes as $funcionario): ?>
                        <tr class="border-b border-gray-100">
                            <td class="py-3 text-sm">
                                <div class="font-medium text-gray-900"><?php echo htmlspecialchars($funcionario['nome']); ?></div>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($funcionario['departamento'] ?? 'Não informado'); ?></div>
                            </td>
                            <td class="py-3 text-sm text-gray-600">
                                <?php echo htmlspecialchars($funcionario['cargo']); ?>
                            </td>
                            <td class="py-3 text-sm text-gray-600">
                                <?php echo date('d/m/Y', strtotime($funcionario['data_admissao'])); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-center">
                <a href="funcionarios_lista.php" class="text-sm text-green-600 hover:text-green-700 font-medium">Ver todos os funcionários</a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Tabela de Funcionários por Cargo -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Distribuição por Cargo</h2>
        <?php if (empty($funcionariosPorCargo)): ?>
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-user-tie text-3xl mb-2"></i>
            <p>Nenhum dado disponível</p>
        </div>
        <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left pb-3 text-sm font-medium text-gray-500">Cargo</th>
                        <th class="text-center pb-3 text-sm font-medium text-gray-500">Quantidade</th>
                        <th class="text-right pb-3 text-sm font-medium text-gray-500">Salário Médio</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($funcionariosPorCargo as $cargo): ?>
                    <tr class="border-b border-gray-100">
                        <td class="py-3 text-sm font-medium text-gray-900">
                            <?php echo htmlspecialchars($cargo['cargo']); ?>
                        </td>
                        <td class="py-3 text-sm text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <?php echo $cargo['total']; ?> funcionário<?php echo $cargo['total'] > 1 ? 's' : ''; ?>
                            </span>
                        </td>
                        <td class="py-3 text-sm text-right text-gray-600">
                            R$ <?php echo number_format($cargo['salario_medio'], 2, ',', '.'); ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>

    <!-- Ações Rápidas -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Ações Rápidas</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <a href="funcionarios_novo.php" class="flex items-center p-3 bg-green-50 text-green-700 rounded-md hover:bg-green-100 transition-colors">
                <div class="p-2 rounded-full bg-green-100">
                    <i class="fas fa-user-plus text-green-600"></i>
                </div>
                <span class="ml-3 font-medium">Novo Funcionário</span>
            </a>
            
            <a href="funcionarios_lista.php" class="flex items-center p-3 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                <div class="p-2 rounded-full bg-blue-100">
                    <i class="fas fa-users text-blue-600"></i>
                </div>
                <span class="ml-3 font-medium">Listar Funcionários</span>
            </a>
            
            <a href="folha_pagamento.php" class="flex items-center p-3 bg-purple-50 text-purple-700 rounded-md hover:bg-purple-100 transition-colors">
                <div class="p-2 rounded-full bg-purple-100">
                    <i class="fas fa-money-check-alt text-purple-600"></i>
                </div>
                <span class="ml-3 font-medium">Folha de Pagamento</span>
            </a>
            
            <a href="relatorios_rh.php" class="flex items-center p-3 bg-yellow-50 text-yellow-700 rounded-md hover:bg-yellow-100 transition-colors">
                <div class="p-2 rounded-full bg-yellow-100">
                    <i class="fas fa-chart-pie text-yellow-600"></i>
                </div>
                <span class="ml-3 font-medium">Relatórios RH</span>
            </a>
            
            <a href="funcionarios_aniversariantes.php" class="flex items-center p-3 bg-pink-50 text-pink-700 rounded-md hover:bg-pink-100 transition-colors">
                <div class="p-2 rounded-full bg-pink-100">
                    <i class="fas fa-birthday-cake text-pink-600"></i>
                </div>
                <span class="ml-3 font-medium">Aniversariantes</span>
            </a>
        </div>
    </div>
</div>

<!-- Script para o gráfico -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dados para o gráfico de departamentos
    const departamentos = <?php echo json_encode(array_column($funcionariosPorDepartamento ?? [], 'departamento')); ?>;
    const quantidades = <?php echo json_encode(array_column($funcionariosPorDepartamento ?? [], 'total')); ?>;
    
    if (departamentos.length > 0) {
        const ctx = document.getElementById('chartDepartamentos').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: departamentos,
                datasets: [{
                    data: quantidades,
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(147, 51, 234, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(107, 114, 128, 0.8)'
                    ],
                    borderColor: [
                        'rgb(34, 197, 94)',
                        'rgb(59, 130, 246)',
                        'rgb(147, 51, 234)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(107, 114, 128)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>

<?php include 'includes/footer_padronizado.php'; ?>
