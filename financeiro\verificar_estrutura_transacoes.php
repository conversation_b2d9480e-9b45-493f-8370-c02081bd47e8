<?php
/**
 * Verificar estrutura da tabela transacoes_financeiras
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';

$db = Database::getInstance();

echo "=== ESTRUTURA DA TABELA TRANSACOES_FINANCEIRAS ===\n\n";

try {
    // Verificar se a tabela existe
    $exists = $db->fetchOne("SHOW TABLES LIKE 'transacoes_financeiras'");
    
    if ($exists) {
        echo "✅ Tabela existe\n\n";
        
        // Obter estrutura da tabela
        $structure = $db->fetchAll("DESCRIBE transacoes_financeiras");
        
        echo "📋 CAMPOS DA TABELA:\n";
        foreach ($structure as $field) {
            echo "  - " . $field['Field'] . " (" . $field['Type'] . ")" . 
                 ($field['Null'] == 'NO' ? ' NOT NULL' : '') . 
                 ($field['Key'] == 'PRI' ? ' PRIMARY KEY' : '') . 
                 ($field['Default'] ? ' DEFAULT ' . $field['Default'] : '') . "\n";
        }
        
        // Verificar dados de exemplo
        echo "\n📊 DADOS DE EXEMPLO (primeiros 5 registros):\n";
        $sample = $db->fetchAll("SELECT * FROM transacoes_financeiras LIMIT 5");
        
        if (!empty($sample)) {
            foreach ($sample as $row) {
                echo "  ID: " . $row['id'] . "\n";
                foreach ($row as $key => $value) {
                    if ($key != 'id') {
                        echo "    $key: $value\n";
                    }
                }
                echo "  ---\n";
            }
        } else {
            echo "  (Nenhum registro encontrado)\n";
        }
        
    } else {
        echo "❌ Tabela NÃO existe\n";
        
        // Verificar se há tabelas similares
        echo "\n🔍 Procurando tabelas similares:\n";
        $tables = $db->fetchAll("SHOW TABLES LIKE '%financeira%'");
        if (!empty($tables)) {
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                echo "  - $tableName\n";
            }
        } else {
            echo "  Nenhuma tabela encontrada com 'financeira' no nome\n";
        }
        
        // Listar todas as tabelas
        echo "\n📋 TODAS AS TABELAS NO BANCO:\n";
        $allTables = $db->fetchAll("SHOW TABLES");
        foreach ($allTables as $table) {
            $tableName = array_values($table)[0];
            echo "  - $tableName\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
}

echo "\n============================================================\n";
echo "Verificação concluída em: " . date('d/m/Y H:i:s') . "\n";
?>
