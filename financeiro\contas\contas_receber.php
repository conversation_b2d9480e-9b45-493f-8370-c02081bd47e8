<?php
// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// TEMPORARIAMENTE REMOVIDAS AS RESTRIÇÕES DE LOGIN
// Comentado para permitir acesso livre ao módulo financeiro
/*
if (!isset($_SESSION['user_id'])) {
    echo '<h2>Acesso Restrito</h2>';
    echo '<p>Você precisa estar logado para acessar esta página.</p>';
    echo '<p><a href="../login.php">Fazer Login</a></p>';
    exit;
}
*/

// Incluir os arquivos de configuração e funções
require_once 'includes/config.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        // Tenta usar Auth::getUserName() primeiro
        if (method_exists('Auth', 'getUserName')) {
            return Auth::getUserName() ?? 'Usuário';
        }
        // Fallback para sessão direta
        return $_SESSION['user_nome'] ?? $_SESSION['user_name'] ?? 'Usuário';
    }
}

// Variáveis de controle
$statusFilter = $_GET['status'] ?? '';
$dataInicio = $_GET['data_inicio'] ?? '';
$dataFim = $_GET['data_fim'] ?? '';
$busca = $_GET['busca'] ?? '';

// Processar ações (adicionar, receber, cancelar, excluir)
if ($_POST['action'] ?? '') {
    try {
        $db = Database::getInstance();
        $action = $_POST['action'];
        
        switch ($action) {
            case 'adicionar':
                $stmt = $db->prepare("INSERT INTO contas_receber (descricao, valor, data_vencimento, cliente_nome, observacoes, status) VALUES (?, ?, ?, ?, ?, 'pendente')");
                $stmt->execute([
                    $_POST['descricao'],
                    $_POST['valor'],
                    $_POST['data_vencimento'],
                    $_POST['cliente_nome'],
                    $_POST['observacoes']
                ]);
                $_SESSION['msg_success'] = "Conta a receber adicionada com sucesso!";
                break;
                
            case 'receber':
                $stmt = $db->prepare("UPDATE contas_receber SET status = 'recebido', data_recebimento = ?, forma_recebimento = ?, observacoes = CONCAT(COALESCE(observacoes, ''), ' - ', ?) WHERE id = ?");
                $stmt->execute([
                    $_POST['data_recebimento'],
                    $_POST['forma_recebimento'],
                    $_POST['observacoes_recebimento'],
                    $_POST['conta_id']
                ]);
                $_SESSION['msg_success'] = "Conta recebida com sucesso!";
                break;
                
            case 'cancelar':
                $stmt = $db->prepare("UPDATE contas_receber SET status = 'cancelado', observacoes = CONCAT(COALESCE(observacoes, ''), ' - Cancelado: ', ?) WHERE id = ?");
                $stmt->execute([
                    $_POST['motivo_cancelamento'],
                    $_POST['conta_id']
                ]);
                $_SESSION['msg_success'] = "Conta cancelada com sucesso!";
                break;
                
            case 'excluir':
                $stmt = $db->prepare("DELETE FROM contas_receber WHERE id = ?");
                $stmt->execute([$_POST['conta_id']]);
                $_SESSION['msg_success'] = "Conta excluída com sucesso!";
                break;
        }
        
        header('Location: contas_receber.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['msg_error'] = "Erro ao processar ação: " . $e->getMessage();
    }
}

// Obter dados para o dashboard
try {
    $db = Database::getInstance();
    
    // Cards do dashboard
    $contasPendentes = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_receber
        WHERE status = 'pendente'
    ");
    
    $contasRecebidas = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_receber
        WHERE status = 'recebido' AND MONTH(data_recebimento) = MONTH(CURDATE()) AND YEAR(data_recebimento) = YEAR(CURDATE())
    ");
    
    $contasVencidas = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_receber
        WHERE status = 'pendente' AND data_vencimento < CURDATE()
    ");
    
    $contasVencemHoje = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_receber
        WHERE status = 'pendente' AND data_vencimento = CURDATE()
    ");
    
    // Dados para o gráfico de evolução dos recebimentos (últimos 6 meses)
    $recebimentosMensais = $db->fetchAll("
        SELECT MONTH(data_recebimento) as mes, YEAR(data_recebimento) as ano, 
        COUNT(*) as total_contas, SUM(valor) as total_valor
        FROM contas_receber
        WHERE status = 'recebido' AND data_recebimento >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY YEAR(data_recebimento), MONTH(data_recebimento)
        ORDER BY ano ASC, mes ASC
    ");
    
    // Maiores clientes (por valor recebido)
    $maioresClientes = $db->fetchAll("
        SELECT cliente_nome, COUNT(*) as total_contas, SUM(valor) as total_valor
        FROM contas_receber
        WHERE status = 'recebido' AND cliente_nome IS NOT NULL
        GROUP BY cliente_nome
        ORDER BY total_valor DESC
        LIMIT 5
    ");
    
    // Construir WHERE clause para a listagem
    $whereConditions = [];
    $params = [];
    
    if ($statusFilter) {
        $whereConditions[] = "status = ?";
        $params[] = $statusFilter;
    }
    
    if ($dataInicio) {
        $whereConditions[] = "data_vencimento >= ?";
        $params[] = $dataInicio;
    }
    
    if ($dataFim) {
        $whereConditions[] = "data_vencimento <= ?";
        $params[] = $dataFim;
    }
    
    if ($busca) {
        $whereConditions[] = "(descricao LIKE ? OR cliente_nome LIKE ?)";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
    }
    
    $whereClause = $whereConditions ? "WHERE " . implode(" AND ", $whereConditions) : "";
    
    // Contas a receber com filtros
    $contasReceber = $db->fetchAll("
        SELECT * FROM contas_receber
        $whereClause
        ORDER BY data_vencimento ASC, created_at DESC
    ", $params);
    
} catch (Exception $e) {
    $_SESSION['msg_error'] = "Erro ao carregar dados: " . $e->getMessage();
    $contasPendentes = ['total' => 0, 'valor_total' => 0];
    $contasRecebidas = ['total' => 0, 'valor_total' => 0];
    $contasVencidas = ['total' => 0, 'valor_total' => 0];
    $contasVencemHoje = ['total' => 0, 'valor_total' => 0];
    $recebimentosMensais = [];
    $maioresClientes = [];
    $contasReceber = [];
}

// Incluir o cabeçalho e sidebar padronizados
include_once 'includes/header_padronizado_novo.php';
include_once 'includes/sidebar_padronizado.php';
?>

<!-- CSS corrigido para layout perfeito -->
<style>
/* Reset específico para contas_receber.php */
.main-content {
    padding-top: 0 !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-top: 4rem !important; /* Espaço para header fixo */
}

.main-content .container {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
}

/* Container principal da página */
.page-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
}

/* Correção para gráficos */
.chart-container {
    height: 300px !important;
    position: relative;
    width: 100%;
    overflow: hidden;
}

.chart-container canvas {
    height: 100% !important;
    width: 100% !important;
    max-height: 300px !important;
}

/* Modais corrigidos */
.modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

.modal-content {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 100000 !important;
}

/* Responsividade melhorada */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0 !important;
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
    
    .page-container {
        padding: 1rem;
    }
    
    .lg\:col-span-2 {
        grid-column: span 1 !important;
    }
}

/* Botões com posicionamento correto */
.btn-primary {
    position: relative;
    z-index: 100;
}

/* Cards e grids */
.dashboard-grid {
    gap: 1.5rem;
}

/* Ocultar elementos escondidos */
.hidden {
    display: none !important;
}
</style>

<!-- Conteúdo da página -->
<div class="page-container">
    <!-- Header da página -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Contas a Receber</h1>
            <p class="text-gray-600">Gestão de Recebimentos e Clientes</p>
        </div>
        <div class="flex space-x-2">
            <button onclick="openModal('modalAdicionar')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors btn-primary">
                <i class="fas fa-plus mr-2"></i>Nova Conta a Receber
            </button>
        </div>
    </div>

    <!-- Dashboard Content - Cards -->
    <div class="dashboard-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Total Pendentes -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600"></i>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Total Pendentes</dt>
                    <dd class="text-lg font-medium text-gray-900"><?= $contasPendentes['total'] ?> contas</dd>
                    <dd class="text-sm text-gray-600">R$ <?= number_format($contasPendentes['valor_total'], 2, ',', '.') ?></dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Recebidas no Mês -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600"></i>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Recebidas no Mês</dt>
                    <dd class="text-lg font-medium text-gray-900"><?= $contasRecebidas['total'] ?> contas</dd>
                    <dd class="text-sm text-gray-600">R$ <?= number_format($contasRecebidas['valor_total'], 2, ',', '.') ?></dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Vencidas -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Vencidas</dt>
                    <dd class="text-lg font-medium text-gray-900"><?= $contasVencidas['total'] ?> contas</dd>
                    <dd class="text-sm text-gray-600">R$ <?= number_format($contasVencidas['valor_total'], 2, ',', '.') ?></dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Vencem Hoje -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-calendar-day text-orange-600"></i>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Vencem Hoje</dt>
                    <dd class="text-lg font-medium text-gray-900"><?= $contasVencemHoje['total'] ?> contas</dd>
                    <dd class="text-sm text-gray-600">R$ <?= number_format($contasVencemHoje['valor_total'], 2, ',', '.') ?></dd>
                </dl>
            </div>
        </div>
    </div>
</div>

    <!-- Gráficos e Listas -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Gráfico de Evolução dos Recebimentos -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Evolução dos Recebimentos</h3>
                <span class="text-sm text-gray-500">Últimos 6 meses</span>
            </div>
            <div class="chart-container">
                <canvas id="graficoRecebimentos"></canvas>
            </div>
        </div>

    <!-- Maiores Clientes -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Maiores Clientes</h3>
        <div class="space-y-4">
            <?php if (empty($maioresClientes)): ?>
                <p class="text-gray-500 text-sm">Nenhum cliente encontrado.</p>
            <?php else: ?>
                <?php foreach ($maioresClientes as $cliente): ?>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($cliente['cliente_nome']) ?></p>
                            <p class="text-xs text-gray-500"><?= $cliente['total_contas'] ?> contas</p>
                        </div>
                        <span class="text-sm font-medium text-green-600">
                            R$ <?= number_format($cliente['total_valor'], 2, ',', '.') ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Ações Rápidas e Filtros -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h2 class="text-xl font-semibold text-gray-900">Contas a Receber</h2>
        <div class="flex flex-wrap gap-2">
            <button onclick="openModal('modalAdicionar')" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Nova Conta
            </button>
        </div>
    </div>

    <!-- Filtros -->
    <form method="GET" class="mt-4 grid grid-cols-1 md:grid-cols-5 gap-4">
        <select name="status" class="form-select rounded-md border-gray-300">
            <option value="">Todos os Status</option>
            <option value="pendente" <?= $statusFilter === 'pendente' ? 'selected' : '' ?>>Pendente</option>
            <option value="recebido" <?= $statusFilter === 'recebido' ? 'selected' : '' ?>>Recebido</option>
            <option value="cancelado" <?= $statusFilter === 'cancelado' ? 'selected' : '' ?>>Cancelado</option>
        </select>
        
        <input type="date" name="data_inicio" placeholder="Data Início" value="<?= htmlspecialchars($dataInicio) ?>" class="form-input rounded-md border-gray-300">
        
        <input type="date" name="data_fim" placeholder="Data Fim" value="<?= htmlspecialchars($dataFim) ?>" class="form-input rounded-md border-gray-300">
        
        <input type="text" name="busca" placeholder="Buscar por descrição ou cliente" value="<?= htmlspecialchars($busca) ?>" class="form-input rounded-md border-gray-300">
        
        <div class="flex gap-2">
            <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Filtrar
            </button>
            <a href="contas_receber.php" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                <i class="fas fa-times mr-2"></i>Limpar
            </a>
        </div>
    </form>
</div>

<!-- Tabela de Contas a Receber -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($contasReceber)): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            Nenhuma conta a receber encontrada.
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($contasReceber as $conta): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($conta['descricao']) ?></div>
                                <?php if ($conta['observacoes']): ?>
                                    <div class="text-xs text-gray-500"><?= htmlspecialchars($conta['observacoes']) ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?= htmlspecialchars($conta['cliente_nome'] ?? 'Não informado') ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                R$ <?= number_format($conta['valor'], 2, ',', '.') ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?= date('d/m/Y', strtotime($conta['data_vencimento'])) ?>
                                <?php if ($conta['status'] === 'pendente' && $conta['data_vencimento'] < date('Y-m-d')): ?>
                                    <span class="text-red-600 text-xs">(Vencida)</span>
                                <?php elseif ($conta['status'] === 'pendente' && $conta['data_vencimento'] === date('Y-m-d')): ?>
                                    <span class="text-orange-600 text-xs">(Vence hoje)</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php if ($conta['status'] === 'pendente'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pendente
                                    </span>
                                <?php elseif ($conta['status'] === 'recebido'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Recebido
                                    </span>
                                <?php elseif ($conta['status'] === 'cancelado'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Cancelado
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium">
                                <div class="flex space-x-2">
                                    <?php if ($conta['status'] === 'pendente'): ?>
                                        <button onclick="abrirModalReceber(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['descricao']) ?>', <?= $conta['valor'] ?>)" 
                                                class="text-green-600 hover:text-green-900 transition-colors">
                                            <i class="fas fa-check" title="Receber"></i>
                                        </button>
                                        <button onclick="abrirModalCancelar(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['descricao']) ?>')" 
                                                class="text-yellow-600 hover:text-yellow-900 transition-colors">
                                            <i class="fas fa-ban" title="Cancelar"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button onclick="abrirModalExcluir(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['descricao']) ?>')" 
                                            class="text-red-600 hover:text-red-900 transition-colors">
                                        <i class="fas fa-trash" title="Excluir"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

</div> <!-- Fim do container principal -->

<!-- Modal Adicionar Conta -->
<div id="modalAdicionar" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Nova Conta a Receber</h3>
            <button type="button" onclick="closeModal('modalAdicionar')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="adicionar">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                <input type="text" name="descricao" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Cliente</label>
                <input type="text" name="cliente_nome" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Valor</label>
                <input type="number" name="valor" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Data de Vencimento</label>
                <input type="date" name="data_vencimento" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                <textarea name="observacoes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalAdicionar')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Adicionar
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Receber Conta -->
<div id="modalReceber" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Receber Conta</h3>
            <button type="button" onclick="closeModal('modalReceber')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="receber">
            <input type="hidden" name="conta_id" id="receber_conta_id">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                <p id="receber_conta_info" class="text-sm text-gray-600 bg-gray-50 p-3 rounded-md"></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Data de Recebimento</label>
                <input type="date" name="data_recebimento" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Forma de Recebimento</label>
                <select name="forma_recebimento" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    <option value="">Selecione...</option>
                    <option value="dinheiro">Dinheiro</option>
                    <option value="pix">PIX</option>
                    <option value="cartao_credito">Cartão de Crédito</option>
                    <option value="cartao_debito">Cartão de Débito</option>
                    <option value="transferencia">Transferência Bancária</option>
                    <option value="boleto">Boleto</option>
                    <option value="cheque">Cheque</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Observações do Recebimento</label>
                <textarea name="observacoes_recebimento" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalReceber')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                    Confirmar Recebimento
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Cancelar Conta -->
<div id="modalCancelar" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Cancelar Conta</h3>
            <button type="button" onclick="closeModal('modalCancelar')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="cancelar">
            <input type="hidden" name="conta_id" id="cancelar_conta_id">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                <p id="cancelar_conta_info" class="text-sm text-gray-600 bg-gray-50 p-3 rounded-md"></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Motivo do Cancelamento</label>
                <textarea name="motivo_cancelamento" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500" placeholder="Descreva o motivo do cancelamento..."></textarea>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalCancelar')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Voltar
                </button>
                <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
                    Confirmar Cancelamento
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Excluir Conta -->
<div id="modalExcluir" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Excluir Conta</h3>
            <button type="button" onclick="closeModal('modalExcluir')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="excluir">
            <input type="hidden" name="conta_id" id="excluir_conta_id">
            
            <div>
                <p class="text-sm text-gray-600 mb-3">Tem certeza que deseja excluir esta conta?</p>
                <p id="excluir_conta_info" class="text-sm text-gray-800 bg-gray-50 p-3 rounded-md"></p>
                <p class="text-xs text-red-600 mt-2">Esta ação não pode ser desfeita.</p>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalExcluir')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Confirmar Exclusão
                </button>
            </div>
        </form>
    </div>
</div>
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Excluir Conta</h3>
            <form method="POST">
                <input type="hidden" name="action" value="excluir">
                <input type="hidden" name="conta_id" id="excluir_conta_id">
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600">Tem certeza que deseja excluir esta conta?</p>
                    <p id="excluir_conta_info" class="text-sm text-gray-800 bg-gray-50 p-3 rounded-md mt-2"></p>
                    <p class="text-xs text-red-600 mt-2">Esta ação não pode ser desfeita.</p>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('modalExcluir')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Cancelar
                    </button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                        Confirmar Exclusão
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Funções para controle dos modais - versão melhorada
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('hidden');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Fechar modal ao clicar no overlay
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modalId);
        }
    });
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('hidden');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Fechar modais com ESC
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = ['modalAdicionar', 'modalReceber', 'modalCancelar', 'modalExcluir'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal && !modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});

function abrirModalReceber(id, descricao, valor) {
    document.getElementById('receber_conta_id').value = id;
    document.getElementById('receber_conta_info').innerHTML = `<strong>${descricao}</strong><br>Valor: R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
    document.querySelector('input[name="data_recebimento"]').value = new Date().toISOString().split('T')[0];
    openModal('modalReceber');
}

function abrirModalCancelar(id, descricao) {
    document.getElementById('cancelar_conta_id').value = id;
    document.getElementById('cancelar_conta_info').innerHTML = `<strong>${descricao}</strong>`;
    openModal('modalCancelar');
}

function abrirModalExcluir(id, descricao) {
    document.getElementById('excluir_conta_id').value = id;
    document.getElementById('excluir_conta_info').innerHTML = `<strong>${descricao}</strong>`;
    openModal('modalExcluir');
}

// Fechar modal ao clicar fora
window.onclick = function(event) {
    const modals = ['modalAdicionar', 'modalReceber', 'modalCancelar', 'modalExcluir'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
}

// Gráfico de Evolução dos Recebimentos
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('graficoRecebimentos').getContext('2d');
    
    // Preparar dados do PHP para JavaScript
    const recebimentosData = <?= json_encode($recebimentosMensais) ?>;
    
    // Processando dados para o gráfico
    const labels = [];
    const valores = [];
    const quantidades = [];
    
    // Criar array dos últimos 6 meses
    const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    const dataAtual = new Date();
    
    for (let i = 5; i >= 0; i--) {
        const data = new Date(dataAtual.getFullYear(), dataAtual.getMonth() - i, 1);
        const mes = data.getMonth() + 1;
        const ano = data.getFullYear();
        
        labels.push(`${meses[data.getMonth()]}/${ano}`);
        
        // Procurar dados correspondentes
        const dadosMes = recebimentosData.find(item => item.mes == mes && item.ano == ano);
        valores.push(dadosMes ? parseFloat(dadosMes.total_valor) : 0);
        quantidades.push(dadosMes ? parseInt(dadosMes.total_contas) : 0);
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Valor Recebido (R$)',
                data: valores,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Quantidade de Contas',
                data: quantidades,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Valor (R$)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'R$ ' + value.toLocaleString('pt-BR');
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Quantidade'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return `Valor: R$ ${context.parsed.y.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
                            } else {
                                return `Quantidade: ${context.parsed.y} contas`;
                            }
                        }
                    }
                }
            }
        }
    });
});
</script>

</div> <!-- Fechamento do page-container -->

<?php include_once 'includes/footer_padronizado.php'; ?>
