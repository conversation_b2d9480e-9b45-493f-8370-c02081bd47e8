<?php
/**
 * Teste do Dashboard Financeiro
 * Verifica se todas as funcionalidades estão operacionais
 */

echo "=== TESTE DO DASHBOARD FINANCEIRO ===\n";
echo "Data/Hora: " . date('d/m/Y H:i:s') . "\n\n";

try {
    // Inclui os arquivos necessários
    require_once '../includes/init.php';
    require_once '../includes/Database.php';
    
    $db = Database::getInstance();
    echo "✅ Conexão com banco estabelecida com sucesso\n";
    
    // Teste 1: Verificar tabelas necessárias
    echo "\n📋 TESTE 1: Verificando tabelas necessárias...\n";
    
    $tabelas = [
        'transacoes_financeiras',
        'mensalidades_alunos',
        'folha_pagamento',
        'folha_pagamento_itens',
        'alunos',
        'funcionarios'
    ];
    
    foreach ($tabelas as $tabela) {
        try {
            $result = $db->fetchOne("SHOW TABLES LIKE '$tabela'");
            if ($result) {
                echo "  ✅ Tabela '$tabela' existe\n";
            } else {
                echo "  ❌ Tabela '$tabela' NÃO existe\n";
            }
        } catch (Exception $e) {
            echo "  ❌ Erro ao verificar tabela '$tabela': " . $e->getMessage() . "\n";
        }
    }
    
    // Teste 2: Teste das funções do dashboard
    echo "\n💰 TESTE 2: Testando funções financeiras...\n";
    
    // Função obterSaldoBancos
    try {
        $contas = $db->fetchAll("
            SELECT 
                COALESCE(cb.nome_banco, 'Não informado') as banco,
                SUM(CASE WHEN t.tipo = 'receita' THEN t.valor ELSE -t.valor END) as saldo
            FROM transacoes_financeiras t
            LEFT JOIN contas_bancarias cb ON t.conta_bancaria_id = cb.id
            WHERE t.status = 'efetivada'
            GROUP BY t.conta_bancaria_id, cb.nome_banco
            ORDER BY saldo DESC
        ");
        echo "  ✅ Saldo por banco: " . count($contas) . " contas encontradas\n";
        foreach ($contas as $conta) {
            echo "    - " . ($conta['banco'] ?: 'Sem banco') . ": R$ " . number_format($conta['saldo'], 2, ',', '.') . "\n";
        }
    } catch (Exception $e) {
        // Fallback caso não exista tabela contas_bancarias
        try {
            $contas = $db->fetchAll("
                SELECT 
                    CONCAT('Conta ', COALESCE(conta_bancaria_id, 'N/A')) as banco,
                    SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END) as saldo
                FROM transacoes_financeiras
                WHERE status = 'efetivada'
                GROUP BY conta_bancaria_id
                ORDER BY saldo DESC
            ");
            echo "  ✅ Saldo por banco (fallback): " . count($contas) . " contas encontradas\n";
            foreach ($contas as $conta) {
                echo "    - " . ($conta['banco'] ?: 'Sem banco') . ": R$ " . number_format($conta['saldo'], 2, ',', '.') . "\n";
            }
        } catch (Exception $e2) {
            echo "  ❌ Erro ao obter saldo dos bancos: " . $e2->getMessage() . "\n";
        }
    }
    
    // Função obterIndicadoresGerais
    try {
        $saldoTotal = $db->fetchOne("
            SELECT SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END) as total
            FROM transacoes_financeiras 
            WHERE status = 'efetivada'
        ");
        echo "  ✅ Saldo total: R$ " . number_format($saldoTotal['total'] ?? 0, 2, ',', '.') . "\n";
        
        $receitasMes = $db->fetchOne("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM transacoes_financeiras 
            WHERE tipo = 'receita' 
            AND MONTH(data_transacao) = MONTH(CURRENT_DATE())
            AND YEAR(data_transacao) = YEAR(CURRENT_DATE())
            AND status = 'efetivada'
        ");
        echo "  ✅ Receitas do mês: R$ " . number_format($receitasMes['total'], 2, ',', '.') . "\n";
        
        $despesasMes = $db->fetchOne("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM transacoes_financeiras 
            WHERE tipo = 'despesa' 
            AND MONTH(data_transacao) = MONTH(CURRENT_DATE())
            AND YEAR(data_transacao) = YEAR(CURRENT_DATE())
            AND status = 'efetivada'
        ");
        echo "  ✅ Despesas do mês: R$ " . number_format($despesasMes['total'], 2, ',', '.') . "\n";
        
        $resultado = $receitasMes['total'] - $despesasMes['total'];
        echo "  ✅ Resultado do mês: R$ " . number_format($resultado, 2, ',', '.') . "\n";
        
    } catch (Exception $e) {
        echo "  ❌ Erro ao obter indicadores gerais: " . $e->getMessage() . "\n";
    }
    
    // Teste 3: Fluxo de caixa
    echo "\n📊 TESTE 3: Testando fluxo de caixa...\n";
    
    try {
        $fluxo = $db->fetchAll("
            SELECT 
                DATE_FORMAT(data_transacao, '%Y-%m') as mes,
                SUM(CASE WHEN tipo = 'receita' THEN valor ELSE 0 END) as receitas,
                SUM(CASE WHEN tipo = 'despesa' THEN valor ELSE 0 END) as despesas
            FROM transacoes_financeiras 
            WHERE data_transacao >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            AND status = 'efetivada'
            GROUP BY DATE_FORMAT(data_transacao, '%Y-%m')
            ORDER BY mes DESC
            LIMIT 12
        ");
        echo "  ✅ Fluxo de caixa: " . count($fluxo) . " meses de dados\n";
        foreach ($fluxo as $mes) {
            echo "    - " . $mes['mes'] . ": R$ " . number_format($mes['receitas'], 2, ',', '.') . " / R$ " . number_format($mes['despesas'], 2, ',', '.') . "\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erro ao obter fluxo de caixa: " . $e->getMessage() . "\n";
    }
    
    // Teste 4: Mensalidades e folha
    echo "\n👥 TESTE 4: Testando mensalidades e folha...\n";
    
    try {
        $mensalidadesPendentes = $db->fetchOne("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM mensalidades_alunos 
            WHERE status = 'pendente'
        ");
        echo "  ✅ Mensalidades pendentes: R$ " . number_format($mensalidadesPendentes['total'], 2, ',', '.') . "\n";
        
        $folhaMes = $db->fetchOne("
            SELECT COALESCE(SUM(valor_total), 0) as total
            FROM folha_pagamento 
            WHERE mes_referencia = MONTH(CURRENT_DATE())
            AND ano_referencia = YEAR(CURRENT_DATE())
        ");
        echo "  ✅ Folha do mês: R$ " . number_format($folhaMes['total'], 2, ',', '.') . "\n";
        
    } catch (Exception $e) {
        echo "  ❌ Erro ao obter dados de mensalidades/folha: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎯 CONCLUSÃO: Dashboard financeiro testado!\n";
    echo "Todos os componentes principais estão funcionais.\n";
    echo "Acesse: http://localhost/reinandus/financeiro/index.php\n";
    
} catch (Exception $e) {
    echo "❌ ERRO CRÍTICO: " . $e->getMessage() . "\n";
    echo "Não foi possível conectar ao banco de dados.\n";
}

echo "\n============================================================\n";
echo "Teste concluído em: " . date('d/m/Y H:i:s') . "\n";
?>
