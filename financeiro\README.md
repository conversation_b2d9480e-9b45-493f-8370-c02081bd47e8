# Módulo Financeiro - Sistema Faciência ERP

## Visão Geral

O módulo Financeiro é responsável pela gestão financeira completa da instituição, incluindo controle de contas a pagar e receber, fluxo de caixa, boletos, folha de pagamento, contabilidade e relatórios financeiros.

## Estrutura de Pastas

### 📁 **core/** - Núcleo do Sistema Financeiro
Contém os arquivos principais de configuração e dashboard:

- **dashboard_financeiro.php** - Dashboard principal do módulo financeiro
- **configuracoes.php** - Configurações gerais do sistema financeiro
- **acesso_direto.php** - Acesso direto ao módulo

### 📁 **contas/** - Gestão de Contas
Módulos para controle de contas financeiras:

- **contas_pagar.php** - Gestão de contas a pagar
- **contas_receber.php** - Gestão de contas a receber
- **contas_bancarias.php** - Cadastro e controle de contas bancárias
- **reconciliacao.php** - Reconciliação bancária

### 📁 **transacoes/** - Transações Financeiras
Controle de movimentações financeiras:

- **transacoes_financeiras.php** - Registro de transações
- **fluxo_caixa.php** - Controle de fluxo de caixa
- **lancamentos_contabeis.php** - Lançamentos contábeis

### 📁 **boletos/** - Sistema de Boletos e Cobrança
Gestão completa de boletos e mensalidades:

- **boletos.php** - Gestão de boletos
- **boleto_pdf.php** - Geração de boletos em PDF
- **cobranca_polos.php** - Cobrança específica por polos
- **mensalidades.php** - Controle de mensalidades

### 📁 **rh/** - Recursos Humanos
Gestão de pessoal e folha de pagamento:

- **funcionarios.php** - Cadastro de funcionários
- **folha_pagamento.php** - Processamento da folha de pagamento

### 📁 **contabilidade/** - Contabilidade
Módulos contábeis e plano de contas:

- **plano_contas_gerencial.php** - Plano de contas gerencial
- **categorias.php** - Categorias financeiras
- **centros_custo.php** - Centros de custo
- **demonstrativos.php** - Demonstrativos contábeis

### 📁 **relatorios/** - Relatórios Financeiros
Sistema de relatórios e análises:

- **relatorios.php** - Relatórios financeiros diversos

### 📁 **pagamentos/** - Sistema de Pagamentos
Gestão de pagamentos e integrações:

- **pagamentos.php** - Processamento de pagamentos
- **integrador.php** - Integração com sistemas externos

### 📁 **includes/** - Arquivos de Inclusão
Bibliotecas e funções compartilhadas:

- **PlanoContasGerencial.php** - Classe do plano de contas
- **RelatorioManager.php** - Gerenciador de relatórios
- **SaldoManager.php** - Gerenciador de saldos
- **boleto_functions.php** - Funções para boletos
- **config.php** - Configurações do módulo
- **init.php** - Inicialização do sistema
- **header.php**, **footer.php**, **sidebar.php** - Layout
- **integrador_class.php** - Classe de integração

### 📁 **assets/** - Recursos Estáticos
- **css/** - Folhas de estilo específicas do financeiro
- **js/** - Scripts JavaScript do módulo

### 📁 **ajax/** - Endpoints AJAX
Arquivos para requisições assíncronas:

- **boleto_detalhes.php** - Detalhes de boletos
- **buscar_alunos.php** - Busca de alunos
- **excluir_boleto.php** - Exclusão de boletos
- **indicadores_rapidos.php** - Indicadores em tempo real
- **obter_indicadores.php** - Obtenção de indicadores

### 📁 **sql/** - Scripts SQL
Scripts de banco de dados:

- **plano_contas_gerencial.sql** - Estrutura do plano de contas
- **tabelas_financeiro.sql** - Estrutura das tabelas financeiras

### 📁 **docs/** - Documentação
Documentos e manuais:

- **FaCiencia - Plano de Contas Gerencial.pdf** - Manual do plano de contas

## Arquivos Principais na Raiz

- **index.php** - Dashboard principal do módulo financeiro

## Funcionalidades Principais

### 1. **Gestão de Contas**
   - Contas a pagar com controle de vencimentos
   - Contas a receber com acompanhamento
   - Gestão de contas bancárias
   - Reconciliação bancária automática

### 2. **Sistema de Boletos**
   - Geração automática de boletos
   - Integração com bancos
   - Controle de mensalidades por polo
   - Cobrança automatizada

### 3. **Fluxo de Caixa**
   - Controle de entradas e saídas
   - Projeções financeiras
   - Análise de liquidez
   - Relatórios de movimentação

### 4. **Folha de Pagamento**
   - Cadastro de funcionários
   - Cálculo automático de salários
   - Controle de benefícios
   - Relatórios trabalhistas

### 5. **Contabilidade**
   - Plano de contas gerencial
   - Lançamentos contábeis
   - Centros de custo
   - Demonstrativos financeiros

### 6. **Relatórios e Análises**
   - Relatórios financeiros diversos
   - Indicadores de performance
   - Análises gerenciais
   - Exportação de dados

### 7. **Integrações**
   - Sistema bancário
   - Plataformas de pagamento
   - APIs externas
   - Sistemas acadêmicos

## Tecnologias Utilizadas

- **PHP** - Linguagem principal
- **MySQL** - Banco de dados
- **JavaScript/jQuery** - Frontend interativo
- **Bootstrap** - Framework CSS
- **Chart.js** - Gráficos e indicadores
- **APIs Bancárias** - Integração com bancos

## Indicadores e Dashboards

O módulo oferece indicadores em tempo real:

- **Contas a Pagar** - Vencimentos e pendências
- **Contas a Receber** - Recebimentos esperados
- **Fluxo de Caixa** - Posição financeira atual
- **Inadimplência** - Controle de atrasos
- **Faturamento** - Receitas por período

## Segurança e Controles

- **Controle de Acesso** - Permissões por usuário
- **Auditoria** - Log de todas as operações
- **Backup Automático** - Proteção de dados
- **Validações** - Consistência de informações

## Integrações Disponíveis

### **Sistemas Bancários:**
- Itaú
- Banco do Brasil
- Caixa Econômica
- Outros bancos via API

### **Plataformas de Pagamento:**
- PagSeguro
- PayPal
- Mercado Pago
- PIX

## Observações Importantes

- Todos os arquivos de teste, debug e temporários foram removidos
- A estrutura foi reorganizada para facilitar manutenção e análise externa
- Funcionalidades existentes foram preservadas
- Código documentado seguindo boas práticas
- Sistema preparado para auditoria e análise externa

## Configuração e Instalação

1. **Configurar Banco de Dados**
   - Executar scripts SQL da pasta `sql/`
   - Configurar conexão em `includes/config.php`

2. **Configurar Integrações**
   - APIs bancárias em `configuracoes.php`
   - Credenciais de pagamento

3. **Permissões de Usuário**
   - Definir acessos por módulo
   - Configurar níveis de permissão

## Suporte

Para dúvidas sobre funcionalidades específicas, consulte:
- Comentários nos arquivos PHP
- Documentação na pasta `docs/`
- Entre em contato com a equipe de desenvolvimento
