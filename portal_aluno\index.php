<?php
session_start();

// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar se o aluno está logado
if (!isset($_SESSION['aluno_id'])) {
    header('Location: ../login.php');
    exit;
}

require_once 'config/database.php';
require_once 'includes/functions.php';

$aluno_id = $_SESSION['aluno_id'];

try {
    // Buscar dados do aluno
    $aluno = getAlunoCompleto($aluno_id);
    if (!$aluno) {
        session_destroy();
        header('Location: login.php');
        exit;
    }

    // Buscar estatísticas do aluno
    $stats = getEstatisticasAluno($aluno_id);

    // Buscar últimas atividades
    $atividades = getUltimasAtividades($aluno_id, 5);

    // Buscar próximos vencimentos
    $proximosVencimentos = getProximosVencimentos($aluno_id, 3);
} catch (Exception $e) {
    echo "<h1>Erro no Portal do Aluno</h1>";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p><pre>" . $e->getTraceAsString() . "</pre>";
    exit;
}

$page_title = 'Portal do Aluno - Dashboard';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/portal.css">
    <link rel="stylesheet" href="assets/css/no-animations.css">
    <link rel="stylesheet" href="assets/css/no-animations.css">
    
    <!-- Configuração do Tailwind para cores roxas -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'purple': {
                            50: '#faf5ff',
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7c3aed',
                            800: '#6b21a8',
                            900: '#581c87',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="p-4 lg:p-6">
            <!-- Welcome Section -->
            <div class="mb-6">
                <div class="bg-gradient-to-r from-purple-600 to-purple-800 rounded-lg p-6 text-white">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">
                                Olá, <?php echo explode(' ', $aluno['nome'])[0]; ?>! 👋
                            </h1>
                            <p class="text-purple-100 text-lg">
                                Bem-vindo ao seu portal acadêmico
                            </p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold"><?php echo $stats['cursos_ativos']; ?></div>
                                <div class="text-sm text-purple-200">Cursos Ativos</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold"><?php echo $stats['mensalidades_pendentes']; ?></div>
                                <div class="text-sm text-purple-200">Pendências</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Situação Acadêmica -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-graduation-cap text-purple-500 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Situação</p>
                            <p class="text-lg font-semibold text-gray-900 capitalize">
                                <?php echo $aluno['status']; ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Mensalidades -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-credit-card text-green-500 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Financeiro</p>
                            <p class="text-lg font-semibold text-gray-900">
                                R$ <?php echo number_format($stats['valor_pendente'], 2, ',', '.'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Documentos -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-file-alt text-blue-500 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Documentos</p>
                            <p class="text-lg font-semibold text-gray-900">
                                <?php echo $stats['documentos_emitidos']; ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Último Acesso -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-orange-500 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Último Acesso</p>
                            <p class="text-sm font-semibold text-gray-900">
                                <?php echo date('d/m/Y H:i', strtotime($aluno['ultimo_acesso'] ?? 'now')); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Cursos Matriculados -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-book text-purple-500 mr-2"></i>
                                Meus Cursos
                            </h2>
                        </div>
                        <div class="p-6">
                            <?php if (!empty($stats['cursos'])): ?>
                                <div class="space-y-4">
                                    <?php foreach ($stats['cursos'] as $curso): ?>
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                            <div class="mb-2 sm:mb-0">
                                                <h3 class="font-semibold text-gray-900"><?php echo $curso['curso_nome']; ?></h3>
                                                <p class="text-sm text-gray-600">
                                                    Turma: <?php echo isset($curso['turma_nome']) ? htmlspecialchars($curso['turma_nome']) : 'Não informado'; ?> | 
                                                    Polo: <?php echo isset($curso['polo_nome']) ? htmlspecialchars($curso['polo_nome']) : 'Não informado'; ?>
                                                </p>
                                                <p class="text-xs text-gray-500">
                                                    Início: <?php echo date('d/m/Y', strtotime($curso['data_inicio'])); ?>
                                                </p>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    <?php echo $curso['status'] === 'ativo' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                    <?php echo ucfirst($curso['status']); ?>
                                                </span>
<a href="curso.php?id=<?php echo isset($curso['matricula_id']) ? $curso['matricula_id'] : ''; ?>" class="text-purple-600 hover:text-purple-800 text-sm font-medium">Ver Detalhes</a>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-book text-gray-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Nenhum curso encontrado</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Últimas Atividades -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-history text-purple-500 mr-2"></i>
                                Atividades Recentes
                            </h2>
                        </div>
                        <div class="p-6">
                            <?php if (!empty($atividades)): ?>
                                <div class="space-y-4">
                                    <?php foreach ($atividades as $atividade): ?>
                                    <div class="flex items-start space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-circle text-purple-500 text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900"><?php echo $atividade['descricao']; ?></p>
                                            <p class="text-xs text-gray-500">
                                                <?php echo date('d/m/Y H:i', strtotime($atividade['created_at'])); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Nenhuma atividade recente</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Próximos Vencimentos -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-calendar-alt text-purple-500 mr-2"></i>
                                Próximos Vencimentos
                            </h2>
                        </div>
                        <div class="p-6">
                            <?php if (!empty($proximosVencimentos)): ?>
                                <div class="space-y-3">
                                    <?php foreach ($proximosVencimentos as $vencimento): ?>
                                    <div class="border-l-4 border-purple-500 pl-4">
                                        <p class="text-sm font-medium text-gray-900">
                                            R$ <?php echo number_format($vencimento['valor'], 2, ',', '.'); ?>
                                        </p>
                                        <p class="text-xs text-gray-600">
                                            Vence em <?php echo date('d/m/Y', strtotime($vencimento['data_vencimento'])); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo $vencimento['descricao']; ?>
                                        </p>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mt-4">
                                    <a href="financeiro.php" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        Ver todas as mensalidades →
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-check-circle text-green-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Nenhum vencimento próximo</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Ações Rápidas -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-bolt text-purple-500 mr-2"></i>
                                Ações Rápidas
                            </h2>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-3">
                                <a href="documentos.php" 
                                   class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-colors">
                                    <i class="fas fa-file-download text-purple-500 text-xl mb-2"></i>
                                    <span class="text-sm font-medium text-gray-900">Documentos</span>
                                </a>
                                
                                <a href="financeiro.php" 
                                   class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-colors">
                                    <i class="fas fa-credit-card text-purple-500 text-xl mb-2"></i>
                                    <span class="text-sm font-medium text-gray-900">Financeiro</span>
                                </a>
                                
                                <a href="perfil.php" 
                                   class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-colors">
                                    <i class="fas fa-user-edit text-purple-500 text-xl mb-2"></i>
                                    <span class="text-sm font-medium text-gray-900">Perfil</span>
                                </a>
                                
                                <a href="suporte.php" 
                                   class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-colors">
                                    <i class="fas fa-headset text-purple-500 text-xl mb-2"></i>
                                    <span class="text-sm font-medium text-gray-900">Suporte</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <?php include 'includes/acoes_rapidas.php'; ?>

    <!-- Scripts -->
    <script src="assets/js/portal.js"></script>
</body>
</html>
