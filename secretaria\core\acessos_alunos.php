<?php
session_start();

require_once '../includes/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'criar_acesso':
                $aluno_id = (int)$_POST['aluno_id'];
                $email = trim($_POST['email']);
                $senha_temporaria = $_POST['senha_temporaria'] ?? '123456';
                
                // Verificar se o aluno existe
                $aluno = $db->fetchOne("SELECT * FROM alunos WHERE id = ?", [$aluno_id]);
                if (!$aluno) {
                    throw new Exception('Aluno não encontrado.');
                }

                // Verificar se já existe acesso
                $acesso_existente = $db->fetchOne("SELECT id FROM alunos_acesso WHERE aluno_id = ?", [$aluno_id]);
                if ($acesso_existente) {
                    throw new Exception('Aluno já possui acesso ao portal.');
                }
                
                // Criar acesso
                $senha_hash = password_hash($senha_temporaria, PASSWORD_DEFAULT);
                $db->insert('alunos_acesso', [
                    'aluno_id' => $aluno_id,
                    'email' => $email,
                    'senha' => $senha_hash,
                    'status' => 'ativo',
                    'primeiro_acesso' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                $message = "Acesso criado com sucesso para {$aluno['nome']}!";
                $messageType = 'success';
                break;
                
            case 'criar_multiplos':
                $alunos_selecionados = $_POST['alunos'] ?? [];
                $senha_padrao = $_POST['senha_padrao'] ?? '123456';
                $criados = 0;
                $erros = [];
                
                foreach ($alunos_selecionados as $aluno_id) {
                    try {
                        $aluno = $db->fetchOne("SELECT * FROM alunos WHERE id = ?", [$aluno_id]);
                        if (!$aluno || empty($aluno['email'])) {
                            $erros[] = "Aluno ID $aluno_id: sem email válido";
                            continue;
                        }

                        // Verificar se já existe
                        $existe = $db->fetchOne("SELECT id FROM alunos_acesso WHERE aluno_id = ?", [$aluno_id]);
                        if ($existe) {
                            $erros[] = "{$aluno['nome']}: já possui acesso";
                            continue;
                        }
                        
                        $senha_hash = password_hash($senha_padrao, PASSWORD_DEFAULT);
                        $db->insert('alunos_acesso', [
                            'aluno_id' => $aluno_id,
                            'email' => $aluno['email'],
                            'senha' => $senha_hash,
                            'status' => 'ativo',
                            'primeiro_acesso' => 1,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                        
                        $criados++;
                    } catch (Exception $e) {
                        $erros[] = "{$aluno['nome']}: " . $e->getMessage();
                    }
                }
                
                $message = "Criados $criados acessos.";
                if (!empty($erros)) {
                    $message .= " Erros: " . implode(', ', array_slice($erros, 0, 3));
                    if (count($erros) > 3) $message .= "... (+" . (count($erros) - 3) . " mais)";
                }
                $messageType = $criados > 0 ? 'success' : 'error';
                break;
                
            case 'resetar_senha':
                $acesso_id = (int)$_POST['acesso_id'];
                $nova_senha = $_POST['nova_senha'] ?? '123456';
                
                $senha_hash = password_hash($nova_senha, PASSWORD_DEFAULT);
                $db->update('alunos_acesso', [
                    'senha' => $senha_hash,
                    'primeiro_acesso' => 1,
                    'tentativas_login' => 0,
                    'bloqueado_ate' => null,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$acesso_id]);
                
                $message = 'Senha resetada com sucesso!';
                $messageType = 'success';
                break;
                
            case 'alterar_status':
                $acesso_id = (int)$_POST['acesso_id'];
                $novo_status = $_POST['novo_status'];
                
                $db->update('alunos_acesso', [
                    'status' => $novo_status,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$acesso_id]);
                
                $message = 'Status alterado com sucesso!';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = 'Erro: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Buscar acessos existentes com paginação
$filtro_status = $_GET['status'] ?? '';
$filtro_busca = $_GET['busca'] ?? '';

// Parâmetros de paginação
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, (int)$_GET['per_page'])) : 20; // Entre 10 e 100 registros
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];

if ($filtro_status) {
    $where_conditions[] = "aa.status = ?";
    $params[] = $filtro_status;
}

if ($filtro_busca) {
    $where_conditions[] = "(a.nome LIKE ? OR a.email LIKE ? OR aa.email LIKE ?)";
    $params[] = "%$filtro_busca%";
    $params[] = "%$filtro_busca%";
    $params[] = "%$filtro_busca%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Contar total de registros para paginação
try {
    $total_query = "
        SELECT COUNT(*) as total
        FROM alunos_acesso aa
        INNER JOIN alunos a ON aa.aluno_id = a.id
        $where_clause
    ";

    $total_result = $db->fetchOne($total_query, $params);
    $total_records = (int)$total_result['total'];
    $total_pages = $total_records > 0 ? ceil($total_records / $per_page) : 1;

    // Buscar registros da página atual
    $acessos = $db->fetchAll("
        SELECT aa.*, a.nome, a.email as email_aluno, a.status as status_aluno
        FROM alunos_acesso aa
        INNER JOIN alunos a ON aa.aluno_id = a.id
        $where_clause
        ORDER BY aa.created_at DESC
        LIMIT $per_page OFFSET $offset
    ", $params);
} catch (Exception $e) {
    // Em caso de erro, usar valores padrão
    $total_records = 0;
    $total_pages = 1;
    $acessos = [];
    $message = 'Erro ao carregar dados: ' . $e->getMessage();
    $messageType = 'error';
}

// Buscar alunos sem acesso
try {
    $alunos_sem_acesso = $db->fetchAll("
        SELECT a.id, a.nome, a.email, a.status
        FROM alunos a
        LEFT JOIN alunos_acesso aa ON a.id = aa.aluno_id
        WHERE aa.id IS NULL
        AND a.status = 'ativo'
        AND a.email IS NOT NULL
        AND a.email != ''
        ORDER BY a.nome
        LIMIT 50
    ");
} catch (Exception $e) {
    $alunos_sem_acesso = [];
}

$page_title = 'Acessos dos Alunos';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Secretaria</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'purple': {
                            50: '#faf5ff', 100: '#f3e8ff', 200: '#e9d5ff', 300: '#d8b4fe',
                            400: '#c084fc', 500: '#a855f7', 600: '#9333ea', 700: '#7c3aed',
                            800: '#6b21a8', 900: '#581c87',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100 bg-gray-50">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Header -->
        <?php include '../includes/header.php'; ?>

        <!-- Content -->
        <main class="p-6">
                <div class="container mx-auto max-w-7xl">
            <!-- Page Header -->
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900"><?php echo $page_title; ?></h1>
                <p class="text-gray-600">Gerenciar acessos dos alunos ao portal</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-md <?php echo $messageType === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                    <?php echo $message; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Ações Rápidas -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Criar Acesso Individual -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Criar Acesso Individual</h3>
                        <button onclick="openModal('modalCriarAcesso')" 
                                class="w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                            <i class="fas fa-user-plus mr-2"></i>
                            Criar Acesso
                        </button>
                    </div>

                    <!-- Criar Múltiplos Acessos -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Criar Múltiplos Acessos</h3>
                        <button onclick="openModal('modalCriarMultiplos')" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                            <i class="fas fa-users mr-2"></i>
                            Criar em Massa
                        </button>
                    </div>

                    <!-- Estatísticas -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Estatísticas</h3>
                        <div class="text-sm text-gray-600">
                            <p>Acessos ativos: <?php echo count(array_filter($acessos, function($a) { return $a['status'] === 'ativo'; })); ?></p>
                            <p>Sem acesso: <?php echo count($alunos_sem_acesso); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Todos</option>
                            <option value="ativo" <?php echo $filtro_status === 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                            <option value="inativo" <?php echo $filtro_status === 'inativo' ? 'selected' : ''; ?>>Inativo</option>
                            <option value="bloqueado" <?php echo $filtro_status === 'bloqueado' ? 'selected' : ''; ?>>Bloqueado</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
                        <input type="text" name="busca" value="<?php echo htmlspecialchars($filtro_busca); ?>"
                               placeholder="Nome ou email..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div class="flex items-end space-x-2">
                        <button type="submit" class="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-search mr-2"></i>Filtrar
                        </button>
                        <a href="acessos_alunos.php" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-times mr-2"></i>Limpar
                        </a>
                    </div>
                </form>

                <!-- Informações de Paginação -->
                <?php if ($total_records > 0): ?>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between items-center text-sm text-gray-600">
                        <div>
                            <strong><?php echo $total_records; ?></strong> acessos encontrados
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>Registros por página:</span>
                            <form method="GET" class="inline">
                                <select name="per_page" onchange="this.form.submit()"
                                        class="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-purple-500 focus:border-purple-500">
                                    <option value="10" <?php echo $per_page == 10 ? 'selected' : ''; ?>>10</option>
                                    <option value="20" <?php echo $per_page == 20 ? 'selected' : ''; ?>>20</option>
                                    <option value="50" <?php echo $per_page == 50 ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo $per_page == 100 ? 'selected' : ''; ?>>100</option>
                                </select>
                                <!-- Campos hidden para manter filtros -->
                                <input type="hidden" name="status" value="<?php echo htmlspecialchars($filtro_status); ?>">
                                <input type="hidden" name="busca" value="<?php echo htmlspecialchars($filtro_busca); ?>">
                            </form>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Lista de Acessos -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">
                            Acessos Existentes (<?php echo $total_records; ?> total)
                        </h2>
                        <div class="text-sm text-gray-500">
                            Página <?php echo $page; ?> de <?php echo max(1, $total_pages); ?>
                            (<?php echo count($acessos); ?> registros)
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aluno</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email de Acesso</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Último Acesso</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($acessos as $acesso): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                            <i class="fas fa-user text-purple-500"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($acesso['nome']); ?></div>
                                            <div class="text-sm text-gray-500">ID: <?php echo $acesso['aluno_id']; ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($acesso['email']); ?></div>
                                    <?php if ($acesso['primeiro_acesso']): ?>
                                    <div class="text-xs text-orange-600">Primeiro acesso pendente</div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php echo $acesso['status'] === 'ativo' ? 'bg-green-100 text-green-800' : 
                                                  ($acesso['status'] === 'bloqueado' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'); ?>">
                                        <?php echo ucfirst($acesso['status']); ?>
                                    </span>
                                    <?php if ($acesso['tentativas_login'] > 0): ?>
                                    <div class="text-xs text-red-600 mt-1">
                                        <?php echo $acesso['tentativas_login']; ?> tentativas
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $acesso['ultimo_acesso'] ? date('d/m/Y H:i', strtotime($acesso['ultimo_acesso'])) : 'Nunca'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="resetarSenha(<?php echo $acesso['id']; ?>, '<?php echo htmlspecialchars($acesso['nome']); ?>')" 
                                                class="text-purple-600 hover:text-purple-900" title="Resetar Senha">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        
                                        <button onclick="alterarStatus(<?php echo $acesso['id']; ?>, '<?php echo $acesso['status']; ?>', '<?php echo htmlspecialchars($acesso['nome']); ?>')" 
                                                class="text-blue-600 hover:text-blue-900" title="Alterar Status">
                                            <i class="fas fa-toggle-on"></i>
                                        </button>
                                        
                                        <a href="../portal_aluno/login.php" target="_blank" 
                                           class="text-green-600 hover:text-green-900" title="Testar Login">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    
                    <?php if (empty($acessos)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-users text-gray-300 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum acesso encontrado</h3>
                        <p class="text-gray-500">Crie acessos para os alunos começarem a usar o portal.</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Paginação -->
                <?php if ($total_pages > 1): ?>
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Mostrando <?php echo $offset + 1; ?> a <?php echo min($offset + $per_page, $total_records); ?>
                            de <?php echo $total_records; ?> resultados
                        </div>

                        <div class="flex space-x-1">
                            <?php
                            // Construir URL base para paginação
                            $base_url = 'acessos_alunos.php?';
                            $url_params = [];
                            if ($filtro_status) $url_params[] = 'status=' . urlencode($filtro_status);
                            if ($filtro_busca) $url_params[] = 'busca=' . urlencode($filtro_busca);
                            if ($per_page != 20) $url_params[] = 'per_page=' . $per_page; // Só adiciona se não for o padrão
                            $base_url .= implode('&', $url_params);
                            $base_url .= !empty($url_params) ? '&' : '';
                            ?>

                            <!-- Primeira página -->
                            <?php if ($page > 1): ?>
                            <a href="<?php echo $base_url; ?>page=1"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                            <a href="<?php echo $base_url; ?>page=<?php echo $page - 1; ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                                <i class="fas fa-angle-left"></i>
                            </a>
                            <?php endif; ?>

                            <!-- Páginas numeradas -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                            <a href="<?php echo $base_url; ?>page=<?php echo $i; ?>"
                               class="px-3 py-2 text-sm font-medium <?php echo $i === $page ? 'text-purple-600 bg-purple-50 border-purple-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'; ?> border">
                                <?php echo $i; ?>
                            </a>
                            <?php endfor; ?>

                            <!-- Próxima página -->
                            <?php if ($page < $total_pages): ?>
                            <a href="<?php echo $base_url; ?>page=<?php echo $page + 1; ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                                <i class="fas fa-angle-right"></i>
                            </a>
                            <a href="<?php echo $base_url; ?>page=<?php echo $total_pages; ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        
                </div>
            </main>
    </div>

    <!-- Modal Criar Acesso Individual -->
    <div id="modalCriarAcesso" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Criar Acesso Individual</h3>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="criar_acesso">
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Aluno</label>
                            <select name="aluno_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                                <option value="">Selecione um aluno</option>
                                <?php foreach ($alunos_sem_acesso as $aluno): ?>
                                <option value="<?php echo $aluno['id']; ?>">
                                    <?php echo htmlspecialchars($aluno['nome']); ?> (<?php echo htmlspecialchars($aluno['email']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email de Acesso</label>
                            <input type="email" name="email" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                   placeholder="Email para login">
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Senha Temporária</label>
                            <input type="text" name="senha_temporaria" value="123456" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeModal('modalCriarAcesso')" 
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-md">
                                Criar Acesso
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Criar Múltiplos Acessos -->
    <div id="modalCriarMultiplos" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Criar Múltiplos Acessos</h3>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="criar_multiplos">
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Senha Padrão</label>
                            <input type="text" name="senha_padrao" value="123456" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Selecionar Alunos (<?php echo count($alunos_sem_acesso); ?> disponíveis)
                            </label>
                            <div class="max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3">
                                <div class="mb-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="selectAll" class="mr-2">
                                        <span class="font-medium">Selecionar Todos</span>
                                    </label>
                                </div>
                                <hr class="mb-2">
                                <?php foreach ($alunos_sem_acesso as $aluno): ?>
                                <div class="mb-1">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="alunos[]" value="<?php echo $aluno['id']; ?>" class="mr-2 aluno-checkbox">
                                        <span class="text-sm">
                                            <?php echo htmlspecialchars($aluno['nome']); ?> 
                                            <span class="text-gray-500">(<?php echo htmlspecialchars($aluno['email']); ?>)</span>
                                        </span>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeModal('modalCriarMultiplos')" 
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md">
                                Criar Acessos
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        function resetarSenha(acessoId, nomeAluno) {
            const novaSenha = prompt(`Resetar senha para ${nomeAluno}.\nDigite a nova senha:`, '123456');
            if (novaSenha) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="resetar_senha">
                    <input type="hidden" name="acesso_id" value="${acessoId}">
                    <input type="hidden" name="nova_senha" value="${novaSenha}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function alterarStatus(acessoId, statusAtual, nomeAluno) {
            const novoStatus = statusAtual === 'ativo' ? 'inativo' : 'ativo';
            if (confirm(`Alterar status de ${nomeAluno} para ${novoStatus}?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="alterar_status">
                    <input type="hidden" name="acesso_id" value="${acessoId}">
                    <input type="hidden" name="novo_status" value="${novoStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Selecionar todos os alunos
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.aluno-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Atualizar "Selecionar Todos" baseado nos checkboxes individuais
        document.querySelectorAll('.aluno-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('.aluno-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.aluno-checkbox:checked');
                document.getElementById('selectAll').checked = allCheckboxes.length === checkedCheckboxes.length;
            });
        });
    </script>
</body>
</html>
