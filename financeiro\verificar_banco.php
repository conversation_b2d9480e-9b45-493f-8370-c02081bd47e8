<?php
header('Content-Type: text/plain; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'reinandus';

$conn = new mysqli($dbHost, $dbUser, $dbPass, $dbName);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Conexão com o banco de dados '$dbName' estabelecida com sucesso.\n\n";

$sql = "SHOW TABLES";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "Tabelas encontradas no banco de dados:\n";
    echo str_repeat("-", 40) . "\n";
    while($row = $result->fetch_row()) {
        echo "- " . $row[0] . "\n";
    }
    echo str_repeat("-", 40) . "\n";
    echo "Total de tabelas: " . $result->num_rows . "\n";
} else {
    echo "Nenhuma tabela encontrada no banco de dados.";
}

$conn->close();
?>
