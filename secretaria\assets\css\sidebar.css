/* Estilos específicos para o sidebar */
#sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 30; /* Reduzido para ficar atrás do conteúdo principal */
    transition: width 0.3s ease;
    display: flex;
    flex-direction: column;
}

#sidebar-header {
    flex-shrink: 0; /* Impede que o cabeçalho encolha */
}

#sidebar-content {
    flex-grow: 1; /* Permite que o conteúdo cresça e ocupe o espaço disponível */
    overflow-y: auto; /* Adiciona rolagem apenas ao conteúdo */
    padding-bottom: 100px; /* Espaço extra no final para garantir que todos os itens sejam visíveis */
}

/* Estilização da barra de rolagem */
#sidebar::-webkit-scrollbar,
#sidebar-content::-webkit-scrollbar {
    width: 6px;
}

#sidebar::-webkit-scrollbar-track,
#sidebar-content::-webkit-scrollbar-track {
    background: #1f2937;
}

#sidebar::-webkit-scrollbar-thumb,
#sidebar-content::-webkit-scrollbar-thumb {
    background-color: #4b5563;
    border-radius: 6px;
}

.sidebar-collapsed {
    width: 70px !important;
}

.sidebar-expanded {
    width: 250px !important;
}

/* Ajuste para o conteúdo principal */
.main-content {
    margin-left: 250px;
    transition: margin-left 0.3s ease;
    width: calc(100% - 250px);
    position: relative;
    z-index: 40; /* Maior que o sidebar para garantir que fique por cima */
}

.sidebar-collapsed ~ .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    #sidebar {
        width: 0;
        overflow: hidden;
    }

    #sidebar.sidebar-expanded {
        width: 250px !important;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .sidebar-expanded ~ .main-content {
        margin-left: 0;
        width: 100%;
    }
}

/* Estilos para os itens do menu */
#sidebar nav {
    padding-bottom: 100px; /* Espaço extra no final do menu para garantir que todos os itens sejam visíveis */
}

/* Ajuste para o footer */
footer {
    position: relative;
    z-index: 10;
}

/* Dropdown Styles */
.sidebar-dropdown {
    position: relative;
}

.sidebar-dropdown-toggle {
    cursor: pointer;
}

.sidebar-dropdown-arrow {
    transition: transform 0.2s ease;
    font-size: 0.75rem;
}

.sidebar-dropdown.open .sidebar-dropdown-arrow {
    transform: rotate(180deg);
}

.sidebar-dropdown-menu {
    transition: all 0.3s ease;
    overflow: hidden;
}

.sidebar-dropdown-menu.hidden {
    max-height: 0;
    opacity: 0;
}

.sidebar-dropdown-menu:not(.hidden) {
    max-height: 200px;
    opacity: 1;
}

/* Esconder dropdown quando sidebar está colapsada */
.sidebar-collapsed .sidebar-dropdown-arrow {
    display: none;
}

.sidebar-collapsed .sidebar-dropdown-menu {
    display: none !important;
}
