<?php
/**
 * VERIFICAÇÃO FINAL ALTERNATIVA - MÓDULO FINANCEIRO
 * Versão alternativa com conexão MySQL simplificada
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

// Definição de constantes
define('BASE_PATH', dirname(__FILE__));

// Cabeçalho HTML
echo '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação Final Alternativa - Módulo Financeiro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .check-item { margin-bottom: 15px; padding: 15px; border-radius: 5px; }
        .check-success { background-color: #d1e7dd; border-left: 5px solid #198754; }
        .check-warning { background-color: #fff3cd; border-left: 5px solid #ffc107; }
        .check-error { background-color: #f8d7da; border-left: 5px solid #dc3545; }
        .check-info { background-color: #cfe2ff; border-left: 5px solid #0d6efd; }
        .detail { margin-top: 10px; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.85rem; }
        h1, h2 { color: #0d6efd; }
        .page-header { background-color: #0d6efd; color: white; padding: 20px; margin-bottom: 30px; border-radius: 5px; }
        .progress-bar { transition: width 0.5s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="text-center"><i class="fas fa-check-circle me-2"></i>Verificação Final Alternativa</h1>
            <p class="text-center">Módulo Financeiro - ERP Faciência</p>
        </div>
        
        <div class="progress mb-4">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <div id="status-text" class="text-center mb-4">Iniciando verificação...</div>';

// Função para atualizar a barra de progresso via JavaScript
function atualizarProgresso($porcentagem, $mensagem) {
    echo '<script>
        document.querySelector(".progress-bar").style.width = "' . $porcentagem . '%";
        document.getElementById("status-text").innerText = "' . $mensagem . '";
    </script>';
    echo str_pad('', 4096);
    ob_flush();
    flush();
}

// Função para exibir resultado
function exibirResultado($status, $titulo, $mensagem, $detalhes = '') {
    $status_class = [
        'success' => 'check-success',
        'warning' => 'check-warning',
        'error' => 'check-error',
        'info' => 'check-info'
    ];
    
    $icons = [
        'success' => '<i class="fas fa-check-circle text-success"></i>',
        'warning' => '<i class="fas fa-exclamation-triangle text-warning"></i>',
        'error' => '<i class="fas fa-times-circle text-danger"></i>',
        'info' => '<i class="fas fa-info-circle text-info"></i>'
    ];
    
    echo '<div class="check-item ' . $status_class[$status] . '">';
    echo '<h4>' . $icons[$status] . ' ' . $titulo . '</h4>';
    echo '<p>' . $mensagem . '</p>';
    
    if (!empty($detalhes)) {
        echo '<div class="detail">';
        echo '<pre>' . $detalhes . '</pre>';
        echo '</div>';
    }
    
    echo '</div>';
}

// 1. Verificar conexão com o banco de dados (versão simplificada)
atualizarProgresso(10, "Verificando conexão com o banco de dados...");

try {
    // Tenta conectar ao banco de dados usando mysqli (alternativa ao PDO)
    $bancos_possiveis = [
        'u682219090_faciencia_erp',
        'faciencia_erp', 
        'reinandus',
        'erp_faciencia',
        'sistema_erp'
    ];
    
    $banco_encontrado = false;
    $mysqli = null;
    
    foreach ($bancos_possiveis as $banco) {
        $mysqli = @new mysqli('localhost', 'root', '', $banco);
        
        if (!$mysqli->connect_error) {
            $banco_encontrado = $banco;
            break;
        }
    }
    
    if (!$banco_encontrado) {
        throw new Exception("Não foi possível conectar a nenhum dos bancos de dados conhecidos.");
    }
    
    exibirResultado(
        'success',
        'Conexão com o Banco de Dados',
        'Conexão estabelecida com sucesso ao banco de dados: ' . $banco_encontrado
    );
    
    // 2. Verificar tabelas principais
    atualizarProgresso(30, "Verificando tabelas principais...");
    
    $tabelas = [
        'plano_contas' => ['id', 'codigo', 'nome', 'tipo'],
        'contas_receber' => ['id', 'descricao', 'valor', 'data_vencimento', 'status'],
        'contas_pagar' => ['id', 'descricao', 'valor', 'data_vencimento', 'status'],
        'transacoes_financeiras' => ['id', 'descricao', 'valor', 'data_transacao', 'tipo']
    ];
    
    foreach ($tabelas as $tabela => $colunas) {
        // Verificar se a tabela existe
        $result = $mysqli->query("SHOW TABLES LIKE '$tabela'");
        if ($result->num_rows == 0) {
            exibirResultado(
                'error',
                'Tabela: ' . $tabela,
                "A tabela '$tabela' não existe no banco de dados"
            );
            continue;
        }
        
        // Verificar estrutura da tabela
        $result = $mysqli->query("DESCRIBE $tabela");
        $colunas_existentes = [];
        while ($row = $result->fetch_assoc()) {
            $colunas_existentes[] = $row['Field'];
        }
        
        $colunas_faltando = array_diff($colunas, $colunas_existentes);
        
        if (!empty($colunas_faltando)) {
            exibirResultado(
                'warning',
                'Tabela: ' . $tabela,
                "A tabela '$tabela' existe, mas faltam as colunas: " . implode(', ', $colunas_faltando)
            );
            continue;
        }
        
        // Verificar se tem registros
        $result = $mysqli->query("SELECT COUNT(*) as total FROM $tabela");
        $count = $result->fetch_assoc()['total'];
        
        exibirResultado(
            'success',
            'Tabela: ' . $tabela,
            "A tabela '$tabela' existe com todas as colunas necessárias e contém $count registros"
        );
    }
    
    // 3. Verificar arquivos principais
    atualizarProgresso(60, "Verificando arquivos principais...");
    
    $arquivos = [
        'index.php' => 'Dashboard principal',
        'contas_receber.php' => 'Contas a receber',
        'contas_pagar.php' => 'Contas a pagar',
        'plano_contas_gerencial.php' => 'Plano de contas',
        'transacoes_financeiras.php' => 'Transações financeiras',
        'relatorios.php' => 'Relatórios'
    ];
    
    foreach ($arquivos as $arquivo => $descricao) {
        $caminho = BASE_PATH . '/' . $arquivo;
        
        if (!file_exists($caminho)) {
            exibirResultado(
                'error',
                'Arquivo: ' . $arquivo,
                "O arquivo '$arquivo' não existe"
            );
            continue;
        }
        
        exibirResultado(
            'success',
            'Arquivo: ' . $arquivo,
            "O arquivo '$arquivo' existe: $descricao"
        );
    }
    
    // 4. Verificar includes
    atualizarProgresso(80, "Verificando arquivos de includes...");
    
    $includes = [
        'header_padronizado.php',
        'footer_padronizado.php',
        'sidebar_padronizado.php',
        'config.php'
    ];
    
    foreach ($includes as $include) {
        $caminho = BASE_PATH . '/includes/' . $include;
        
        if (!file_exists($caminho)) {
            exibirResultado(
                'error',
                'Include: ' . $include,
                "O arquivo '$include' não existe no diretório includes"
            );
            continue;
        }
        
        exibirResultado(
            'success',
            'Include: ' . $include,
            "O arquivo '$include' existe no diretório includes"
        );
    }
    
    // 5. Resumo
    atualizarProgresso(100, "Verificação concluída!");
    
    echo '<div class="card mb-4 mt-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">Resumo da Verificação</h3>
        </div>
        <div class="card-body">
            <p>A verificação foi concluída com sucesso!</p>
            
            <div class="alert alert-info">
                <h4><i class="fas fa-info-circle me-2"></i>Próximos Passos</h4>
                <p>Com base nas verificações realizadas, você pode:</p>
                <ol>
                    <li>Corrigir eventuais problemas identificados</li>
                    <li>Utilizar o módulo financeiro para suas operações diárias</li>
                    <li>Realizar manutenções periódicas usando o script <code>manutencao_automatizada.php</code></li>
                </ol>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="/reinandus/financeiro/index.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>Ir para o Dashboard
                </a>
                <a href="/reinandus/financeiro/manutencao_automatizada.php" class="btn btn-success">
                    <i class="fas fa-tools me-2"></i>Manutenção Automatizada
                </a>
            </div>
        </div>
    </div>';
    
} catch (Exception $e) {
    exibirResultado(
        'error',
        'Erro na Verificação',
        'Ocorreu um erro durante a verificação: ' . $e->getMessage()
    );
    
    echo '<div class="alert alert-danger mt-4">
        <h4><i class="fas fa-exclamation-triangle me-2"></i>Verificação Interrompida</h4>
        <p>A verificação não pôde ser concluída devido a erros. Por favor, resolva os problemas acima e tente novamente.</p>
        <p>Você pode usar o script <code>corrigir_banco_automatico.php</code> para tentar corrigir problemas no banco de dados.</p>
        <a href="/reinandus/financeiro/corrigir_banco_automatico.php" class="btn btn-warning mt-2">
            <i class="fas fa-database me-2"></i>Corrigir Banco de Dados
        </a>
    </div>';
}

// Rodapé HTML
echo '
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
