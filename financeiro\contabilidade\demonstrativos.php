<?php
/**
 * Demonstrativos Financeiros
 * DRE, Balanço, Fluxo de Caixa e outros demonstrativos contábeis
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once 'includes/SaldoManager.php';
require_once 'includes/RelatorioManager.php';

// Verifica autenticação e permissão
Auth::requireLogin();
$userType = Auth::getUserType();
if (!in_array($userType, ['financeiro', 'admin_master'])) {
    $_SESSION['error'] = 'Você não tem permissão para acessar o módulo financeiro.';
    header('Location: ../index.php');
    exit;
}

$db = Database::getInstance();
$saldoManager = new SaldoManager($db);
$relatorioManager = new RelatorioManager($db, $saldoManager);

// Parâmetros
$demonstrativo = $_GET['demonstrativo'] ?? 'dre';
$dataInicio = $_GET['data_inicio'] ?? date('Y-m-01');
$dataFim = $_GET['data_fim'] ?? date('Y-m-t');
$comparativo = $_GET['comparativo'] ?? 'mensal';

// Carrega dados
try {
    // Atualiza saldos antes de gerar demonstrativos
    $saldoManager->atualizarTodosSaldos();
    
    switch ($demonstrativo) {
        case 'dre':
            $dados = gerarDRE($db, $dataInicio, $dataFim, $comparativo);
            break;
        case 'balanco':
            $dados = gerarBalanco($db, $dataInicio, $dataFim);
            break;
        case 'fluxo_caixa':
            $dados = gerarFluxoCaixa($db, $dataInicio, $dataFim);
            break;
        case 'dashboard':
        default:
            $dados = [
                'resumo' => $saldoManager->getResumoFinanceiro($dataInicio, $dataFim),
                'periodo' => ['inicio' => $dataInicio, 'fim' => $dataFim]
            ];
    }
} catch (Exception $e) {
    $erro = $e->getMessage();
    error_log("Erro nos demonstrativos: " . $e->getMessage());
}

/**
 * Gera Demonstração de Resultado do Exercício (DRE)
 */
function gerarDRE($db, $dataInicio, $dataFim, $comparativo) {
    // Períodos para comparação
    $periodos = [];
    
    if ($comparativo === 'mensal') {
        // Últimos 6 meses
        for ($i = 5; $i >= 0; $i--) {
            $inicio = date('Y-m-01', strtotime("-$i months", strtotime($dataInicio)));
            $fim = date('Y-m-t', strtotime("-$i months", strtotime($dataInicio)));
            $periodos[] = [
                'label' => date('M/Y', strtotime($inicio)),
                'inicio' => $inicio,
                'fim' => $fim
            ];
        }
    } else {
        // Comparação anual
        $ano = date('Y', strtotime($dataInicio));
        for ($i = 2; $i >= 0; $i--) {
            $anoAtual = $ano - $i;
            $periodos[] = [
                'label' => $anoAtual,
                'inicio' => "$anoAtual-01-01",
                'fim' => "$anoAtual-12-31"
            ];
        }
    }
    
    $dre = [];
    
    foreach ($periodos as $periodo) {
        // Receitas
        $receitas = $db->fetchAll("
            SELECT 
                COALESCE(cf.nome, 'Outras Receitas') as categoria,
                SUM(valor) as total
            FROM (
                SELECT categoria_id, valor FROM transacoes_financeiras 
                WHERE tipo = 'receita' AND status = 'efetivada' 
                AND data_transacao BETWEEN ? AND ?
                
                UNION ALL
                
                SELECT categoria_id, valor FROM contas_receber 
                WHERE status = 'recebido' 
                AND data_recebimento BETWEEN ? AND ?
            ) as receitas_consolidadas
            LEFT JOIN categorias_financeiras cf ON receitas_consolidadas.categoria_id = cf.id
            GROUP BY receitas_consolidadas.categoria_id
            ORDER BY total DESC
        ", [$periodo['inicio'], $periodo['fim'], $periodo['inicio'], $periodo['fim']]);
        
        // Despesas
        $despesas = $db->fetchAll("
            SELECT 
                COALESCE(cf.nome, 'Outras Despesas') as categoria,
                SUM(valor) as total
            FROM (
                SELECT categoria_id, valor FROM transacoes_financeiras 
                WHERE tipo = 'despesa' AND status = 'efetivada' 
                AND data_transacao BETWEEN ? AND ?
                
                UNION ALL
                
                SELECT categoria_id, valor FROM contas_pagar 
                WHERE status = 'pago' 
                AND data_pagamento BETWEEN ? AND ?
                
                UNION ALL
                
                SELECT NULL as categoria_id, salario_liquido as valor FROM folha_pagamento 
                WHERE status = 'paga' 
                AND data_pagamento BETWEEN ? AND ?
            ) as despesas_consolidadas
            LEFT JOIN categorias_financeiras cf ON despesas_consolidadas.categoria_id = cf.id
            GROUP BY despesas_consolidadas.categoria_id
            ORDER BY total DESC
        ", [$periodo['inicio'], $periodo['fim'], $periodo['inicio'], $periodo['fim'], $periodo['inicio'], $periodo['fim']]);
        
        $totalReceitas = array_sum(array_column($receitas, 'total'));
        $totalDespesas = array_sum(array_column($despesas, 'total'));
        
        $dre[$periodo['label']] = [
            'periodo' => $periodo,
            'receitas' => $receitas,
            'despesas' => $despesas,
            'total_receitas' => $totalReceitas,
            'total_despesas' => $totalDespesas,
            'resultado_bruto' => $totalReceitas - $totalDespesas,
            'margem_liquida' => $totalReceitas > 0 ? (($totalReceitas - $totalDespesas) / $totalReceitas) * 100 : 0
        ];
    }
    
    return $dre;
}

/**
 * Gera Balanço Patrimonial Simplificado
 */
function gerarBalanco($db, $dataInicio, $dataFim) {
    // Ativo
    $ativo = [
        'circulante' => [
            'disponibilidades' => [],
            'contas_receber' => 0
        ],
        'total' => 0
    ];
    
    // Disponibilidades (Contas Bancárias)
    $contasBancarias = $db->fetchAll("
        SELECT nome, saldo_atual, tipo
        FROM contas_bancarias 
        WHERE status = 'ativo'
        ORDER BY saldo_atual DESC
    ");
    
    $ativo['circulante']['disponibilidades'] = $contasBancarias;
    $totalDisponibilidades = array_sum(array_column($contasBancarias, 'saldo_atual'));
    
    // Contas a Receber
    $contasReceber = $db->fetchOne("
        SELECT COALESCE(SUM(valor), 0) as total
        FROM contas_receber 
        WHERE status = 'pendente'
    ")['total'];
    
    $ativo['circulante']['contas_receber'] = $contasReceber;
    $ativo['total'] = $totalDisponibilidades + $contasReceber;
    
    // Passivo
    $passivo = [
        'circulante' => [
            'contas_pagar' => 0,
            'folha_pagamento' => 0
        ],
        'total' => 0
    ];
    
    // Contas a Pagar
    $contasPagar = $db->fetchOne("
        SELECT COALESCE(SUM(valor), 0) as total
        FROM contas_pagar 
        WHERE status = 'pendente'
    ")['total'];
    
    $passivo['circulante']['contas_pagar'] = $contasPagar;
    
    // Folha de Pagamento Pendente
    $folhaPendente = $db->fetchOne("
        SELECT COALESCE(SUM(salario_liquido), 0) as total
        FROM folha_pagamento 
        WHERE status = 'calculada'
    ")['total'];
    
    $passivo['circulante']['folha_pagamento'] = $folhaPendente;
    $passivo['total'] = $contasPagar + $folhaPendente;
    
    // Patrimônio Líquido
    $patrimonioLiquido = $ativo['total'] - $passivo['total'];
    
    return [
        'data_referencia' => $dataFim,
        'ativo' => $ativo,
        'passivo' => $passivo,
        'patrimonio_liquido' => $patrimonioLiquido,
        'total_passivo_pl' => $passivo['total'] + $patrimonioLiquido
    ];
}

/**
 * Gera Demonstrativo de Fluxo de Caixa
 */
function gerarFluxoCaixa($db, $dataInicio, $dataFim) {
    // Saldo inicial (primeiro dia do período)
    $saldoInicial = $db->fetchOne("
        SELECT COALESCE(SUM(saldo_atual), 0) as total
        FROM contas_bancarias 
        WHERE status = 'ativo'
    ")['total'];
    
    // Atividades Operacionais
    $operacionais = [
        'recebimentos' => [],
        'pagamentos' => [],
        'total_recebimentos' => 0,
        'total_pagamentos' => 0,
        'resultado' => 0
    ];
    
    // Recebimentos Operacionais
    $recebimentos = $db->fetchAll("
        SELECT 
            'Receitas de Serviços' as descricao,
            SUM(valor) as valor
        FROM (
            SELECT valor FROM transacoes_financeiras 
            WHERE tipo = 'receita' AND status = 'efetivada' 
            AND data_transacao BETWEEN ? AND ?
            
            UNION ALL
            
            SELECT valor FROM contas_receber 
            WHERE status = 'recebido' 
            AND data_recebimento BETWEEN ? AND ?
        ) as recebimentos_consolidados
    ", [$dataInicio, $dataFim, $dataInicio, $dataFim]);
    
    $operacionais['recebimentos'] = $recebimentos;
    $operacionais['total_recebimentos'] = array_sum(array_column($recebimentos, 'valor'));
    
    // Pagamentos Operacionais
    $pagamentos = $db->fetchAll("
        SELECT 
            CASE 
                WHEN categoria IS NULL THEN 'Folha de Pagamento'
                ELSE categoria
            END as descricao,
            SUM(valor) as valor
        FROM (
            SELECT cf.nome as categoria, tf.valor
            FROM transacoes_financeiras tf
            LEFT JOIN categorias_financeiras cf ON tf.categoria_id = cf.id
            WHERE tf.tipo = 'despesa' AND tf.status = 'efetivada' 
            AND tf.data_transacao BETWEEN ? AND ?
            
            UNION ALL
            
            SELECT cf.nome as categoria, cp.valor
            FROM contas_pagar cp
            LEFT JOIN categorias_financeiras cf ON cp.categoria_id = cf.id
            WHERE cp.status = 'pago' 
            AND cp.data_pagamento BETWEEN ? AND ?
            
            UNION ALL
            
            SELECT NULL as categoria, fp.salario_liquido as valor
            FROM folha_pagamento fp
            WHERE fp.status = 'paga' 
            AND fp.data_pagamento BETWEEN ? AND ?
        ) as pagamentos_consolidados
        GROUP BY categoria
        ORDER BY valor DESC
    ", [$dataInicio, $dataFim, $dataInicio, $dataFim, $dataInicio, $dataFim]);
    
    $operacionais['pagamentos'] = $pagamentos;
    $operacionais['total_pagamentos'] = array_sum(array_column($pagamentos, 'valor'));
    $operacionais['resultado'] = $operacionais['total_recebimentos'] - $operacionais['total_pagamentos'];
    
    // Saldo final
    $saldoFinal = $saldoInicial + $operacionais['resultado'];
    
    return [
        'periodo' => ['inicio' => $dataInicio, 'fim' => $dataFim],
        'saldo_inicial' => $saldoInicial,
        'operacionais' => $operacionais,
        'saldo_final' => $saldoFinal,
        'variacao' => $operacionais['resultado']
    ];
}

$pageTitle = 'Demonstrativos Financeiros';
?>

<?php include 'includes/header_padronizado_novo.php'; ?>

<!-- CSS Específico para Demonstrativos -->
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .demonstration-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        overflow: hidden;
    }
    
    .demonstration-header {
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        color: white;
        padding: 24px;
        text-align: center;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-group label {
        font-weight: 600;
        margin-bottom: 6px;
        color: #374151;
    }
    
    .filter-input {
        padding: 10px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s;
    }
    
    .filter-input:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    
    .demo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-top: 24px;
    }
    
    .demo-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .demo-section h3 {
        color: #1f2937;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .value-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .value-item:last-child {
        border-bottom: none;
    }
    
    .value-label {
        color: #6b7280;
        font-size: 14px;
    }
    
    .value-amount {
        font-weight: 600;
        font-size: 16px;
    }
    
    .value-positive {
        color: #059669;
    }
    
    .value-negative {
        color: #dc2626;
    }
    
    .chart-container {
        height: 400px;
        margin: 20px 0;
    }
    
    @media (max-width: 768px) {
        .page-container {
            padding: 16px;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .demo-grid {
            grid-template-columns: 1fr;
        }
        
        .demonstration-header {
            padding: 16px;
        }
        
        .demo-section {
            padding: 16px;
        }
    }
</style>

<div class="page-container">
                    
    <!-- Header da página -->
    <div class="demonstration-card">
        <div class="demonstration-header">
            <h1><i class="fas fa-chart-line mr-3"></i>Demonstrativos Financeiros</h1>
            <p class="mt-2 opacity-90">Demonstrações contábeis e financeiras oficiais</p>
        </div>
    </div>

    <!-- Filtros e Controles -->
    <div class="demonstration-card">
        <div class="p-6">
            <form method="GET">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>Demonstrativo</label>
                        <select name="demonstrativo" class="filter-input">
                            <option value="dashboard" <?php echo $demonstrativo === 'dashboard' ? 'selected' : ''; ?>>Dashboard</option>
                            <option value="dre" <?php echo $demonstrativo === 'dre' ? 'selected' : ''; ?>>DRE</option>
                            <option value="balanco" <?php echo $demonstrativo === 'balanco' ? 'selected' : ''; ?>>Balanço Patrimonial</option>
                            <option value="fluxo_caixa" <?php echo $demonstrativo === 'fluxo_caixa' ? 'selected' : ''; ?>>Fluxo de Caixa</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Data Início</label>
                        <input type="date" name="data_inicio" value="<?php echo $dataInicio; ?>" class="filter-input">
                    </div>
                    <div class="filter-group">
                        <label>Data Fim</label>
                        <input type="date" name="data_fim" value="<?php echo $dataFim; ?>" class="filter-input">
                    </div>
                    <?php if ($demonstrativo === 'dre'): ?>
                    <div class="filter-group">
                        <label>Comparativo</label>
                        <select name="comparativo" class="filter-input">
                            <option value="mensal" <?php echo $comparativo === 'mensal' ? 'selected' : ''; ?>>Mensal</option>
                            <option value="anual" <?php echo $comparativo === 'anual' ? 'selected' : ''; ?>>Anual</option>
                        </select>
                    </div>
                    <?php endif; ?>
                    <div class="filter-group" style="justify-content: flex-end; flex-direction: row; gap: 12px;">
                        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-file-alt mr-2"></i>Gerar
                        </button>
                        <button type="button" onclick="window.print()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-print mr-2"></i>Imprimir
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php if ($demonstrativo === 'dashboard'): ?>
        <!-- Dashboard dos Demonstrativos -->
        <div class="demo-grid">
            <a href="?demonstrativo=dre&data_inicio=<?php echo $dataInicio; ?>&data_fim=<?php echo $dataFim; ?>" 
               class="demo-section hover:shadow-lg transition-shadow text-decoration-none">
                <div class="d-flex align-items-center">
                    <div class="w-12 h-12 bg-success bg-opacity-10 rounded-lg d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-chart-line text-success fs-4"></i>
                    </div>
                    <div>
                        <h3 class="mb-1">DRE</h3>
                        <p class="text-muted small mb-0">Demonstração do Resultado</p>
                    </div>
                </div>
            </a>

            <a href="?demonstrativo=balanco&data_inicio=<?php echo $dataInicio; ?>&data_fim=<?php echo $dataFim; ?>" 
               class="demo-section hover:shadow-lg transition-shadow text-decoration-none">
                <div class="d-flex align-items-center">
                    <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-balance-scale text-primary fs-4"></i>
                    </div>
                    <div>
                        <h3 class="mb-1">Balanço</h3>
                        <p class="text-muted small mb-0">Patrimonial</p>
                    </div>
                </div>
            </a>

            <a href="?demonstrativo=fluxo_caixa&data_inicio=<?php echo $dataInicio; ?>&data_fim=<?php echo $dataFim; ?>" 
               class="demo-section hover:shadow-lg transition-shadow text-decoration-none">
                <div class="d-flex align-items-center">
                    <div class="w-12 h-12 bg-warning bg-opacity-10 rounded-lg d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-money-bill-wave text-warning fs-4"></i>
                    </div>
                    <div>
                        <h3 class="mb-1">Fluxo de Caixa</h3>
                        <p class="text-muted small mb-0">Movimentações</p>
                    </div>
                </div>
            </a>

            <div class="demo-section">
                <div class="d-flex align-items-center">
                    <div class="w-12 h-12 bg-info bg-opacity-10 rounded-lg d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-chart-pie text-info fs-4"></i>
                    </div>
                    <div>
                        <h3 class="mb-1">Resumo Geral</h3>
                        <p class="text-muted small mb-0">Dados do período</p>
                    </div>
                </div>
            </div>
        </div>
                                        <i class="fas fa-water text-purple-600 text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-lg font-semibold text-gray-900">Fluxo de Caixa</h3>
                                        <p class="text-sm text-gray-600">DFC</p>
                                    </div>
                                </div>
                            </a>

                            <a href="relatorios.php" 
                               class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-lg font-semibold text-gray-900">Relatórios</h3>
                                        <p class="text-sm text-gray-600">Analíticos</p>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <!-- Resumo Rápido -->
                        <?php if (isset($dados['resumo'])): ?>
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Resumo do Período</h3>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">
                                        R$ <?php echo number_format($dados['resumo']['saldo_total_contas'], 2, ',', '.'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600">Saldo Total</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">
                                        R$ <?php echo number_format($dados['resumo']['receitas_periodo'], 2, ',', '.'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600">Receitas</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-600">
                                        R$ <?php echo number_format($dados['resumo']['despesas_periodo'], 2, ',', '.'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600">Despesas</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold <?php echo $dados['resumo']['resultado_periodo'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                        R$ <?php echo number_format($dados['resumo']['resultado_periodo'], 2, ',', '.'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600">Resultado</div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                    <?php elseif ($demonstrativo === 'dre' && !empty($dados)): ?>
                        <!-- DRE - Demonstração do Resultado do Exercício -->
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Demonstração do Resultado do Exercício</h3>
                                <p class="text-sm text-gray-600">Comparativo <?php echo ucfirst($comparativo); ?></p>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Conta</th>
                                            <?php foreach ($dados as $periodo => $dadosPeriodo): ?>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"><?php echo $periodo; ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <!-- Receitas -->
                                        <tr class="bg-green-50">
                                            <td class="px-6 py-4 font-semibold text-green-800">RECEITAS</td>
                                            <?php foreach ($dados as $dadosPeriodo): ?>
                                            <td class="px-6 py-4 font-semibold text-green-600">
                                                R$ <?php echo number_format($dadosPeriodo['total_receitas'], 2, ',', '.'); ?>
                                            </td>
                                            <?php endforeach; ?>
                                        </tr>
                                        
                                        <!-- Despesas -->
                                        <tr class="bg-red-50">
                                            <td class="px-6 py-4 font-semibold text-red-800">DESPESAS</td>
                                            <?php foreach ($dados as $dadosPeriodo): ?>
                                            <td class="px-6 py-4 font-semibold text-red-600">
                                                (R$ <?php echo number_format($dadosPeriodo['total_despesas'], 2, ',', '.'); ?>)
                                            </td>
                                            <?php endforeach; ?>
                                        </tr>
                                        
                                        <!-- Resultado -->
                                        <tr class="bg-gray-100 border-t-2">
                                            <td class="px-6 py-4 font-bold text-gray-900">RESULTADO LÍQUIDO</td>
                                            <?php foreach ($dados as $dadosPeriodo): ?>
                                            <td class="px-6 py-4 font-bold <?php echo $dadosPeriodo['resultado_bruto'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                R$ <?php echo number_format($dadosPeriodo['resultado_bruto'], 2, ',', '.'); ?>
                                            </td>
                                            <?php endforeach; ?>
                                        </tr>
                                        
                                        <!-- Margem -->
                                        <tr>
                                            <td class="px-6 py-4 font-semibold text-gray-700">MARGEM LÍQUIDA (%)</td>
                                            <?php foreach ($dados as $dadosPeriodo): ?>
                                            <td class="px-6 py-4 font-semibold <?php echo $dadosPeriodo['margem_liquida'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                <?php echo number_format($dadosPeriodo['margem_liquida'], 1, ',', '.'); ?>%
                                            </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    <?php elseif ($demonstrativo === 'balanco' && !empty($dados)): ?>
                        <!-- Balanço Patrimonial -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- ATIVO -->
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
                                    <h3 class="text-lg font-semibold text-blue-800">ATIVO</h3>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <!-- Ativo Circulante -->
                                        <div>
                                            <h4 class="font-semibold text-gray-900 mb-2">Ativo Circulante</h4>
                                            
                                            <!-- Disponibilidades -->
                                            <div class="ml-4 space-y-1">
                                                <div class="font-medium text-gray-700">Disponibilidades</div>
                                                <?php foreach ($dados['ativo']['circulante']['disponibilidades'] as $conta): ?>
                                                <div class="ml-4 flex justify-between text-sm">
                                                    <span><?php echo htmlspecialchars($conta['nome']); ?></span>
                                                    <span>R$ <?php echo number_format($conta['saldo_atual'], 2, ',', '.'); ?></span>
                                                </div>
                                                <?php endforeach; ?>
                                                
                                                <!-- Contas a Receber -->
                                                <div class="ml-4 flex justify-between text-sm">
                                                    <span>Contas a Receber</span>
                                                    <span>R$ <?php echo number_format($dados['ativo']['circulante']['contas_receber'], 2, ',', '.'); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Total do Ativo -->
                                        <div class="border-t pt-4">
                                            <div class="flex justify-between font-bold text-lg">
                                                <span>TOTAL DO ATIVO</span>
                                                <span class="text-blue-600">R$ <?php echo number_format($dados['ativo']['total'], 2, ',', '.'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PASSIVO + PL -->
                            <div class="bg-white rounded-lg shadow">
                                <div class="px-6 py-4 border-b border-gray-200 bg-red-50">
                                    <h3 class="text-lg font-semibold text-red-800">PASSIVO + PATRIMÔNIO LÍQUIDO</h3>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <!-- Passivo Circulante -->
                                        <div>
                                            <h4 class="font-semibold text-gray-900 mb-2">Passivo Circulante</h4>
                                            <div class="ml-4 space-y-1 text-sm">
                                                <div class="flex justify-between">
                                                    <span>Contas a Pagar</span>
                                                    <span>R$ <?php echo number_format($dados['passivo']['circulante']['contas_pagar'], 2, ',', '.'); ?></span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>Folha de Pagamento</span>
                                                    <span>R$ <?php echo number_format($dados['passivo']['circulante']['folha_pagamento'], 2, ',', '.'); ?></span>
                                                </div>
                                            </div>
                                            <div class="ml-4 mt-2 pt-2 border-t flex justify-between font-medium">
                                                <span>Total Passivo Circulante</span>
                                                <span>R$ <?php echo number_format($dados['passivo']['total'], 2, ',', '.'); ?></span>
                                            </div>
                                        </div>
                                        
                                        <!-- Patrimônio Líquido -->
                                        <div>
                                            <h4 class="font-semibold text-gray-900 mb-2">Patrimônio Líquido</h4>
                                            <div class="ml-4 text-sm">
                                                <div class="flex justify-between">
                                                    <span>Capital + Reservas</span>
                                                    <span class="<?php echo $dados['patrimonio_liquido'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                        R$ <?php echo number_format($dados['patrimonio_liquido'], 2, ',', '.'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Total -->
                                        <div class="border-t pt-4">
                                            <div class="flex justify-between font-bold text-lg">
                                                <span>TOTAL PASSIVO + PL</span>
                                                <span class="text-red-600">R$ <?php echo number_format($dados['total_passivo_pl'], 2, ',', '.'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <?php elseif ($demonstrativo === 'fluxo_caixa' && !empty($dados)): ?>
                        <!-- Demonstrativo de Fluxo de Caixa -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Demonstração do Fluxo de Caixa</h3>
                                <p class="text-sm text-gray-600">Período: <?php echo date('d/m/Y', strtotime($dados['periodo']['inicio'])); ?> a <?php echo date('d/m/Y', strtotime($dados['periodo']['fim'])); ?></p>
                            </div>
                            <div class="p-6">
                                <div class="space-y-6">
                                    <!-- Saldo Inicial -->
                                    <div class="flex justify-between items-center py-3 border-b">
                                        <span class="font-semibold">Saldo Inicial de Caixa</span>
                                        <span class="font-semibold text-blue-600">R$ <?php echo number_format($dados['saldo_inicial'], 2, ',', '.'); ?></span>
                                    </div>
                                    
                                    <!-- Atividades Operacionais -->
                                    <div>
                                        <h4 class="font-semibold text-lg text-gray-900 mb-3">Atividades Operacionais</h4>
                                        
                                        <!-- Recebimentos -->
                                        <div class="mb-4">
                                            <h5 class="font-medium text-green-700 mb-2">Recebimentos:</h5>
                                            <div class="ml-4 space-y-1">
                                                <?php foreach ($dados['operacionais']['recebimentos'] as $recebimento): ?>
                                                <div class="flex justify-between text-sm">
                                                    <span><?php echo htmlspecialchars($recebimento['descricao']); ?></span>
                                                    <span class="text-green-600">R$ <?php echo number_format($recebimento['valor'], 2, ',', '.'); ?></span>
                                                </div>
                                                <?php endforeach; ?>
                                                <div class="flex justify-between font-medium border-t pt-1">
                                                    <span>Total Recebimentos</span>
                                                    <span class="text-green-600">R$ <?php echo number_format($dados['operacionais']['total_recebimentos'], 2, ',', '.'); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Pagamentos -->
                                        <div class="mb-4">
                                            <h5 class="font-medium text-red-700 mb-2">Pagamentos:</h5>
                                            <div class="ml-4 space-y-1">
                                                <?php foreach ($dados['operacionais']['pagamentos'] as $pagamento): ?>
                                                <div class="flex justify-between text-sm">
                                                    <span><?php echo htmlspecialchars($pagamento['descricao']); ?></span>
                                                    <span class="text-red-600">(R$ <?php echo number_format($pagamento['valor'], 2, ',', '.'); ?>)</span>
                                                </div>
                                                <?php endforeach; ?>
                                                <div class="flex justify-between font-medium border-t pt-1">
                                                    <span>Total Pagamentos</span>
                                                    <span class="text-red-600">(R$ <?php echo number_format($dados['operacionais']['total_pagamentos'], 2, ',', '.'); ?>)</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Resultado Operacional -->
                                        <div class="bg-gray-50 p-3 rounded">
                                            <div class="flex justify-between font-semibold">
                                                <span>Caixa Líquido das Atividades Operacionais</span>
                                                <span class="<?php echo $dados['operacionais']['resultado'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                    R$ <?php echo number_format($dados['operacionais']['resultado'], 2, ',', '.'); ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Saldo Final -->
                                    <div class="border-t pt-4">
                                        <div class="flex justify-between items-center">
                                            <span class="font-bold text-lg">Saldo Final de Caixa</span>
                                            <span class="font-bold text-lg <?php echo $dados['saldo_final'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                R$ <?php echo number_format($dados['saldo_final'], 2, ',', '.'); ?>
                                            </span>
                                        </div>
                                        <div class="flex justify-between items-center mt-2 text-sm">
                                            <span>Variação do Período</span>
                                            <span class="<?php echo $dados['variacao'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                R$ <?php echo number_format($dados['variacao'], 2, ',', '.'); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

        <?php else: ?>
            <!-- Nenhum dado -->
            <div class="demo-section text-center">
                <i class="fas fa-file-alt text-muted fs-1 mb-4"></i>
                <p class="text-muted">Selecione um demonstrativo para visualizar.</p>
            </div>
        <?php endif; ?>

</div>

<?php include 'includes/footer_padronizado.php'; ?>

<!-- Scripts Específicos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Scripts para gráficos e interações dos demonstrativos
document.addEventListener('DOMContentLoaded', function() {
    // Configurações específicas dos demonstrativos aqui
    console.log('Demonstrativos carregados');
});
</script>
