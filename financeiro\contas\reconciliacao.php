<?php
/**
 * Reconciliação e Auditoria Financeira
 * Ferramenta para garantir consistência contábil e fiscal
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once 'includes/SaldoManager.php';

// Verifica autenticação e permissão
Auth::requireLogin();
$userType = Auth::getUserType();
if (!in_array($userType, ['financeiro', 'admin_master'])) {
    $_SESSION['error'] = 'Você não tem permissão para acessar o módulo financeiro.';
    header('Location: ../index.php');
    exit;
}

$db = Database::getInstance();
$saldoManager = new SaldoManager($db);

$action = $_GET['action'] ?? 'dashboard';

// Processa ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'atualizar_saldos') {
        try {
            $resultados = $saldoManager->atualizarTodosSaldos();
            $total = count($resultados);
            $sucessos = count(array_filter($resultados));
            $_SESSION['success'] = "Saldos atualizados: $sucessos de $total contas processadas.";
        } catch (Exception $e) {
            $_SESSION['error'] = 'Erro ao atualizar saldos: ' . $e->getMessage();
        }
        header('Location: reconciliacao.php');
        exit;
    }

    if ($action === 'corrigir_inconsistencias') {
        try {
            $resultado = $saldoManager->corrigirInconsistencias();
            if ($resultado['sucesso']) {
                $correcoes = count($resultado['correcoes']);
                $_SESSION['success'] = "Inconsistências corrigidas: $correcoes correções aplicadas.";
            } else {
                $_SESSION['error'] = 'Erro ao corrigir inconsistências: ' . $resultado['erro'];
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Erro ao corrigir inconsistências: ' . $e->getMessage();
        }
        header('Location: reconciliacao.php');
        exit;
    }
}

// Carrega dados para exibição
try {
    // Resumo financeiro atual
    $resumoFinanceiro = $saldoManager->getResumoFinanceiro();
    
    // Validação da consistência
    $validacao = $saldoManager->validarConsistencia();
    
    // Estatísticas detalhadas
    $estatisticas = [
        'total_contas_bancarias' => $db->fetchOne("SELECT COUNT(*) as total FROM contas_bancarias WHERE status = 'ativo'")['total'],
        'total_categorias' => $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE status = 'ativo'")['total'],
        'total_transacoes' => $db->fetchOne("SELECT COUNT(*) as total FROM transacoes_financeiras WHERE status = 'efetivada'")['total'],
        'contas_pagar_pendentes' => $db->fetchOne("SELECT COUNT(*) as total FROM contas_pagar WHERE status = 'pendente'")['total'],
        'contas_receber_pendentes' => $db->fetchOne("SELECT COUNT(*) as total FROM contas_receber WHERE status = 'pendente'")['total'],
        'transacoes_sem_conta' => $db->fetchOne("SELECT COUNT(*) as total FROM transacoes_financeiras WHERE conta_bancaria_id IS NULL AND tipo IN ('receita', 'despesa')")['total'],
        'valor_transacoes_sem_conta' => $db->fetchOne("SELECT COALESCE(SUM(valor), 0) as total FROM transacoes_financeiras WHERE conta_bancaria_id IS NULL AND tipo IN ('receita', 'despesa')")['total']
    ];
    
    // Análise por conta bancária
    $analiseContas = $db->fetchAll("
        SELECT 
            cb.id,
            cb.nome,
            cb.tipo,
            cb.saldo_inicial,
            cb.saldo_atual,
            cb.data_saldo,
            (SELECT COUNT(*) FROM transacoes_financeiras tf WHERE tf.conta_bancaria_id = cb.id AND tf.status = 'efetivada') as total_transacoes,
            (SELECT COALESCE(SUM(CASE WHEN tf.tipo = 'receita' THEN tf.valor ELSE -tf.valor END), 0) 
             FROM transacoes_financeiras tf 
             WHERE tf.conta_bancaria_id = cb.id AND tf.status = 'efetivada') as movimentacao_total
        FROM contas_bancarias cb
        WHERE cb.status = 'ativo'
        ORDER BY cb.nome
    ");
    
    // Calcula saldo real para cada conta
    foreach ($analiseContas as &$conta) {
        $saldoDetalhado = $saldoManager->calcularSaldoContaBancaria($conta['id']);
        $conta['saldo_calculado'] = $saldoDetalhado['saldo_calculado'];
        $conta['diferenca'] = $conta['saldo_atual'] - $conta['saldo_calculado'];
        $conta['detalhes_calculo'] = $saldoDetalhado;
    }
    
    // Últimas transações sem conta bancária
    $transacoesSemConta = $db->fetchAll("
        SELECT tf.*, cf.nome as categoria_nome
        FROM transacoes_financeiras tf
        LEFT JOIN categorias_financeiras cf ON tf.categoria_id = cf.id
        WHERE tf.conta_bancaria_id IS NULL 
        AND tf.tipo IN ('receita', 'despesa')
        AND tf.status = 'efetivada'
        ORDER BY tf.created_at DESC
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $resumoFinanceiro = null;
    $validacao = ['consistente' => false, 'inconsistencias' => ['Erro ao carregar dados: ' . $e->getMessage()]];
    $estatisticas = [];
    $analiseContas = [];
    $transacoesSemConta = [];
    $erro_geral = $e->getMessage();
}

$pageTitle = 'Reconciliação Financeira';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Faciência ERP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/financeiro.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col ml-64">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>
            
            <!-- Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    
                    <!-- Mensagens -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Header da página -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Reconciliação Financeira</h1>
                        <p class="text-gray-600 mt-2">Auditoria e consistência do sistema financeiro</p>
                    </div>

                    <!-- Status da Consistência -->
                    <div class="mb-8">
                        <?php if ($validacao['consistente']): ?>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-400 text-2xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-green-800">Sistema Consistente</h3>
                                        <p class="text-green-700">Todos os dados financeiros estão sincronizados e corretos.</p>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-red-800">Inconsistências Detectadas</h3>
                                        <div class="mt-2 text-red-700">
                                            <ul class="list-disc list-inside space-y-1">
                                                <?php foreach ($validacao['inconsistencias'] as $inconsistencia): ?>
                                                    <li><?php echo htmlspecialchars($inconsistencia); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                        <div class="mt-4 space-x-3">
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="corrigir_inconsistencias">
                                                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                                    <i class="fas fa-tools mr-2"></i>Corrigir Automaticamente
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Ações Rápidas -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Atualizar Saldos</h3>
                            <p class="text-gray-600 mb-4">Recalcula todos os saldos das contas bancárias baseado nas transações.</p>
                            <form method="POST">
                                <input type="hidden" name="action" value="atualizar_saldos">
                                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                                    <i class="fas fa-sync mr-2"></i>Atualizar Saldos
                                </button>
                            </form>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Diagnóstico Completo</h3>
                            <p class="text-gray-600 mb-4">Verifica tabelas, índices e configurações do sistema.</p>
                            <a href="diagnostico_sistema.php" class="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded hover:bg-purple-700">
                                <i class="fas fa-stethoscope mr-2"></i>Executar Diagnóstico
                            </a>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Backup de Dados</h3>
                            <p class="text-gray-600 mb-4">Exporta backup dos dados financeiros para segurança.</p>
                            <button class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-download mr-2"></i>Gerar Backup
                            </button>
                        </div>
                    </div>

                    <!-- Estatísticas Gerais -->
                    <div class="bg-white rounded-lg shadow mb-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Estatísticas do Sistema</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600"><?php echo number_format($estatisticas['total_contas_bancarias'] ?? 0); ?></div>
                                    <div class="text-sm text-gray-600">Contas Bancárias</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600"><?php echo number_format($estatisticas['total_transacoes'] ?? 0); ?></div>
                                    <div class="text-sm text-gray-600">Transações</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-600"><?php echo number_format($estatisticas['contas_pagar_pendentes'] ?? 0); ?></div>
                                    <div class="text-sm text-gray-600">Contas a Pagar</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600"><?php echo number_format($estatisticas['contas_receber_pendentes'] ?? 0); ?></div>
                                    <div class="text-sm text-gray-600">Contas a Receber</div>
                                </div>
                            </div>
                            
                            <?php if (($estatisticas['transacoes_sem_conta'] ?? 0) > 0): ?>
                                <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-400 mr-2"></i>
                                        <span class="font-medium text-yellow-800">
                                            <?php echo $estatisticas['transacoes_sem_conta']; ?> transações sem conta bancária 
                                            (R$ <?php echo number_format($estatisticas['valor_transacoes_sem_conta'], 2, ',', '.'); ?>)
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Análise por Conta Bancária -->
                    <div class="bg-white rounded-lg shadow mb-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Análise das Contas Bancárias</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conta</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo Registrado</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo Calculado</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diferença</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transações</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($analiseContas as $conta): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($conta['nome']); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo ucfirst($conta['tipo']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                R$ <?php echo number_format($conta['saldo_atual'], 2, ',', '.'); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                R$ <?php echo number_format($conta['saldo_calculado'], 2, ',', '.'); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <?php
                                                $diferenca = $conta['diferenca'];
                                                $class = $diferenca == 0 ? 'text-green-600' : 'text-red-600';
                                                ?>
                                                <span class="<?php echo $class; ?>">
                                                    R$ <?php echo number_format(abs($diferenca), 2, ',', '.'); ?>
                                                    <?php if ($diferenca != 0): ?>
                                                        (<?php echo $diferenca > 0 ? '+' : '-'; ?>)
                                                    <?php endif; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($conta['total_transacoes']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($conta['diferenca'] == 0): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fas fa-check mr-1"></i>Sincronizado
                                                    </span>
                                                <?php else: ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>Divergência
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Transações sem Conta Bancária -->
                    <?php if (!empty($transacoesSemConta)): ?>
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Transações sem Conta Bancária</h3>
                                <p class="text-sm text-gray-600">Estas transações precisam ter uma conta bancária definida</p>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categoria</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php foreach ($transacoesSemConta as $transacao): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo date('d/m/Y', strtotime($transacao['data_transacao'])); ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($transacao['descricao']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $transacao['tipo'] === 'receita' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                        <?php echo ucfirst($transacao['tipo']); ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    R$ <?php echo number_format($transacao['valor'], 2, ',', '.'); ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($transacao['categoria_nome'] ?? 'Sem categoria'); ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/financeiro.js"></script>
</body>
</html>
