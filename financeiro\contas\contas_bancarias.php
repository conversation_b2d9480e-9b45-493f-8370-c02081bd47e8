<?php
// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// TEMPORARIAMENTE REMOVIDAS AS RESTRIÇÕES DE LOGIN
// Comentado para permitir acesso livre ao módulo financeiro
/*
if (!isset($_SESSION['user_id'])) {
    echo '<h2>Acesso Restrito</h2>';
    echo '<p>Você precisa estar logado para acessar esta página.</p>';
    echo '<p><a href="../login.php">Fazer Login</a></p>';
    exit;
}
*/

// Incluir os arquivos de configuração e funções
require_once 'includes/config.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        // Tenta usar Auth::getUserName() primeiro
        if (method_exists('Auth', 'getUserName')) {
            return Auth::getUserName() ?? 'Usuário';
        }
        // Fallback para sessão direta
        return $_SESSION['user_nome'] ?? $_SESSION['user_name'] ?? 'Usuário';
    }
}

// Variáveis de controle
$statusFilter = $_GET['status'] ?? '';
$tipoFilter = $_GET['tipo'] ?? '';
$busca = $_GET['busca'] ?? '';

// Processar ações (adicionar, editar, excluir, ativar/desativar)
if ($_POST['action'] ?? '') {
    try {
        $db = Database::getInstance();
        $action = $_POST['action'];
        
        switch ($action) {
            case 'adicionar':
                $saldoInicial = str_replace(['.', ','], ['', '.'], $_POST['saldo_inicial']);
                $saldoInicial = floatval($saldoInicial);
                
                $stmt = $db->prepare("INSERT INTO contas_bancarias (nome, banco, agencia, conta, tipo, saldo_inicial, saldo_atual, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'ativo')");
                $stmt->execute([
                    $_POST['nome'],
                    $_POST['banco'],
                    $_POST['agencia'],
                    $_POST['conta'],
                    $_POST['tipo'],
                    $saldoInicial,
                    $saldoInicial
                ]);
                $_SESSION['msg_success'] = "Conta bancária adicionada com sucesso!";
                break;
                
            case 'editar':
                $saldoInicial = str_replace(['.', ','], ['', '.'], $_POST['saldo_inicial']);
                $saldoInicial = floatval($saldoInicial);
                
                $stmt = $db->prepare("UPDATE contas_bancarias SET nome = ?, banco = ?, agencia = ?, conta = ?, tipo = ?, saldo_inicial = ?, saldo_atual = ? WHERE id = ?");
                $stmt->execute([
                    $_POST['nome'],
                    $_POST['banco'],
                    $_POST['agencia'],
                    $_POST['conta'],
                    $_POST['tipo'],
                    $saldoInicial,
                    $saldoInicial,
                    $_POST['conta_id']
                ]);
                $_SESSION['msg_success'] = "Conta bancária atualizada com sucesso!";
                break;
                
            case 'alternar_status':
                $contaAtual = $db->fetchOne("SELECT status FROM contas_bancarias WHERE id = ?", [$_POST['conta_id']]);
                $novoStatus = $contaAtual['status'] === 'ativo' ? 'inativo' : 'ativo';
                
                $stmt = $db->prepare("UPDATE contas_bancarias SET status = ? WHERE id = ?");
                $stmt->execute([$novoStatus, $_POST['conta_id']]);
                $_SESSION['msg_success'] = "Status da conta alterado com sucesso!";
                break;
                
            case 'excluir':
                // Verificar se há transações vinculadas
                $transacoes = $db->fetchOne("SELECT COUNT(*) as total FROM transacoes_financeiras WHERE conta_bancaria_id = ?", [$_POST['conta_id']]);
                
                if ($transacoes['total'] > 0) {
                    $_SESSION['msg_error'] = "Não é possível excluir esta conta. Existem transações vinculadas a ela.";
                } else {
                    $stmt = $db->prepare("DELETE FROM contas_bancarias WHERE id = ?");
                    $stmt->execute([$_POST['conta_id']]);
                    $_SESSION['msg_success'] = "Conta bancária excluída com sucesso!";
                }
                break;
        }
        
        header('Location: contas_bancarias.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['msg_error'] = "Erro ao processar ação: " . $e->getMessage();
    }
}

// Obter dados para o dashboard
try {
    $db = Database::getInstance();
    
    // Cards do dashboard
    $contasAtivas = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(saldo_atual), 0) as saldo_total
        FROM contas_bancarias
        WHERE status = 'ativo'
    ");
    
    $contasInativas = $db->fetchOne("
        SELECT COUNT(*) as total
        FROM contas_bancarias
        WHERE status = 'inativo'
    ");
    
    $contasCorrente = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(saldo_atual), 0) as saldo_total
        FROM contas_bancarias
        WHERE tipo = 'corrente' AND status = 'ativo'
    ");
    
    $contasPoupanca = $db->fetchOne("
        SELECT COUNT(*) as total, COALESCE(SUM(saldo_atual), 0) as saldo_total
        FROM contas_bancarias
        WHERE tipo = 'poupanca' AND status = 'ativo'
    ");
    
    // Distribuição por banco (para gráfico)
    $distribuicaoBancos = $db->fetchAll("
        SELECT banco, COUNT(*) as total_contas, SUM(saldo_atual) as saldo_total
        FROM contas_bancarias
        WHERE status = 'ativo' AND banco IS NOT NULL AND banco != ''
        GROUP BY banco
        ORDER BY saldo_total DESC
        LIMIT 8
    ");
    
    // Maiores contas (por saldo)
    $maioresContas = $db->fetchAll("
        SELECT nome, banco, tipo, saldo_atual
        FROM contas_bancarias
        WHERE status = 'ativo'
        ORDER BY saldo_atual DESC
        LIMIT 5
    ");
    
    // Construir WHERE clause para a listagem
    $whereConditions = [];
    $params = [];
    
    if ($statusFilter) {
        $whereConditions[] = "status = ?";
        $params[] = $statusFilter;
    }
    
    if ($tipoFilter) {
        $whereConditions[] = "tipo = ?";
        $params[] = $tipoFilter;
    }
    
    if ($busca) {
        $whereConditions[] = "(nome LIKE ? OR banco LIKE ? OR agencia LIKE ? OR conta LIKE ?)";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
    }
    
    $whereClause = $whereConditions ? "WHERE " . implode(" AND ", $whereConditions) : "";
    
    // Contas bancárias com filtros
    $contasBancarias = $db->fetchAll("
        SELECT * FROM contas_bancarias
        $whereClause
        ORDER BY status DESC, nome ASC
    ", $params);
    
} catch (Exception $e) {
    $_SESSION['msg_error'] = "Erro ao carregar dados: " . $e->getMessage();
    $contasAtivas = ['total' => 0, 'saldo_total' => 0];
    $contasInativas = ['total' => 0];
    $contasCorrente = ['total' => 0, 'saldo_total' => 0];
    $contasPoupanca = ['total' => 0, 'saldo_total' => 0];
    $distribuicaoBancos = [];
    $maioresContas = [];
    $contasBancarias = [];
}

// Incluir o cabeçalho e sidebar padronizados
include_once 'includes/header_padronizado_novo.php';
include_once 'includes/sidebar_padronizado.php';
?>

<!-- CSS corrigido para layout perfeito -->
<style>
/* Reset específico para contas_bancarias.php */
.main-content {
    padding-top: 0 !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-top: 4rem !important; /* Espaço para header fixo */
}

.main-content .container {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
}

/* Container principal da página */
.page-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
}

/* Correção para gráficos */
.chart-container {
    height: 300px !important;
    position: relative;
    width: 100%;
    overflow: hidden;
}

.chart-container canvas {
    height: 100% !important;
    width: 100% !important;
    max-height: 300px !important;
}

/* Modais corrigidos */
.modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

.modal-content {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 100000 !important;
}

/* Responsividade melhorada */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0 !important;
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
    
    .page-container {
        padding: 1rem;
    }
    
    .lg\:col-span-2 {
        grid-column: span 1 !important;
    }
}

/* Botões com posicionamento correto */
.btn-primary {
    position: relative;
    z-index: 100;
}

/* Cards e grids */
.dashboard-grid {
    gap: 1.5rem;
}

/* Ocultar elementos escondidos */
.hidden {
    display: none !important;
}

/* Status badges */
.status-ativo {
    background-color: #10B981;
    color: white;
}

.status-inativo {
    background-color: #EF4444;
    color: white;
}
</style>

<!-- Conteúdo da página -->
<div class="page-container">
    <!-- Header da página -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Contas Bancárias</h1>
            <p class="text-gray-600">Gestão de Contas Bancárias e Saldos</p>
        </div>
        <div class="flex space-x-2">
            <button onclick="openModal('modalAdicionar')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors btn-primary">
                <i class="fas fa-plus mr-2"></i>Nova Conta Bancária
            </button>
        </div>
    </div>

    <!-- Dashboard Content - Cards -->
    <div class="dashboard-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Contas Ativas -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-university text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Contas Ativas</p>
                    <p class="text-2xl font-bold text-gray-900"><?= $contasAtivas['total'] ?></p>
                    <p class="text-sm text-green-600">R$ <?= number_format($contasAtivas['saldo_total'], 2, ',', '.') ?></p>
                </div>
            </div>
        </div>

        <!-- Contas Corrente -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-credit-card text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Conta Corrente</p>
                    <p class="text-2xl font-bold text-gray-900"><?= $contasCorrente['total'] ?></p>
                    <p class="text-sm text-blue-600">R$ <?= number_format($contasCorrente['saldo_total'], 2, ',', '.') ?></p>
                </div>
            </div>
        </div>

        <!-- Contas Poupança -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-piggy-bank text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Poupança</p>
                    <p class="text-2xl font-bold text-gray-900"><?= $contasPoupanca['total'] ?></p>
                    <p class="text-sm text-purple-600">R$ <?= number_format($contasPoupanca['saldo_total'], 2, ',', '.') ?></p>
                </div>
            </div>
        </div>

        <!-- Contas Inativas -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-ban text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Inativas</p>
                    <p class="text-2xl font-bold text-gray-900"><?= $contasInativas['total'] ?></p>
                    <p class="text-sm text-red-600">Desativadas</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos e Resumos -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Gráfico de Distribuição por Banco -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Distribuição por Banco</h3>
                <span class="text-sm text-gray-500">Saldo total por instituição</span>
            </div>
            <div class="chart-container">
                <canvas id="graficoDistribuicao"></canvas>
            </div>
        </div>

        <!-- Maiores Contas -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Maiores Saldos</h3>
            <div class="space-y-4">
                <?php if (empty($maioresContas)): ?>
                    <p class="text-gray-500 text-sm">Nenhuma conta encontrada.</p>
                <?php else: ?>
                    <?php foreach ($maioresContas as $conta): ?>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($conta['nome']) ?></p>
                                <p class="text-xs text-gray-500"><?= htmlspecialchars($conta['banco']) ?> - <?= ucfirst($conta['tipo']) ?></p>
                            </div>
                            <span class="text-sm font-medium text-green-600">
                                R$ <?= number_format($conta['saldo_atual'], 2, ',', '.') ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Filtros e Listagem -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <h2 class="text-xl font-semibold text-gray-900">Contas Bancárias</h2>
        </div>

        <!-- Filtros -->
        <form method="GET" class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <select name="status" class="form-select rounded-md border-gray-300">
                <option value="">Todos os Status</option>
                <option value="ativo" <?= $statusFilter === 'ativo' ? 'selected' : '' ?>>Ativo</option>
                <option value="inativo" <?= $statusFilter === 'inativo' ? 'selected' : '' ?>>Inativo</option>
            </select>
            
            <select name="tipo" class="form-select rounded-md border-gray-300">
                <option value="">Todos os Tipos</option>
                <option value="corrente" <?= $tipoFilter === 'corrente' ? 'selected' : '' ?>>Conta Corrente</option>
                <option value="poupanca" <?= $tipoFilter === 'poupanca' ? 'selected' : '' ?>>Poupança</option>
                <option value="investimento" <?= $tipoFilter === 'investimento' ? 'selected' : '' ?>>Investimento</option>
            </select>
            
            <input type="text" name="busca" placeholder="Buscar por nome, banco, agência..." value="<?= htmlspecialchars($busca) ?>" class="form-input rounded-md border-gray-300">
            
            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filtrar
                </button>
                <a href="contas_bancarias.php" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times mr-2"></i>Limpar
                </a>
            </div>
        </form>

        <!-- Tabela -->
        <div class="mt-6 overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conta</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banco</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo Atual</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($contasBancarias)): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                Nenhuma conta bancária encontrada.
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($contasBancarias as $conta): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($conta['nome']) ?></div>
                                    <?php if ($conta['agencia'] && $conta['conta']): ?>
                                        <div class="text-sm text-gray-500">Ag: <?= htmlspecialchars($conta['agencia']) ?> / Conta: <?= htmlspecialchars($conta['conta']) ?></div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900"><?= htmlspecialchars($conta['banco']) ?></td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?= ucfirst($conta['tipo']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    R$ <?= number_format($conta['saldo_atual'], 2, ',', '.') ?>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-<?= $conta['status'] ?>">
                                        <?= ucfirst($conta['status']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="abrirModalEditar(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['nome']) ?>', '<?= htmlspecialchars($conta['banco']) ?>', '<?= htmlspecialchars($conta['agencia']) ?>', '<?= htmlspecialchars($conta['conta']) ?>', '<?= $conta['tipo'] ?>', <?= $conta['saldo_inicial'] ?>)" 
                                                class="text-blue-600 hover:text-blue-900 transition-colors">
                                            <i class="fas fa-edit" title="Editar"></i>
                                        </button>
                                        <button onclick="abrirModalStatus(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['nome']) ?>', '<?= $conta['status'] ?>')" 
                                                class="text-yellow-600 hover:text-yellow-900 transition-colors">
                                            <i class="fas fa-toggle-<?= $conta['status'] === 'ativo' ? 'on' : 'off' ?>" title="Alterar Status"></i>
                                        </button>
                                        <button onclick="abrirModalExcluir(<?= $conta['id'] ?>, '<?= htmlspecialchars($conta['nome']) ?>')" 
                                                class="text-red-600 hover:text-red-900 transition-colors">
                                            <i class="fas fa-trash" title="Excluir"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Adicionar Conta -->
<div id="modalAdicionar" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Nova Conta Bancária</h3>
            <button type="button" onclick="closeModal('modalAdicionar')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="adicionar">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Nome da Conta</label>
                    <input type="text" name="nome" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Banco</label>
                    <input type="text" name="banco" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Agência</label>
                    <input type="text" name="agencia" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                    <input type="text" name="conta" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                    <select name="tipo" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Selecione...</option>
                        <option value="corrente">Conta Corrente</option>
                        <option value="poupanca">Poupança</option>
                        <option value="investimento">Investimento</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Saldo Inicial</label>
                    <input type="text" name="saldo_inicial" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="0,00">
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalAdicionar')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Adicionar
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Editar Conta -->
<div id="modalEditar" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Editar Conta Bancária</h3>
            <button type="button" onclick="closeModal('modalEditar')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="editar">
            <input type="hidden" name="conta_id" id="editar_conta_id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Nome da Conta</label>
                    <input type="text" name="nome" id="editar_nome" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Banco</label>
                    <input type="text" name="banco" id="editar_banco" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Agência</label>
                    <input type="text" name="agencia" id="editar_agencia" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                    <input type="text" name="conta" id="editar_conta" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                    <select name="tipo" id="editar_tipo" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Selecione...</option>
                        <option value="corrente">Conta Corrente</option>
                        <option value="poupanca">Poupança</option>
                        <option value="investimento">Investimento</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Saldo Inicial</label>
                    <input type="text" name="saldo_inicial" id="editar_saldo_inicial" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalEditar')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Salvar
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Alterar Status -->
<div id="modalStatus" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Alterar Status</h3>
            <button type="button" onclick="closeModal('modalStatus')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="alternar_status">
            <input type="hidden" name="conta_id" id="status_conta_id">
            
            <div>
                <p class="text-sm text-gray-600 mb-3">Tem certeza que deseja alterar o status desta conta?</p>
                <p id="status_conta_info" class="text-sm text-gray-800 bg-gray-50 p-3 rounded-md"></p>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalStatus')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
                    Alterar Status
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Excluir Conta -->
<div id="modalExcluir" class="hidden modal-overlay bg-gray-600 bg-opacity-50">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Excluir Conta</h3>
            <button type="button" onclick="closeModal('modalExcluir')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="excluir">
            <input type="hidden" name="conta_id" id="excluir_conta_id">
            
            <div>
                <p class="text-sm text-gray-600 mb-3">Tem certeza que deseja excluir esta conta?</p>
                <p id="excluir_conta_info" class="text-sm text-gray-800 bg-gray-50 p-3 rounded-md"></p>
                <p class="text-xs text-red-600 mt-2">Esta ação não pode ser desfeita.</p>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('modalExcluir')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancelar
                </button>
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Confirmar Exclusão
                </button>
            </div>
        </form>
    </div>
</div>

</div> <!-- Fechamento do page-container -->

<script>
// Funções para controle dos modais - versão melhorada
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('hidden');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Fechar modal ao clicar no overlay
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modalId);
        }
    });
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('hidden');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Fechar modais com ESC
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = ['modalAdicionar', 'modalEditar', 'modalStatus', 'modalExcluir'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal && !modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});

function abrirModalEditar(id, nome, banco, agencia, conta, tipo, saldoInicial) {
    document.getElementById('editar_conta_id').value = id;
    document.getElementById('editar_nome').value = nome;
    document.getElementById('editar_banco').value = banco;
    document.getElementById('editar_agencia').value = agencia;
    document.getElementById('editar_conta').value = conta;
    document.getElementById('editar_tipo').value = tipo;
    document.getElementById('editar_saldo_inicial').value = saldoInicial.toLocaleString('pt-BR', {minimumFractionDigits: 2});
    openModal('modalEditar');
}

function abrirModalStatus(id, nome, statusAtual) {
    document.getElementById('status_conta_id').value = id;
    const novoStatus = statusAtual === 'ativo' ? 'inativo' : 'ativo';
    document.getElementById('status_conta_info').innerHTML = `<strong>${nome}</strong><br>Status atual: <span class="font-semibold">${statusAtual}</span><br>Novo status: <span class="font-semibold">${novoStatus}</span>`;
    openModal('modalStatus');
}

function abrirModalExcluir(id, nome) {
    document.getElementById('excluir_conta_id').value = id;
    document.getElementById('excluir_conta_info').innerHTML = `<strong>${nome}</strong>`;
    openModal('modalExcluir');
}

// Gráfico de Distribuição por Banco
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('graficoDistribuicao').getContext('2d');
    
    // Preparar dados do PHP para JavaScript
    const distribuicaoData = <?= json_encode($distribuicaoBancos) ?>;
    
    // Processando dados para o gráfico
    const labels = [];
    const valores = [];
    const cores = [
        '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
        '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
    ];
    
    distribuicaoData.forEach((item, index) => {
        labels.push(item.banco);
        valores.push(parseFloat(item.saldo_total));
    });
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: valores,
                backgroundColor: cores.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const valor = context.parsed;
                            return context.label + ': R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2});
                        }
                    }
                }
            }
        }
    });
});

// Formatação de valores monetários
document.addEventListener('DOMContentLoaded', function() {
    const inputsSaldo = document.querySelectorAll('input[name="saldo_inicial"]');
    
    inputsSaldo.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = (value / 100).toFixed(2);
            value = value.replace('.', ',');
            value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            e.target.value = value;
        });
    });
});
</script>

<?php include_once 'includes/footer_padronizado.php'; ?>
