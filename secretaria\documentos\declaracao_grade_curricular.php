<?php
/**
 * ================================================================
 * FACIÊNCIA ERP - MÓDULO DE DECLARAÇÕES DE GRADE CURRICULAR
 * ================================================================
 * 
 * Sistema de gerenciamento de declarações de grade curricular
 * Permite gerar, visualizar e gerenciar declarações de grade curricular
 * em formato PDF
 * 
 * @version 1.0.0
 * <AUTHOR> ERP Development Team
 * @created 2025-06-23
 * 
 * Funcionalidades:
 * - Geração de declarações de grade curricular em PDF
 * - Visualização online de documentos
 * - Controle de solicitações e status
 * ================================================================
 */

// ================================================================
// CONFIGURAÇÕES INICIAIS DO SISTEMA
// ================================================================

// Configurações de depuração (remover em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configurações de performance para processamento de documentos
ini_set('memory_limit', '256M');           // Limite de memória aumentado
ini_set('max_execution_time', 60);         // Timeout de 1 minuto
set_time_limit(60);                        // Define timeout do script

// ================================================================
// CARREGAMENTO DE DEPENDÊNCIAS
// ================================================================

// Carrega as configurações do banco de dados (igual ao teste que funcionou)
require_once 'config/database.php';

// Carrega as classes principais do sistema
require_once 'includes/Database.php';      // Gerenciamento de banco de dados
require_once 'includes/Auth.php';          // Sistema de autenticação
require_once 'includes/Utils.php';         // Utilitários gerais

// Carrega as funções e inicializações
require_once 'includes/functions.php';     // Funções auxiliares
require_once 'includes/init.php';          // Inicialização do sistema

// ================================================================
// CONFIGURAÇÃO DA BIBLIOTECA TCPDF (GERAÇÃO DE PDF)
// ================================================================

// Verifica e carrega a biblioteca TCPDF para geração de PDFs
if (!class_exists('TCPDF')) {
    // Tenta carregar do diretório vendor (Composer)
    $tcpdf_path = 'vendor/tecnickcom/tcpdf/tcpdf.php';
    if (file_exists($tcpdf_path)) {
        require_once $tcpdf_path;
    } else {
        // Fallback para diretório local
        require_once 'includes/tcpdf/tcpdf.php';
    }
}

// ================================================================
// INICIALIZAÇÃO DA SESSÃO E AUTENTICAÇÃO
// ================================================================

// Inicia a sessão se necessário
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// ================================================================
// VERIFICAÇÃO DE AUTENTICAÇÃO E INICIALIZAÇÃO DE VARIÁVEIS
// ================================================================

// Verifica se o usuário está autenticado (comentado para testes)
// if (!isset($_SESSION['user_id'])) {
//     header('Location: login.php');
//     exit;
// }

// Para testes, simula um usuário logado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Administrador';
}

// Inicialização de variáveis do sistema
$titulo_pagina = 'Declarações de Grade Curricular';    // Título padrão da página
$view = 'listar';                                      // View padrão a ser exibida
$mensagem = $_SESSION['mensagem'] ?? null;             // Mensagem de feedback do sistema

// Limpa mensagens da sessão após uso
if (isset($_SESSION['mensagem'])) {
    unset($_SESSION['mensagem']);
}

// Conecta ao banco de dados (igual ao teste que funcionou)
$db = Database::getInstance();

// ================================================================
// FUNÇÕES AUXILIARES PARA OPERAÇÕES DE BANCO DE DADOS
// ================================================================

/**
 * Executa uma consulta SQL que retorna um único resultado
 */
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

/**
 * Executa uma consulta SQL que retorna múltiplos resultados
 */
function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        $result = $db->fetchAll($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL (fetchAll): ' . $e->getMessage());
        return $default;
    }
}

/**
 * Busca dados completos do aluno para geração de documentos
 * Baseado na função que funciona em declaracoes.php
 */
function buscarDadosAlunoCompletoParaDocumento($db, $aluno_id) {
    // PRIMEIRA TENTATIVA: Busca matrícula ativa mais recente (prioridade máxima)
    $sql = "SELECT a.*,
               c.nome as curso_nome,
               c.id as curso_id,
               c.carga_horaria as curso_carga_horaria,
               t.carga_horaria as turma_carga_horaria,
               t.nome as turma_nome,
               t.id as turma_id,
               m.id as matricula_id,
               m.status as matricula_status,
               m.polo_id as matricula_polo_id,
               p.nome as polo_nome,
               p.razao_social as polo_razao_social,
               p.mec as polo_mec,
               p.id as polo_id
            FROM alunos a
            LEFT JOIN matriculas m ON a.id = m.aluno_id AND m.status = 'ativo'
            LEFT JOIN cursos c ON m.curso_id = c.id
            LEFT JOIN turmas t ON m.turma_id = t.id
            LEFT JOIN polos p ON m.polo_id = p.id
            WHERE a.id = ?
            ORDER BY m.created_at DESC
            LIMIT 1";

    $aluno = executarConsulta($db, $sql, [$aluno_id]);

    // SEGUNDA TENTATIVA: Se não encontrou matrícula ativa, busca qualquer matrícula
    if (!$aluno || empty($aluno['polo_nome'])) {
        $sql = "SELECT a.*,
                   c.nome as curso_nome,
                   c.id as curso_id,
                   c.carga_horaria as curso_carga_horaria,
                   t.carga_horaria as turma_carga_horaria,
                   t.nome as turma_nome,
                   t.id as turma_id,
                   m.id as matricula_id,
                   m.status as matricula_status,
                   m.polo_id as matricula_polo_id,
                   p.nome as polo_nome,
                   p.razao_social as polo_razao_social,
                   p.mec as polo_mec,
                   p.id as polo_id
                FROM alunos a
                LEFT JOIN matriculas m ON a.id = m.aluno_id
                LEFT JOIN cursos c ON m.curso_id = c.id
                LEFT JOIN turmas t ON m.turma_id = t.id
                LEFT JOIN polos p ON m.polo_id = p.id
                WHERE a.id = ?
                ORDER BY m.created_at DESC
                LIMIT 1";

        $aluno = executarConsulta($db, $sql, [$aluno_id]);
    }

    // Se ainda não encontrou o polo na matrícula, tenta pelo polo_id do aluno
    if (!$aluno || empty($aluno['polo_nome'])) {
        if (!empty($aluno['polo_id'])) {
            $sql_polo = "SELECT nome, razao_social, mec FROM polos WHERE id = ?";
            $polo = executarConsulta($db, $sql_polo, [$aluno['polo_id']]);

            if ($polo && !empty($polo['nome'])) {
                $aluno['polo_nome'] = $polo['nome'];
                $aluno['polo_razao_social'] = $polo['razao_social'];
                $aluno['polo_mec'] = $polo['mec'] ?? '';
            }
        }
    }

    // Caso ainda não tenha polo, busca um polo padrão (primeiro ativo)
    if (!$aluno || empty($aluno['polo_nome'])) {
        $sql_polo_padrao = "SELECT id, nome, razao_social, mec FROM polos WHERE status = 'ativo' LIMIT 1";
        $polo_padrao = executarConsulta($db, $sql_polo_padrao, []);

        if ($polo_padrao) {
            $aluno['polo_nome'] = $polo_padrao['nome'] . ' (Padrão)';
            $aluno['polo_razao_social'] = $polo_padrao['razao_social'] ?? $polo_padrao['nome'] . ' (Padrão)';
            $aluno['polo_mec'] = $polo_padrao['mec'] ?? '';
            $aluno['polo_id'] = $polo_padrao['id'];
        } else {
            $aluno['polo_nome'] = 'Não informado';
            $aluno['polo_razao_social'] = 'Não informado';
            $aluno['polo_mec'] = 'Não informado';
            $aluno['polo_id'] = 1; // valor padrão
        }
    }

    // Se razao_social estiver vazia, usa o nome do polo
    if (empty($aluno['polo_razao_social'])) {
        $aluno['polo_razao_social'] = $aluno['polo_nome'];
    }

    return $aluno;
}

/**
 * Busca grade curricular da turma do aluno
 */
function buscarGradeCurricular($db, $turma_id) {
    // Primeiro, verifica se existe a tabela turma_disciplinas
    $sql_check = "SHOW TABLES LIKE 'turma_disciplinas'";
    $tabela_turma_disc = executarConsulta($db, $sql_check);

    if ($tabela_turma_disc) {
        // PRIORIDADE 1: Usa tabela turma_disciplinas (relacionamento específico)
        $sql = "SELECT
                    d.nome as disciplina_nome,
                    d.codigo as disciplina_codigo,
                    COALESCE(td.carga_horaria_turma, d.carga_horaria) as disciplina_carga_horaria,
                    COALESCE(td.carga_horaria_turma, d.carga_horaria) as carga_horaria_final,
                    COALESCE(td.periodo_turma, d.periodo) as periodo_letivo,
                    p.nome as professor_nome,
                    p.titulacao as professor_titulacao,
                    td.status as disciplina_status,
                    td.ordem
                FROM turma_disciplinas td
                JOIN disciplinas d ON td.disciplina_id = d.id
                LEFT JOIN professores p ON COALESCE(td.professor_id, d.professor_padrao_id) = p.id
                WHERE td.turma_id = ?
                AND td.status = 'ativo'
                AND d.status = 'ativo'
                ORDER BY
                    td.ordem ASC,
                    CASE
                        WHEN COALESCE(td.periodo_turma, d.periodo) IS NOT NULL
                        AND COALESCE(td.periodo_turma, d.periodo) != ''
                        THEN CAST(COALESCE(td.periodo_turma, d.periodo) AS UNSIGNED)
                        ELSE 999
                    END ASC,
                    d.nome ASC";

        $disciplinas = executarConsultaAll($db, $sql, [$turma_id]);

        if (!empty($disciplinas)) {
            return $disciplinas;
        }
    }

    // FALLBACK: Busca disciplinas do curso da turma (método anterior)
    $sql = "SELECT
                d.nome as disciplina_nome,
                d.codigo as disciplina_codigo,
                d.carga_horaria as disciplina_carga_horaria,
                d.carga_horaria as carga_horaria_final,
                d.periodo as periodo_letivo,
                p.nome as professor_nome,
                p.titulacao as professor_titulacao,
                d.status as disciplina_status,
                0 as ordem
            FROM disciplinas d
            JOIN turmas t ON d.curso_id = t.curso_id
            LEFT JOIN professores p ON d.professor_padrao_id = p.id
            WHERE t.id = ?
            AND d.status = 'ativo'
            AND d.carga_horaria > 0
            AND d.nome IS NOT NULL
            AND d.nome != ''
            ORDER BY
                CASE
                    WHEN d.periodo IS NOT NULL AND d.periodo != '' THEN CAST(d.periodo AS UNSIGNED)
                    ELSE 999
                END ASC,
                d.nome ASC
            LIMIT 50";

    return executarConsultaAll($db, $sql, [$turma_id]);
}

// ================================================================
// CONTROLADOR PRINCIPAL - PROCESSAMENTO DE AÇÕES
// ================================================================

// Obtém a ação solicitada (GET ou POST)
$action = $_GET['action'] ?? ($_POST['action'] ?? 'listar');
error_log("Ação solicitada: " . $action);

// Router principal - processa as diferentes ações do sistema
switch ($action) {
    
    case 'gerar':
        // Gera declaração de grade curricular em PDF
        $aluno_id = $_GET['aluno_id'] ?? null;

        if (empty($aluno_id)) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'ID do aluno não fornecido.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }

        // Busca dados completos do aluno usando a função que funciona
        $aluno = buscarDadosAlunoCompletoParaDocumento($db, $aluno_id);

        if (!$aluno) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'Aluno não encontrado.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }

        if (empty($aluno['turma_id'])) {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'Aluno não está matriculado em uma turma válida.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }

        // Busca grade curricular
        $disciplinas = buscarGradeCurricular($db, $aluno['turma_id']);

        if (empty($disciplinas)) {
            $_SESSION['mensagem'] = [
                'tipo' => 'aviso',
                'texto' => 'A grade curricular para esta turma não foi encontrada ou não contém disciplinas.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }

        // 1. Gerar código de verificação único
        $codigo_verificacao = mt_rand(100000, 999999);

        // 2. Buscar tipo de documento
        $sql_tipo = "SELECT id FROM tipos_documentos WHERE nome = 'Declaração de Grade Curricular' LIMIT 1";
        $tipo_doc = executarConsulta($db, $sql_tipo);
        
        if (!$tipo_doc) {
            // Se não existir, cria o tipo de documento
            $sql_criar_tipo = "INSERT INTO tipos_documentos (nome, descricao, status, created_at, updated_at, prazo) VALUES (?, ?, 'ativo', CURDATE(), CURDATE(), 5)";
            $db->query($sql_criar_tipo, ['Declaração de Grade Curricular', 'Declaração que comprova a grade curricular do aluno.']);
            $tipo_documento_id = $db->getConnection()->lastInsertId();
        } else {
            $tipo_documento_id = $tipo_doc['id'];
        }

        // 3. Montar dados para PDF padronizado
        $dados_documento = [
            'aluno_id' => $aluno['id'],
            'aluno_nome' => $aluno['nome'],
            'aluno_cpf' => $aluno['cpf'] ?? '',
            'matricula_numero' => $aluno['matricula_id'] ?? '',
            'curso_nome' => $aluno['curso_nome'] ?? '',
            'polo_nome' => $aluno['polo_nome'] ?? '',
            'polo_razao_social' => $aluno['polo_razao_social'] ?? '',
            'polo_mec' => $aluno['polo_mec'] ?? '',
            'disciplinas' => $disciplinas,
            'data_emissao' => date('d/m/Y'),
            'codigo_verificacao' => $codigo_verificacao
        ];

        // 4. Inserir o documento no banco de dados (arquivo será atualizado após geração)
        $sql_documento = "INSERT INTO documentos_emitidos
                         (solicitacao_id, arquivo, numero_documento, data_emissao, status,
                          aluno_id, matricula_id, curso_id, polo_id, tipo_documento_id,
                          data_validade, codigo_verificacao, data_solicitacao)
                         VALUES (?, '', ?, CURDATE(), 'emitido', ?, ?, ?, ?, ?,
                                DATE_ADD(CURDATE(), INTERVAL 1 YEAR), ?, CURDATE())";
        
        $params_documento = [
            0, // solicitacao_id (0 para emissão direta)
            $codigo_verificacao, // Usar o código gerado como número do documento
            $aluno['id'],
            $aluno['matricula_id'] ?? null,
            $aluno['curso_id'] ?? null,
            $aluno['polo_id'] ?? null,
            $tipo_documento_id,
            $codigo_verificacao // Usar o mesmo código para verificação
        ];

        try {
            $db->query($sql_documento, $params_documento);
            $documento_id = $db->getConnection()->lastInsertId();
            error_log("Documento de grade curricular gerado com ID: $documento_id e código: $codigo_verificacao");
        } catch (Exception $e) {
            error_log('Erro ao inserir documento de grade curricular: ' . $e->getMessage());
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'Ocorreu um erro ao registrar o documento no sistema. Tente novamente.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }

        // 5. Gerar PDF padronizado
        require_once 'views/documentos/gerar_pdf.php';
        $documento = [
            'dados_documento' => $dados_documento
        ];
        $arquivo_pdf = gerarPDF($documento, 'declaracao'); // ou 'grade_curricular' se houver template específico

        // 6. Atualizar o caminho do arquivo no banco de dados
        $sql_update = "UPDATE documentos_emitidos SET arquivo = ? WHERE id = ?";
        $db->query($sql_update, [$arquivo_pdf, $documento_id]);

        // 7. Redirecionar para download/visualização do PDF
        if ($arquivo_pdf && file_exists(__DIR__ . '/../' . $arquivo_pdf)) {
            header('Location: ../' . $arquivo_pdf);
            exit;
        } else {
            $_SESSION['mensagem'] = [
                'tipo' => 'erro',
                'texto' => 'Ocorreu um erro ao gerar o PDF. Tente novamente.'
            ];
            header('Location: declaracao_grade_curricular.php');
            exit;
        }
        
    case 'buscar_alunos':
        // Busca alunos via AJAX
        $termo = $_GET['termo'] ?? '';

        if (strlen($termo) < 2) {
            echo json_encode([]);
            exit;
        }

        // Busca considerando a estrutura real do banco
        $sql = "SELECT a.id, a.nome, a.cpf, a.email,
                       c.nome as curso_nome,
                       t.nome as turma_nome,
                       p.nome as polo_nome
                FROM alunos a
                LEFT JOIN matriculas m ON a.id = m.aluno_id AND m.status = 'ativo'
                LEFT JOIN cursos c ON COALESCE(m.curso_id, a.curso_id) = c.id
                LEFT JOIN turmas t ON COALESCE(m.turma_id, a.turma_id) = t.id
                LEFT JOIN polos p ON COALESCE(m.polo_id, a.polo_id) = p.id
                WHERE (a.nome LIKE ? OR a.cpf LIKE ? OR a.email LIKE ?)
                AND a.status = 'ativo'
                ORDER BY a.nome ASC
                LIMIT 20";

        $termo_busca = "%{$termo}%";
        $alunos = executarConsultaAll($db, $sql, [$termo_busca, $termo_busca, $termo_busca]);

        header('Content-Type: application/json');
        echo json_encode($alunos);
        exit;
        
    default:
        // Exibe a listagem
        $view = 'listar';
        break;
}

// ================================================================
// CARREGAMENTO DA VIEW
// ================================================================
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - Faciência ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php if (file_exists('includes/sidebar.php')) include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <?php if (file_exists('includes/header.php')) include 'includes/header.php'; ?>

            <!-- Main -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>
                    </div>

                    <?php if ($mensagem): ?>
                    <div class="bg-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-100 border-l-4 border-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-500 text-<?php echo $mensagem['tipo'] === 'sucesso' ? 'green' : ($mensagem['tipo'] === 'erro' ? 'red' : 'blue'); ?>-700 p-4 mb-6">
                        <?php echo $mensagem['texto']; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Conteúdo Principal -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-4">Gerar Declaração de Grade Curricular</h2>

                        <div class="mb-6">
                            <label for="busca_aluno" class="block text-sm font-medium text-gray-700 mb-2">
                                Buscar Aluno (Nome, CPF ou Email):
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="busca_aluno"
                                       class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="Digite o nome, CPF ou email do aluno..."
                                       autocomplete="off">
                                <div id="resultados_busca" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-60 overflow-y-auto"></div>
                            </div>
                        </div>

                        <div id="aluno_selecionado" class="hidden">
                            <div class="bg-purple-50 border border-purple-200 rounded-md p-4 mb-4">
                                <h3 class="font-semibold text-purple-700 mb-2">Aluno Selecionado:</h3>
                                <div id="dados_aluno"></div>
                                <div class="mt-4">
                                    <button id="btn_gerar" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                        <i class="fas fa-file-pdf mr-2"></i>
                                        Gerar Declaração de Grade Curricular
                                    </button>
                                    <button id="btn_cancelar" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded ml-2">
                                        Cancelar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Footer -->
            <?php if (file_exists('includes/footer.php')) include 'includes/footer.php'; ?>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        let alunoSelecionado = null;

        // Busca de alunos
        $('#busca_aluno').on('input', function() {
            const termo = $(this).val();

            if (termo.length < 2) {
                $('#resultados_busca').hide();
                return;
            }

            $.ajax({
                url: 'declaracao_grade_curricular.php',
                method: 'GET',
                data: {
                    action: 'buscar_alunos',
                    termo: termo
                },
                dataType: 'json',
                success: function(alunos) {
                    const resultados = $('#resultados_busca');
                    resultados.empty();

                    if (alunos.length === 0) {
                        resultados.append('<div class="p-3 text-gray-500">Nenhum aluno encontrado</div>');
                    } else {
                        alunos.forEach(function(aluno) {
                            const item = $(`
                                <div class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200" data-aluno='${JSON.stringify(aluno)}'>
                                    <div class="font-semibold">${aluno.nome}</div>
                                    <div class="text-sm text-gray-600">
                                        CPF: ${aluno.cpf || 'N/A'} |
                                        Curso: ${aluno.curso_nome || 'N/A'} |
                                        Turma: ${aluno.turma_nome || 'N/A'}
                                    </div>
                                </div>
                            `);
                            resultados.append(item);
                        });
                    }

                    resultados.show();
                },
                error: function() {
                    $('#resultados_busca').html('<div class="p-3 text-red-500">Erro ao buscar alunos</div>').show();
                }
            });
        });

        // Seleção de aluno
        $(document).on('click', '#resultados_busca > div', function() {
            const alunoData = $(this).data('aluno');
            if (alunoData) {
                alunoSelecionado = alunoData;

                $('#dados_aluno').html(`
                    <p><strong>Nome:</strong> ${alunoData.nome}</p>
                    <p><strong>CPF:</strong> ${alunoData.cpf || 'N/A'}</p>
                    <p><strong>Email:</strong> ${alunoData.email || 'N/A'}</p>
                    <p><strong>Curso:</strong> ${alunoData.curso_nome || 'N/A'}</p>
                    <p><strong>Turma:</strong> ${alunoData.turma_nome || 'N/A'}</p>
                    <p><strong>Polo:</strong> ${alunoData.polo_nome || 'N/A'}</p>
                `);

                $('#aluno_selecionado').show();
                $('#resultados_busca').hide();
                $('#busca_aluno').val(alunoData.nome);
            }
        });

        // Gerar declaração
        $('#btn_gerar').click(function() {
            if (alunoSelecionado) {
                window.open(`declaracao_grade_curricular.php?action=gerar&aluno_id=${alunoSelecionado.id}`, '_blank');
            }
        });

        // Cancelar seleção
        $('#btn_cancelar').click(function() {
            alunoSelecionado = null;
            $('#aluno_selecionado').hide();
            $('#busca_aluno').val('');
        });

        // Esconder resultados ao clicar fora
        $(document).click(function(e) {
            if (!$(e.target).closest('#busca_aluno, #resultados_busca').length) {
                $('#resultados_busca').hide();
            }
        });
    });
    </script>
</body>
</html>
