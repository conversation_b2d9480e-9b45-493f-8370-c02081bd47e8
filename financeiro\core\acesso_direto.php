<?php
/**
 * Acesso Direto ao Módulo Financeiro
 * 
 * Este arquivo permite acessar o módulo financeiro diretamente,
 * sem depender de redirecionamentos que podem causar loops.
 */

// Iniciar sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar login básico
// TEMPORARIAMENTE REMOVIDAS AS RESTRIÇÕES DE LOGIN
/*
if (!isset($_SESSION['user_id'])) {
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Acesso Restrito - Módulo Financeiro</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <style>
            body { 
                padding: 50px; 
                background-color: #f5f5f5;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .login-container {
                max-width: 500px;
                margin: 0 auto;
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h2 {
                color: #333;
                margin-bottom: 20px;
                text-align: center;
            }
            .btn-primary {
                background-color: #007bff;
                border-color: #007bff;
                width: 100%;
                padding: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="login-container">
                <h2>Acesso Restrito</h2>
                <div class="alert alert-warning">
                    <p>Você precisa estar logado para acessar o Módulo Financeiro.</p>
                </div>
                <div class="text-center mt-4">
                    <a href="../login.php" class="btn btn-primary">Fazer Login</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}
*/

// Determinar qual página deve ser exibida
$pagina = isset($_GET['pagina']) ? $_GET['pagina'] : 'index';

// Caminho seguro para o arquivo a ser incluído
$arquivo = null;
switch ($pagina) {
    case 'index':
        $arquivo = 'index.php';
        $titulo = 'Dashboard Financeiro';
        break;
    case 'contas_receber':
        $arquivo = 'contas_receber.php';
        $titulo = 'Contas a Receber';
        break;
    case 'contas_pagar':
        $arquivo = 'contas_pagar.php';
        $titulo = 'Contas a Pagar';
        break;
    case 'plano_contas':
        $arquivo = 'plano_contas_gerencial.php';
        $titulo = 'Plano de Contas';
        break;
    case 'transacoes':
        $arquivo = 'transacoes_financeiras.php';
        $titulo = 'Transações Financeiras';
        break;
    case 'relatorios':
        $arquivo = 'relatorios.php';
        $titulo = 'Relatórios Financeiros';
        break;
    default:
        $arquivo = 'index.php';
        $titulo = 'Dashboard Financeiro';
}

// Definir constantes
define('BASE_PATH', dirname(__FILE__));
define('ROOT_PATH', dirname(dirname(__FILE__)));
define('MODULO', 'financeiro');

// Incluir cabeçalho
include_once 'includes/header.php';
?>

<!-- Conteúdo principal do módulo financeiro -->
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4"><?php echo $titulo; ?></h2>
            
            <?php
            // Incluir o arquivo da página solicitada com caminho seguro
            if ($arquivo && file_exists($arquivo)) {
                // Extrair variáveis de GET para uso no arquivo incluído
                extract($_GET);
                
                // Incluir o arquivo
                include_once $arquivo;
            } else {
                echo '<div class="alert alert-danger">Página não encontrada.</div>';
            }
            ?>
        </div>
    </div>
</div>

<?php
// Incluir rodapé
include_once 'includes/footer.php';
?>
