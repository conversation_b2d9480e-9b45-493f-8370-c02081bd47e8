<?php
/**
 * Dashboard Financeiro - Módulo Financeiro
 * Sistema Faciência ERP - Versão FINAL CORRIGIDA
 */

require_once "includes/config.php";
$pageTitle = "Dashboard Financeiro";
$pdo = getConnection();

// Variáveis de controle
$action = $_GET["action"] ?? "listar";
$id = $_GET["id"] ?? null;
$mensagem = "";
$erro = "";

// Processamento de formulários
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        switch ($_POST["action"]) {
            case "salvar":
                // Lógica de salvamento aqui
                $mensagem = "Registro salvo com sucesso!";
                break;
                
            case "excluir":
                // Lógica de exclusão aqui
                $mensagem = "Registro excluído com sucesso!";
                break;
        }
    } catch (Exception $e) {
        $erro = "Erro: " . $e->getMessage();
    }
}

// Buscar dados
$registros = fetchData($pdo, "SELECT * FROM dashboard_financeiro ORDER BY id DESC");

include "includes/header_novo.php";
?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0"><i class="fas fa-chart-pie text-primary"></i> Dashboard Financeiro</h1>
        <p class="text-muted">Gestão e controle • <?php echo date("d/m/Y H:i"); ?></p>
    </div>
</div>

<!-- Mensagens -->
<?php if ($mensagem): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo $mensagem; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($erro): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle"></i> <?php echo $erro; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Ações principais -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Lista de Registros</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalForm">
                    <i class="fas fa-plus"></i> Novo Registro
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($registros)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>Nenhum registro encontrado</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalForm">
                            <i class="fas fa-plus"></i> Adicionar Primeiro Registro
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome/Descrição</th>
                                    <th>Status</th>
                                    <th>Data</th>
                                    <th width="120">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($registros as $registro): ?>
                                <tr>
                                    <td><?php echo $registro["id"]; ?></td>
                                    <td><?php echo htmlspecialchars($registro["nome"] ?? $registro["descricao"] ?? "N/A"); ?></td>
                                    <td>
                                        <span class="badge bg-success">Ativo</span>
                                    </td>
                                    <td><?php echo formatDate($registro["created_at"] ?? date("Y-m-d")); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editarRegistro(<?php echo $registro[\"id\"]; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="excluirRegistro(<?php echo $registro[\"id\"]; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Formulário -->
<div class="modal fade" id="modalForm" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Novo Registro
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="salvar">
                    <input type="hidden" name="id" id="registro_id">
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Nome/Descrição *</label>
                            <input type="text" name="nome" class="form-control" required>
                        </div>
                    </div>
                    
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editarRegistro(id) {
    // Implementar edição
    console.log("Editar registro:", id);
}

function excluirRegistro(id) {
    if (confirmarExclusao("este registro")) {
        // Implementar exclusão
        window.location.href = "?action=excluir&id=" + id;
    }
}
</script>

<?php include "includes/footer.php"; ?>