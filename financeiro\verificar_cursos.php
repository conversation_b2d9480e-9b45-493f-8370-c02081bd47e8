<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== ESTRUTURA DA TABELA CURSOS ===\n";
    $estrutura = $db->fetchAll("DESCRIBE cursos");
    foreach ($estrutura as $campo) {
        echo "{$campo['Field']} - {$campo['Type']} - {$campo['Null']} - {$campo['Default']}\n";
    }
    
} catch (Exception $e) {
    echo 'Erro: ' . $e->getMessage() . "\n";
}
?>
