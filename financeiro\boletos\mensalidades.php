<?php
/**
 * Gestão de Mensalidades - Módulo Financeiro
 * Sistema completo para geração e controle de mensalidades de alunos
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Verifica autenticação e permissão
Auth::requireLogin();
$userType = Auth::getUserType();
if (!in_array($userType, ['financeiro', 'admin_master'])) {
    $_SESSION['error'] = 'Você não tem permissão para acessar o módulo financeiro.';
    header('Location: ../index.php');
    exit;
}

$db = Database::getInstance();
$action = $_GET['action'] ?? 'listar';
$mensalidadeId = $_GET['id'] ?? null;

// Inicializa variáveis
$mensalidades = [];
$alunos = [];
$cursos = [];
$totalPendente = 0;
$totalPago = 0;
$totalVencidas = 0;
$totalMes = 0;

// Inicializa variáveis de filtro
$filtro = $_GET['filtro'] ?? '';
$busca = $_GET['busca'] ?? '';
$mesReferencia = $_GET['mes_referencia'] ?? '';
$cursoId = $_GET['curso_id'] ?? '';
$status = $_GET['status'] ?? '';
$buscaAluno = $_GET['busca_aluno'] ?? '';

// Processa ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'gerar_plano_pagamento') {
        try {
            $alunoId = $_POST['aluno_id'];
            $cursoId = $_POST['curso_id'] ?? null;
            $valorTotal = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_total']);
            $valorTotal = (float)$valorTotal; // Garante que seja numérico
            $numeroParcelas = (int)$_POST['numero_parcelas'];
            $dataVencimentoPrimeira = $_POST['data_vencimento_primeira'];
            $observacoes = $_POST['observacoes'] ?? '';
            
            // Validações
            if ($valorTotal <= 0) {
                throw new Exception('Valor total deve ser maior que zero.');
            }
            if ($numeroParcelas <= 0) {
                throw new Exception('Número de parcelas deve ser maior que zero.');
            }
            
            // Busca dados do aluno
            $aluno = $db->fetchOne("
                SELECT a.*, c.nome as curso_nome
                FROM alunos a 
                LEFT JOIN cursos c ON a.curso_id = c.id 
                WHERE a.id = ?", [$alunoId]);
            
            if (!$aluno) {
                throw new Exception('Aluno não encontrado.');
            }

            // Calcula valor da parcela
            $valorParcela = round($valorTotal / $numeroParcelas, 2);
            
            $db->beginTransaction();
            
            // Gera as mensalidades
            $geradas = 0;
            for ($i = 0; $i < $numeroParcelas; $i++) {
                $dataVencimento = date('Y-m-d', strtotime($dataVencimentoPrimeira . " +{$i} months"));
                $mesReferencia = date('Y-m-01', strtotime($dataVencimento));
                
                // Verifica se já existe mensalidade para este aluno no mês
                $existe = $db->fetchOne("
                    SELECT id FROM mensalidades_alunos
                    WHERE aluno_id = ? AND mes_referencia = ?
                ", [$alunoId, $mesReferencia]);

                if (!$existe) {
                    $dadosMensalidade = [
                        'aluno_id' => $alunoId,
                        'curso_id' => $cursoId,
                        'valor' => $valorParcela,
                        'data_vencimento' => $dataVencimento,
                        'mes_referencia' => $mesReferencia,
                        'desconto' => 0.00,
                        'multa' => 0.00,
                        'juros' => 0.00,
                        'status' => 'pendente',
                        'observacoes' => $observacoes . " (Parcela " . ($i + 1) . "/" . $numeroParcelas . ")",
                        'usuario_id' => Auth::getUserId(),
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    $db->insert('mensalidades_alunos', $dadosMensalidade);
                    $geradas++;
                }
            }
            
            $db->commit();
            $_SESSION['success'] = "$geradas mensalidade(s) gerada(s) com sucesso para {$aluno['nome']}!";
            header('Location: mensalidades.php');
            exit;

        } catch (Exception $e) {
            $db->rollback();
            $_SESSION['error'] = 'Erro ao gerar plano de pagamento: ' . $e->getMessage();
        }
    }

    if ($action === 'gerar_mensalidade_avulsa') {
        try {
            $alunoId = $_POST['aluno_id'];
            $cursoId = $_POST['curso_id'] ?? null;
            $valor = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor']);
            $dataVencimento = $_POST['data_vencimento'];
            $mesReferencia = $_POST['mes_referencia'];
            $descricao = $_POST['descricao'] ?? 'Mensalidade';
            
            $dadosMensalidade = [
                'aluno_id' => $alunoId,
                'curso_id' => $cursoId,
                'valor' => $valor,
                'data_vencimento' => $dataVencimento,
                'mes_referencia' => $mesReferencia,
                'desconto' => 0.00,
                'multa' => 0.00,
                'juros' => 0.00,
                'status' => 'pendente',
                'observacoes' => $descricao,
                'usuario_id' => Auth::getUserId()
            ];

            $db->insert('mensalidades_alunos', $dadosMensalidade);
            $_SESSION['success'] = 'Mensalidade gerada com sucesso!';
            header('Location: mensalidades.php');
            exit;

        } catch (Exception $e) {
            $_SESSION['error'] = 'Erro ao gerar mensalidade: ' . $e->getMessage();
        }
    }

    if ($action === 'registrar_pagamento' && $mensalidadeId) {
        try {
            $valorPago = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_pago']);
            $dataPagamento = $_POST['data_pagamento'];
            $formaPagamento = $_POST['forma_pagamento'];
            $desconto = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['desconto'] ?? '0');
            $multa = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['multa'] ?? '0');
            $juros = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['juros'] ?? '0');
            $observacoes = $_POST['observacoes'] ?? '';

            $db->beginTransaction();

            // Atualiza a mensalidade
            $dadosMensalidade = [
                'status' => 'pago',
                'data_pagamento' => $dataPagamento,
                'valor_pago' => $valorPago,
                'forma_pagamento' => $formaPagamento,
                'desconto' => $desconto,
                'multa' => $multa,
                'juros' => $juros,
                'observacoes' => $observacoes,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->update('mensalidades_alunos', $dadosMensalidade, 'id = ?', [$mensalidadeId]);

            // Busca dados da mensalidade para registrar na conta a receber
            $mensalidade = $db->fetchOne("
                SELECT ma.*, a.nome as aluno_nome, a.email as aluno_email, c.nome as curso_nome
                FROM mensalidades_alunos ma
                JOIN alunos a ON ma.aluno_id = a.id
                LEFT JOIN cursos c ON ma.curso_id = c.id
                WHERE ma.id = ?
            ", [$mensalidadeId]);

            if ($mensalidade) {
                // Registra na conta a receber
                $contaReceber = [
                    'descricao' => "Mensalidade {$mensalidade['curso_nome']} - {$mensalidade['aluno_nome']}",
                    'valor' => $valorPago,
                    'data_vencimento' => $mensalidade['data_vencimento'],
                    'data_recebimento' => $dataPagamento,
                    'cliente_id' => $mensalidade['aluno_id'],
                    'cliente_nome' => $mensalidade['aluno_nome'],
                    'cliente_tipo' => 'aluno',
                    'categoria_id' => 1, // Categoria "Mensalidades de Alunos"
                    'forma_recebimento' => $formaPagamento,
                    'status' => 'recebido',
                    'observacoes' => $observacoes,
                    'usuario_id' => Auth::getUserId()
                ];

                $db->insert('contas_receber', $contaReceber);

                // Registra transação financeira
                $transacao = [
                    'tipo' => 'receita',
                    'descricao' => $contaReceber['descricao'],
                    'valor' => $valorPago,
                    'data_transacao' => $dataPagamento,
                    'categoria_id' => 1,
                    'forma_pagamento' => $formaPagamento,
                    'referencia_tipo' => 'conta_receber',
                    'status' => 'efetivada',
                    'observacoes' => $observacoes,
                    'usuario_id' => Auth::getUserId()
                ];

                $db->insert('transacoes_financeiras', $transacao);
            }

            $db->commit();
            $_SESSION['success'] = 'Pagamento registrado com sucesso!';
            header('Location: mensalidades.php');
            exit;

        } catch (Exception $e) {
            $db->rollback();
            $_SESSION['error'] = 'Erro ao registrar pagamento: ' . $e->getMessage();
        }
    }

    if ($action === 'aplicar_desconto' && $mensalidadeId) {
        try {
            $desconto = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['desconto']);
            $motivo = $_POST['motivo_desconto'];

            $db->update('mensalidades_alunos', [
                'desconto' => $desconto,
                'observacoes' => $motivo,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$mensalidadeId]);

            $_SESSION['success'] = 'Desconto aplicado com sucesso!';
            header('Location: mensalidades.php');
            exit;

        } catch (Exception $e) {
            $_SESSION['error'] = 'Erro ao aplicar desconto: ' . $e->getMessage();
        }
    }
}

// Busca dados para exibição
if ($action === 'gerar') {
    // Busca alunos ativos para geração de mensalidades
    $buscaAluno = $_GET['busca_aluno'] ?? '';
    
    try {
        $whereAluno = "1=1"; // Removendo filtro de status temporariamente
        $paramsAluno = [];

        if ($buscaAluno) {
            $whereAluno .= " AND (a.nome LIKE ? OR a.cpf LIKE ? OR a.email LIKE ?)";
            $paramsAluno[] = "%$buscaAluno%";
            $paramsAluno[] = "%$buscaAluno%";
            $paramsAluno[] = "%$buscaAluno%";
        }

        $alunos = $db->fetchAll("
            SELECT a.id, a.nome, a.cpf, a.email, a.telefone, a.data_nascimento,
                   c.id as curso_id, c.nome as curso_nome, c.modalidade
            FROM alunos a
            LEFT JOIN cursos c ON a.curso_id = c.id
            WHERE $whereAluno
            ORDER BY a.nome
            LIMIT 50
        ", $paramsAluno);
        
        // Busca cursos para o formulário
        $cursos = $db->fetchAll("
            SELECT id, nome, modalidade 
            FROM cursos 
            WHERE 1=1
            ORDER BY nome
        ");
        
    } catch (Exception $e) {
        $alunos = [];
        $cursos = [];
        error_log("Erro ao buscar alunos para geração: " . $e->getMessage());
        $_SESSION['error'] = 'Erro ao buscar dados de alunos: ' . $e->getMessage();
    }
}

if ($action === 'listar') {
    try {
        $where = "1=1";
        $params = [];

        if ($status === 'pendentes') {
            $where .= " AND ma.status = 'pendente'";
        } elseif ($status === 'pagas') {
            $where .= " AND ma.status = 'pago'";
        } elseif ($status === 'vencidas') {
            $where .= " AND ma.status = 'pendente' AND ma.data_vencimento < CURDATE()";
        } elseif ($status === 'vencendo') {
            $where .= " AND ma.status = 'pendente' AND ma.data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
        }

        if ($mesReferencia) {
            $where .= " AND ma.mes_referencia = ?";
            $params[] = $mesReferencia;
        }
        
        if ($cursoId) {
            $where .= " AND ma.curso_id = ?";
            $params[] = $cursoId;
        }

        if ($busca) {
            $where .= " AND (a.nome LIKE ? OR a.cpf LIKE ? OR a.email LIKE ?)";
            $params[] = "%$busca%";
            $params[] = "%$busca%";
            $params[] = "%$busca%";
        }

        $mensalidades = $db->fetchAll("
            SELECT ma.*, 
                   a.nome as aluno_nome, a.cpf as aluno_cpf, 
                   a.email as aluno_email, a.telefone as aluno_telefone,
                   c.nome as curso_nome, c.modalidade as curso_modalidade
            FROM mensalidades_alunos ma
            JOIN alunos a ON ma.aluno_id = a.id
            LEFT JOIN cursos c ON ma.curso_id = c.id
            WHERE $where
            ORDER BY ma.data_vencimento DESC, a.nome ASC
        ", $params);
        
        // Busca cursos para filtro
        $cursos = $db->fetchAll("
            SELECT id, nome FROM cursos 
            WHERE 1=1
            ORDER BY nome
        ");
        
        // Calcula resumos
        $totalPendente = 0;
        $totalPago = 0;
        $totalVencidas = 0;
        $totalMes = count($mensalidades);
        
        foreach ($mensalidades as $mens) {
            if ($mens['status'] === 'pendente') {
                $totalPendente += $mens['valor'];
                if ($mens['data_vencimento'] < date('Y-m-d')) {
                    $totalVencidas += $mens['valor'];
                }
            } elseif ($mens['status'] === 'pago') {
                $totalPago += $mens['valor_pago'] ?? $mens['valor'];
            }
        }
        
    } catch (Exception $e) {
        $mensalidades = [];
        $cursos = [];
        $totalPendente = 0;
        $totalPago = 0;
        $totalVencidas = 0;
        $totalMes = 0;
        error_log("Erro em mensalidades: " . $e->getMessage());
        $_SESSION['error'] = 'Erro ao carregar mensalidades: ' . $e->getMessage();
    }
}

$pageTitle = 'Gestão de Mensalidades';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Faciência ERP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/financeiro.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col ml-64">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>

            <!-- Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">

                    <!-- Removido o alerta de configuração que estava causando problemas -->

                    <?php if ($action === 'listar'): ?>
                    <!-- Listagem de Mensalidades -->
                    <div class="mb-8">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Mensalidades de Alunos</h1>
                                <p class="text-gray-600 mt-2">Gerencie as mensalidades dos alunos específicos</p>
                            </div>
                            <a href="mensalidades.php?action=gerar" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>Gerar Mensalidades
                            </a>
                        </div>

                        <!-- Filtros -->
                        <div class="bg-white rounded-lg shadow p-4 mb-6">
                            <form method="GET" class="flex flex-wrap gap-4 items-end">
                                <input type="hidden" name="action" value="listar">
                                <div class="flex-1 min-w-64">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Buscar Aluno</label>
                                    <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>"
                                           placeholder="Nome ou email do aluno..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                    <select name="filtro" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Todas</option>
                                        <option value="pendentes" <?php echo ($_GET['filtro'] ?? '') === 'pendentes' ? 'selected' : ''; ?>>Pendentes</option>
                                        <option value="vencidas" <?php echo ($_GET['filtro'] ?? '') === 'vencidas' ? 'selected' : ''; ?>>Vencidas</option>
                                        <option value="pagas" <?php echo ($_GET['filtro'] ?? '') === 'pagas' ? 'selected' : ''; ?>>Pagas</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Mês de Referência</label>
                                    <input type="month" name="mes_referencia" value="<?php echo $_GET['mes_referencia'] ?? ''; ?>"
                                           class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    <i class="fas fa-search mr-2"></i>Buscar
                                </button>
                            </form>
                        </div>

                        <!-- Tabela -->
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-blue-600">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Aluno</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Curso</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Mês Ref.</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Valor</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Vencimento</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php if (empty($mensalidades)): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            Nenhuma mensalidade encontrada.
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($mensalidades as $mensalidade): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($mensalidade['aluno_nome']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($mensalidade['aluno_email']); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($mensalidade['curso_nome'] ?? '-'); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo date('m/Y', strtotime($mensalidade['mes_referencia'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            R$ <?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?>
                                            <?php if ($mensalidade['valor_pago'] && $mensalidade['valor_pago'] != $mensalidade['valor']): ?>
                                            <br><span class="text-green-600 text-xs">Pago: R$ <?php echo number_format($mensalidade['valor_pago'], 2, ',', '.'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php
                                            $dataVencimento = new DateTime($mensalidade['data_vencimento']);
                                            echo $dataVencimento->format('d/m/Y');

                                            if ($mensalidade['status'] === 'pendente') {
                                                $hoje = new DateTime();
                                                if ($dataVencimento < $hoje) {
                                                    $diff = $hoje->diff($dataVencimento);
                                                    echo '<br><span class="text-red-600 text-xs">Vencida há ' . $diff->days . ' dia(s)</span>';
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $statusClass = 'bg-gray-100 text-gray-800';
                                            $statusText = ucfirst($mensalidade['status']);

                                            if ($mensalidade['status'] === 'pago') {
                                                $statusClass = 'bg-green-100 text-green-800';
                                                $statusText = 'Paga';
                                            } elseif ($mensalidade['status'] === 'pendente') {
                                                $dataVencimento = new DateTime($mensalidade['data_vencimento']);
                                                $hoje = new DateTime();

                                                if ($dataVencimento < $hoje) {
                                                    $statusClass = 'bg-red-100 text-red-800';
                                                    $statusText = 'Vencida';
                                                } else {
                                                    $statusClass = 'bg-yellow-100 text-yellow-800';
                                                    $statusText = 'Pendente';
                                                }
                                            }
                                            ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                                <?php echo $statusText; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <?php if ($mensalidade['status'] === 'pendente'): ?>
                                            <button onclick="abrirModalPagamento(<?php echo $mensalidade['id']; ?>, '<?php echo htmlspecialchars($mensalidade['aluno_nome']); ?>', <?php echo $mensalidade['valor']; ?>)"
                                                    class="text-green-600 hover:text-green-900 mr-3">
                                                <i class="fas fa-money-check-alt"></i>
                                            </button>
                                            <?php endif; ?>
                                            <a href="#" class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <?php elseif ($action === 'gerar'): ?>
                    <!-- Formulário de Geração de Mensalidades -->
                    <div class="mb-8">
                        <div class="flex items-center mb-6">
                            <a href="mensalidades.php" class="text-blue-600 hover:text-blue-800 mr-4">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Gerar Mensalidades</h1>
                                <p class="text-gray-600 mt-2">Busque alunos e configure as mensalidades individualmente</p>
                            </div>
                        </div>

                        <!-- Busca de Aluno -->
                        <div class="bg-white rounded-lg shadow p-6 mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Buscar Aluno</h3>
                            <form method="GET" class="flex gap-4 items-end">
                                <input type="hidden" name="action" value="gerar">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nome, CPF ou Email</label>
                                    <input type="text" name="busca_aluno" 
                                           value="<?php echo htmlspecialchars($buscaAluno); ?>"
                                           placeholder="Digite o nome, CPF ou email do aluno..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    <i class="fas fa-search mr-2"></i>Buscar
                                </button>
                            </form>
                        </div>

                        <?php if ($buscaAluno || !empty($alunos)): ?>
                        <!-- Resultados da Busca -->
                        <div class="bg-white rounded-lg shadow p-6 mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                Alunos Encontrados 
                                <?php if ($buscaAluno): ?>
                                    <span class="text-sm text-gray-500">(buscando por: "<?php echo htmlspecialchars($buscaAluno); ?>")</span>
                                <?php endif; ?>
                            </h3>
                            
                            <?php if (empty($alunos)): ?>
                                <div class="text-center py-8 text-gray-500">
                                    <i class="fas fa-search text-4xl mb-3"></i>
                                    <p>Nenhum aluno encontrado</p>
                                    <p class="text-sm">Tente buscar por nome, CPF ou email</p>
                                </div>
                            <?php else: ?>
                                <div class="grid gap-4">
                                    <?php foreach ($alunos as $aluno): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <h4 class="text-lg font-medium text-gray-900"><?php echo htmlspecialchars($aluno['nome']); ?></h4>
                                                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2 text-sm text-gray-600">
                                                    <p><strong>ID:</strong> <?php echo htmlspecialchars($aluno['id'] ?? '-'); ?></p>
                                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($aluno['email'] ?? '-'); ?></p>
                                                    <p><strong>Telefone:</strong> <?php echo htmlspecialchars($aluno['telefone'] ?? '-'); ?></p>
                                                </div>
                                                <?php if ($aluno['curso_nome']): ?>
                                                <div class="mt-2 text-sm">
                                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                                        <?php echo htmlspecialchars($aluno['curso_nome'] ?? 'Sem curso'); ?>
                                                    </span>
                                                    <?php if (isset($aluno['valor_curso']) && $aluno['valor_curso']): ?>
                                                    <span class="ml-2 text-gray-600">
                                                        Valor padrão: R$ <?php echo number_format($aluno['valor_curso'], 2, ',', '.'); ?>
                                                    </span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex gap-2 ml-4">
                                                <button onclick="abrirFormularioPlano(<?php echo htmlspecialchars(json_encode($aluno)); ?>)"
                                                        class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 text-sm">
                                                    <i class="fas fa-calendar-alt mr-1"></i>Plano de Pagamento
                                                </button>
                                                <button onclick="abrirFormularioAvulsa(<?php echo htmlspecialchars(json_encode($aluno)); ?>)"
                                                        class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm">
                                                    <i class="fas fa-plus mr-1"></i>Mensalidade Avulsa
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!$buscaAluno): ?>
                        <!-- Instruções -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400 text-xl"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">Como gerar mensalidades</h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li><strong>Busque o aluno:</strong> Use o campo de busca acima para encontrar o aluno desejado</li>
                                            <li><strong>Plano de Pagamento:</strong> Gere várias mensalidades de uma vez com parcelamento flexível</li>
                                            <li><strong>Mensalidade Avulsa:</strong> Gere uma mensalidade específica para um mês determinado</li>
                                            <li><strong>Busca Inteligente:</strong> Funciona com nome completo, parcial, matrícula ou email</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/financeiro.js"></script>
    <script>
    // Modal para registrar pagamento
    function abrirModalPagamento(id, alunoNome, valor) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Registrar Pagamento de Mensalidade</h3>
                    <form method="POST" action="mensalidades.php?action=registrar_pagamento&id=${id}">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Aluno</label>
                                <p class="text-sm text-gray-900">${alunoNome}</p>
                                <p class="text-sm text-gray-600">Valor original: R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data do Pagamento *</label>
                                <input type="date" name="data_pagamento" value="${new Date().toISOString().split('T')[0]}" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Valor Pago *</label>
                                <input type="text" name="valor_pago" value="R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 currency-mask">
                            </div>
                            <div class="grid grid-cols-3 gap-2">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Desconto</label>
                                    <input type="text" name="desconto" value="R$ 0,00" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 currency-mask">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Multa</label>
                                    <input type="text" name="multa" value="R$ 0,00"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 currency-mask">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Juros</label>
                                    <input type="text" name="juros" value="R$ 0,00"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 currency-mask">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Forma de Pagamento</label>
                                <select name="forma_pagamento" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Selecione</option>
                                    <option value="dinheiro">Dinheiro</option>
                                    <option value="pix">PIX</option>
                                    <option value="transferencia">Transferência</option>
                                    <option value="cartao_debito">Cartão de Débito</option>
                                    <option value="cartao_credito">Cartão de Crédito</option>
                                    <option value="boleto">Boleto</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Observações</label>
                                <textarea name="observacoes" rows="2"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-2 mt-6">
                            <button type="button" onclick="this.closest('.fixed').remove()" 
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                                Cancelar
                            </button>
                            <button type="submit" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                                <i class="fas fa-dollar-sign mr-2"></i>Registrar Pagamento
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Aplicar máscara de moeda
        modal.querySelectorAll('.currency-mask').forEach(input => {
            applyCurrencyMask(input);
        });
    }

    // Modal para plano de pagamento
    function abrirFormularioPlano(aluno) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Gerar Plano de Pagamento</h3>
                    <div class="bg-blue-50 p-3 rounded-md mb-4">
                        <p class="font-medium">${aluno.nome}</p>
                        <p class="text-sm text-gray-600">CPF: ${aluno.cpf} | Curso: ${aluno.curso_nome || 'Não informado'}</p>
                    </div>
                    <form method="POST" action="mensalidades.php">
                        <input type="hidden" name="action" value="gerar_plano_pagamento">
                        <input type="hidden" name="aluno_id" value="${aluno.id}">
                        <input type="hidden" name="curso_id" value="${aluno.curso_id || ''}">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Valor Total do Curso *</label>
                                <input type="text" name="valor_total" required 
                                       value="${aluno.valor_mensalidade ? 'R$ ' + (aluno.valor_mensalidade * (aluno.duracao_meses || 12)).toLocaleString('pt-BR', {minimumFractionDigits: 2}) : ''}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 currency-mask">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Número de Parcelas *</label>
                                <select name="numero_parcelas" required onchange="calcularParcela(this)"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="">Selecione</option>
                                    <option value="1">1x (À vista)</option>
                                    <option value="2">2x</option>
                                    <option value="3">3x</option>
                                    <option value="6">6x</option>
                                    <option value="12" ${aluno.duracao_meses == 12 ? 'selected' : ''}>12x</option>
                                    <option value="24">24x</option>
                                    <option value="36">36x</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data da Primeira Parcela *</label>
                                <input type="date" name="data_vencimento_primeira" required
                                       value="${new Date(new Date().getFullYear(), new Date().getMonth() + 1, 10).toISOString().split('T')[0]}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Valor por Parcela</label>
                                <input type="text" readonly id="valor-parcela"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Observações</label>
                            <textarea name="observacoes" rows="2" placeholder="Ex: Acordo especial, desconto aplicado, etc."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
                        </div>
                        
                        <div class="flex justify-end space-x-2">
                            <button type="button" onclick="this.closest('.fixed').remove()" 
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                                Cancelar
                            </button>
                            <button type="submit" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                                <i class="fas fa-calendar-alt mr-2"></i>Gerar Plano
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Aplicar máscaras
        modal.querySelectorAll('.currency-mask').forEach(input => {
            applyCurrencyMask(input);
        });
    }

    // Modal para mensalidade avulsa
    function abrirFormularioAvulsa(aluno) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Gerar Mensalidade Avulsa</h3>
                    <div class="bg-blue-50 p-3 rounded-md mb-4">
                        <p class="font-medium">${aluno.nome}</p>
                        <p class="text-sm text-gray-600">CPF: ${aluno.cpf} | Curso: ${aluno.curso_nome || 'Não informado'}</p>
                    </div>
                    <form method="POST" action="mensalidades.php">
                        <input type="hidden" name="action" value="gerar_mensalidade_avulsa">
                        <input type="hidden" name="aluno_id" value="${aluno.id}">
                        <input type="hidden" name="curso_id" value="${aluno.curso_id || ''}">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Valor *</label>
                                <input type="text" name="valor" required 
                                       value="${aluno.valor_mensalidade ? 'R$ ' + aluno.valor_mensalidade.toLocaleString('pt-BR', {minimumFractionDigits: 2}) : ''}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 currency-mask">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Mês de Referência *</label>
                                <input type="month" name="mes_referencia" required
                                       value="${new Date().toISOString().slice(0, 7)}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data de Vencimento *</label>
                                <input type="date" name="data_vencimento" required
                                       value="${new Date(new Date().getFullYear(), new Date().getMonth() + 1, 10).toISOString().split('T')[0]}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                                <input type="text" name="descricao" 
                                       value="Mensalidade ${aluno.curso_nome || 'curso'}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-2 mt-6">
                            <button type="button" onclick="this.closest('.fixed').remove()" 
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                                Cancelar
                            </button>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                                <i class="fas fa-plus mr-2"></i>Gerar Mensalidade
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Aplicar máscaras
        modal.querySelectorAll('.currency-mask').forEach(input => {
            applyCurrencyMask(input);
        });
    }

    // Função para calcular valor da parcela
    function calcularParcela(select) {
        const modal = select.closest('.fixed');
        const valorTotalInput = modal.querySelector('[name="valor_total"]');
        const valorParcelaInput = modal.querySelector('#valor-parcela');
        
        if (valorTotalInput.value && select.value) {
            const valorTotal = parseFloat(valorTotalInput.value.replace(/[R$\s.]/g, '').replace(',', '.'));
            const numeroParcelas = parseInt(select.value);
            const valorParcela = valorTotal / numeroParcelas;
            
            valorParcelaInput.value = 'R$ ' + valorParcela.toLocaleString('pt-BR', {minimumFractionDigits: 2});
        }
    }

    // Máscara de moeda
    function applyCurrencyMask(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            value = (value / 100).toLocaleString('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            });
            e.target.value = value;
        });
    }

    // Aplicar máscaras aos campos existentes
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.currency-mask').forEach(input => {
            applyCurrencyMask(input);
        });
    });
    </script>
</body>
</html>