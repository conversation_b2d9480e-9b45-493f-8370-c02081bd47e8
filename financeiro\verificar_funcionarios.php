<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== ESTRUTURA DA TABELA FUNCIONARIOS ===\n";
    $estrutura = $db->fetchAll("DESCRIBE funcionarios");
    foreach ($estrutura as $campo) {
        echo "{$campo['Field']} - {$campo['Type']} - {$campo['Null']} - {$campo['Default']}\n";
    }
    
    echo "\n=== DADOS DOS FUNCIONÁRIOS ===\n";
    $funcionarios = $db->fetchAll("SELECT * FROM funcionarios LIMIT 5");
    print_r($funcionarios);
    
} catch (Exception $e) {
    echo 'Erro: ' . $e->getMessage() . "\n";
}
?>
