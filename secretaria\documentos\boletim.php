<?php
/**
 * <PERSON>ágina de Boletim - Sistema Faciência ERP
 * 
 * Esta página é responsável por visualizar boletins dos alunos
 */

// Inicialização do sistema
require_once __DIR__ . '/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissão para acessar boletins
exigirPermissao('boletim');

// Conecta ao banco de dados
$db = Database::getInstance();

// Obtém a ação solicitada
$action = $_GET['action'] ?? 'listar';

// Inicializa variáveis
$view = 'listar';
$titulo_pagina = 'Boletins';
$aluno = null;
$notas = [];
$curso = null;

// Processa a ação solicitada
switch ($action) {
    case 'visualizar':
        // Visualizar boletim de um aluno específico
        $aluno_id = $_GET['aluno_id'] ?? null;
        
        if (!$aluno_id) {
            setMensagem('erro', 'ID do aluno não informado.');
            redirect('notas.php');
        }
        
        // Buscar dados do aluno
        $aluno = $db->fetchOne("
            SELECT a.id, a.nome, a.cpf, a.email, a.telefone,
                   m.id as matricula_id, m.numero_matricula, m.data_matricula,
                   c.id as curso_id, c.nome as curso_nome, c.sigla as curso_sigla,
                   t.id as turma_id, t.nome as turma_nome
            FROM alunos a
            JOIN matriculas m ON a.id = m.aluno_id
            JOIN cursos c ON m.curso_id = c.id
            LEFT JOIN turmas t ON m.turma_id = t.id
            WHERE a.id = ? AND m.status = 'ativo'
            ORDER BY m.data_matricula DESC
            LIMIT 1
        ", [$aluno_id]);
        
        if (!$aluno) {
            setMensagem('erro', 'Aluno não encontrado ou sem matrícula ativa.');
            redirect('notas.php');
        }
        
        // Buscar notas do aluno
        $notas = $db->fetchAll("
            SELECT nd.id, nd.media_final as nota, nd.frequencia, nd.situacao, nd.observacoes,
                   nd.created_at, nd.updated_at,
                   d.id as disciplina_id, d.nome as disciplina_nome, d.codigo as disciplina_codigo,
                   d.carga_horaria, p.nome as professor_nome, p.formacao as professor_formacao
            FROM notas_disciplinas nd
            JOIN disciplinas d ON nd.disciplina_id = d.id
            LEFT JOIN professores p ON d.professor_padrao_id = p.id
            WHERE nd.aluno_id = ? AND nd.curso_id = ?
            ORDER BY d.nome ASC
        ", [$aluno_id, $aluno['curso_id']]);
        
        $titulo_pagina = 'Boletim - ' . $aluno['nome'];
        $view = 'visualizar';
        break;
        
    case 'imprimir':
        // Imprimir boletim (redireciona para histórico.php que já tem essa funcionalidade)
        $aluno_id = $_GET['aluno_id'] ?? null;
        
        if (!$aluno_id) {
            setMensagem('erro', 'ID do aluno não informado.');
            redirect('notas.php');
        }
        
        // Redireciona para o histórico que já tem a funcionalidade de PDF
        redirect('historico.php?action=gerar_pdf&aluno_id=' . $aluno_id);
        break;
        
    default:
        // Listar alunos para seleção de boletim
        $titulo_pagina = 'Selecionar Aluno para Boletim';
        
        // Buscar todos os alunos com matrícula ativa
        $alunos = $db->fetchAll("
            SELECT DISTINCT a.id, a.nome, a.cpf, a.email,
                   c.nome as curso_nome, t.nome as turma_nome,
                   COUNT(nd.id) as total_notas
            FROM alunos a
            JOIN matriculas m ON a.id = m.aluno_id
            JOIN cursos c ON m.curso_id = c.id
            LEFT JOIN turmas t ON m.turma_id = t.id
            LEFT JOIN notas_disciplinas nd ON a.id = nd.aluno_id AND nd.curso_id = c.id
            WHERE m.status = 'ativo'
            GROUP BY a.id, a.nome, a.cpf, a.email, c.nome, t.nome
            ORDER BY a.nome ASC
        ");
        
        $view = 'listar';
        break;
}

// Inclui apenas o CSS/JS e estrutura o HTML completo aqui
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($titulo_pagina); ?> - Sistema Faciência</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Estilos customizados -->
    <style>
        /* Variáveis CSS para consistência */
        :root {
            --color-primary: #3B82F6;
            --color-primary-dark: #2563EB;
            --color-secondary: #6B7280;
            --color-secondary-dark: #4B5563;
            --color-success: #10B981;
            --color-success-dark: #059669;
            --color-danger: #EF4444;
            --color-warning: #F59E0B;
            --border-radius: 0.5rem;
            --transition-default: all 0.2s ease-in-out;
        }

        /* Estilos para botões */
        .btn-primary {
            background-color: var(--color-primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            display: inline-flex;
            align-items: center;
            transition: var(--transition-default);
            text-decoration: none;
            font-weight: 600;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary:hover {
            background-color: var(--color-primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--color-secondary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            display: inline-flex;
            align-items: center;
            transition: var(--transition-default);
            text-decoration: none;
            font-weight: 600;
            border: none;
            cursor: pointer;
        }
        
        .btn-secondary:hover {
            background-color: var(--color-secondary-dark);
            transform: translateY(-1px);
        }
        
        .btn-success {
            background-color: var(--color-success);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            display: inline-flex;
            align-items: center;
            transition: var(--transition-default);
            text-decoration: none;
            font-weight: 600;
            border: none;
            cursor: pointer;
        }
        
        .btn-success:hover {
            background-color: var(--color-success-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        /* Estilos para mensagens */
        .message {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid;
            border-radius: var(--border-radius);
        }

        .message-success {
            background-color: #f0fdf4;
            border-color: var(--color-success);
            color: #166534;
        }

        .message-error {
            background-color: #fef2f2;
            border-color: var(--color-danger);
            color: #991b1b;
        }

        .message-warning {
            background-color: #fffbeb;
            border-color: var(--color-warning);
            color: #92400e;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .btn-primary,
            .btn-secondary,
            .btn-success {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>

            <!-- Conteúdo Principal -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                
                <!-- Cabeçalho da página -->
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-file-alt text-blue-500 text-2xl mr-3"></i>
                        <h1 class="text-3xl font-bold text-gray-800">
                            <?php echo htmlspecialchars($titulo_pagina); ?>
                        </h1>
                    </div>

                    <div class="flex space-x-3">
                        <?php if ($view === 'visualizar'): ?>
                        <a href="boletim.php?action=imprimir&aluno_id=<?php echo $aluno['id']; ?>" 
                           class="btn-success">
                            <i class="fas fa-print mr-2"></i>
                            Imprimir PDF
                        </a>
                        <a href="boletim.php" class="btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Voltar
                        </a>
                        <?php else: ?>
                        <a href="notas.php" class="btn-primary">
                            <i class="fas fa-edit mr-2"></i>
                            Lançar Notas
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Mensagens -->
                <?php if (isset($_SESSION['mensagem']) && isset($_SESSION['mensagem_tipo'])): ?>
                <?php 
                $tipo = $_SESSION['mensagem_tipo'];
                $classe_css = $tipo === 'sucesso' ? 'message message-success' : 
                             ($tipo === 'aviso' ? 'message message-warning' : 
                              'message message-error');
                $icone = $tipo === 'sucesso' ? 'fa-check-circle' : 
                        ($tipo === 'aviso' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle');
                ?>
                <div class="<?php echo $classe_css; ?>">
                    <div class="flex items-center">
                        <i class="fas <?php echo $icone; ?> mr-2"></i>
                        <span><?php echo htmlspecialchars($_SESSION['mensagem']); ?></span>
                    </div>
                </div>
                <?php
                unset($_SESSION['mensagem']);
                unset($_SESSION['mensagem_tipo']);
                endif;
                ?>

                <!-- Área de conteúdo dinâmico -->
                <?php
                $view_file = 'views/boletim/' . $view . '.php';
                if (file_exists($view_file)) {
                    include $view_file;
                } else {
                    echo '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';
                    echo '<strong>Erro:</strong> View não encontrada: ' . htmlspecialchars($view);
                    echo '</div>';
                }
                ?>
                
            </div>
        </main>
    </div>
</div>

</body>
</html>
