<?php
/**
 * TEMPLATE PADRÃO PARA PÁGINAS DO CORE
 * Use este template como base para criar/corrigir páginas
 */

// Inicialização obrigatória
require_once __DIR__ . '/../includes/init.php';

// Verificações de segurança
exigirLogin();
exigirPermissao('modulo_nome'); // Substitua pelo nome do módulo

// Configurações da página
$titulo_pagina = 'Nome da Página';
$conteudo_principal = '';

// Lógica da página aqui...
// Processe $_GET, $_POST, consultas ao banco, etc.

// Exemplo de conteúdo
$conteudo_principal = '
<div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-900">
            <i class="fas fa-icon-name text-blue-600 mr-3"></i>
            ' . $titulo_pagina . '
        </h1>
        <div class="flex space-x-3">
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Item
            </button>
        </div>
    </div>
    
    <!-- Conteúdo da página aqui -->
    <div class="space-y-6">
        <p class="text-gray-600">Conteúdo da sua página...</p>
    </div>
</div>
';

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - Faciência ERP</title>
    
    <!-- CSS Essencial -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    
    <!-- Scripts essenciais -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100">
    <!-- Layout Principal -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="flex-1 flex flex-col overflow-hidden ml-0 lg:ml-64">
            <!-- Header -->
            <?php include '../includes/header.php'; ?>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto max-w-7xl">
                    <?php echo $conteudo_principal; ?>
                </div>
            </main>

            <!-- Footer -->
            <?php include '../includes/footer.php'; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    
    <!-- Scripts específicos da página -->
    <script>
        // JavaScript específico da página aqui
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Página carregada: <?php echo $titulo_pagina; ?>');
        });
    </script>
</body>
</html>
