<?php
/**
 * Verificação da Conexão Principal do Módulo Financeiro
 * Este script testa a conexão com o banco de dados usando a configuração oficial
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo '<h1>Verificação da Conexão Principal do Módulo Financeiro</h1>';

// Incluir o arquivo de configuração
require_once 'includes/config.php';

echo '<h2>1. Testando conexão principal</h2>';
try {
    $pdo = getConnection();
    echo '<p style="color: green;">✓ Conexão principal: SUCESSO</p>';
    
    // Verificar charset
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'character_set_client'");
    $charset_info = $stmt->fetch();
    echo '<p>Charset do cliente: ' . $charset_info['Value'] . '</p>';
    
    // Verificar conexão às tabelas principais
    echo '<h3>Verificando tabelas principais:</h3>';
    $tabelas = [
        'plano_contas' => 'Plano de Contas',
        'contas_receber' => 'Contas a Receber',
        'contas_pagar' => 'Contas a Pagar',
        'transacoes_financeiras' => 'Transações Financeiras',
        'contas_bancarias' => 'Contas Bancárias',
        'centros_custo' => 'Centros de Custo'
    ];
    
    foreach ($tabelas as $tabela => $nome) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $tabela");
            $count = $stmt->fetchColumn();
            echo "<p style='color: green;'>✓ Tabela $nome: OK ($count registros)</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Erro na tabela $nome: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo '<p style="color: red;">✗ Erro na conexão principal: ' . $e->getMessage() . '</p>';
    
    echo '<h3>Solução recomendada:</h3>';
    echo '<p>1. Verifique se o serviço MySQL está ativo</p>';
    echo '<p>2. Verifique se o banco de dados existe</p>';
    echo '<p>3. Execute o script de restauração do banco: <a href="restaurar_banco_alternativo.php">restaurar_banco_alternativo.php</a></p>';
    echo '<p>4. Se o problema persistir, tente usar a versão alternativa: <a href="verificacao_final_alternativa.php">verificacao_final_alternativa.php</a></p>';
}

echo '<h2>Próximos passos:</h2>';
echo '<p><a href="index.php">Acessar Dashboard</a></p>';
echo '<p><a href="restaurar_banco_alternativo.php">Restaurar Banco de Dados (Versão Alternativa)</a></p>';
echo '<p><a href="verificacao_final_alternativa.php">Verificação Final Alternativa</a></p>';
echo '<p><a href="teste_conexao.php">Testar Diferentes Métodos de Conexão</a></p>';

?>
