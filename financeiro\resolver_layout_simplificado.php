<?php
/**
 * Solução Simplificada para Problemas de Layout
 * Este script aplica correções diretas para resolver problemas de layout no módulo financeiro
 * 
 * Data: 04/07/2025
 * Desenvolvedor: Suporte Técnico
 */

// Iniciar sessão
session_start();

// Verificar se o script está sendo executado diretamente
if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
    // Cabeçalho
    echo "<!DOCTYPE html>
    <html lang='pt-BR'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Correção Simplificada de Layout</title>
        <link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>
    </head>
    <body class='bg-gray-100 p-6'>
        <div class='max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6'>
            <h1 class='text-2xl font-bold text-center mb-6'>Correção Simplificada de Layout</h1>";

    // Criar o arquivo CSS simplificado
    $css_content = "/* Correção Simplificada de Layout - Módulo Financeiro */

/* Reset básico para evitar sobreposições */
* {
    box-sizing: border-box;
}

body {
    padding: 0;
    margin: 0;
    background-color: #F9FAFB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* CORREÇÃO DO HEADER */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: white;
    border-bottom: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    height: 64px;
}

@media (min-width: 1024px) {
    header > div {
        margin-left: 256px; /* Sidebar width */
    }
}

/* CORREÇÃO DA SIDEBAR */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 256px;
    z-index: 99;
    overflow-y: auto;
    transition: transform 0.3s ease;
    background-color: #065F46;
}

@media (max-width: 1023px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.sidebar-expanded {
        transform: translateX(0);
    }
}

/* CORREÇÃO DO CONTEÚDO PRINCIPAL */
.main-content {
    padding-top: 80px !important; /* Header height + space */
    min-height: calc(100vh - 64px);
    width: 100%;
    transition: margin-left 0.3s ease;
}

@media (min-width: 1024px) {
    .main-content {
        margin-left: 256px !important;
        width: calc(100% - 256px) !important;
    }
}

/* CORREÇÃO DE Z-INDEX */
.dropdown-menu {
    z-index: 101;
}

.sidebar-overlay {
    z-index: 98;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    display: none;
}

.sidebar-overlay.active {
    display: block;
}

/* CORREÇÕES PARA CARDS E GRIDS */
.grid {
    display: grid;
    grid-gap: 1rem;
}

.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #E5E7EB;
    overflow: hidden;
}";

    // Criar o arquivo JS simplificado
    $js_content = "// Script de Correção Simplificada de Layout

document.addEventListener('DOMContentLoaded', function() {
    // Toggle para o menu mobile
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    // Criar overlay para sidebar mobile
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay';
    overlay.className = 'sidebar-overlay';
    document.body.appendChild(overlay);
    
    // Função para toggle do sidebar
    function toggleSidebar() {
        sidebar.classList.toggle('sidebar-expanded');
        sidebar.classList.toggle('-translate-x-full');
        overlay.classList.toggle('active');
    }
    
    // Evento para o botão de menu mobile
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', toggleSidebar);
    }
    
    // Evento para o botão de fechar no sidebar
    const toggleSidebarButton = document.getElementById('toggle-sidebar');
    if (toggleSidebarButton) {
        toggleSidebarButton.addEventListener('click', toggleSidebar);
    }
    
    // Fechar sidebar ao clicar no overlay
    overlay.addEventListener('click', toggleSidebar);
    
    // Corrigir dropdowns
    function setupDropdowns() {
        // Notificações
        const notificacoesBtn = document.querySelector('[onclick=\"toggleNotificacoes()\"]');
        const notificacoesDropdown = document.getElementById('notificacoes-dropdown');
        
        // Config
        const configBtn = document.querySelector('[onclick=\"toggleConfigMenu()\"]');
        const configDropdown = document.getElementById('config-dropdown');
        
        // User
        const userBtn = document.querySelector('[onclick=\"toggleUserMenu()\"]');
        const userDropdown = document.getElementById('user-dropdown');
        
        // Fechar dropdowns ao clicar fora
        document.addEventListener('click', function(e) {
            if (notificacoesBtn && notificacoesDropdown && !notificacoesBtn.contains(e.target) && !notificacoesDropdown.contains(e.target)) {
                notificacoesDropdown.classList.add('hidden');
            }
            
            if (configBtn && configDropdown && !configBtn.contains(e.target) && !configDropdown.contains(e.target)) {
                configDropdown.classList.add('hidden');
            }
            
            if (userBtn && userDropdown && !userBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.add('hidden');
            }
        });
    }
    
    setupDropdowns();
});

// Redefinir funções de toggle para garantir compatibilidade
function toggleNotificacoes() {
    const dropdown = document.getElementById('notificacoes-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
        document.getElementById('user-dropdown')?.classList.add('hidden');
        document.getElementById('config-dropdown')?.classList.add('hidden');
    }
}

function toggleConfigMenu() {
    const dropdown = document.getElementById('config-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
        document.getElementById('notificacoes-dropdown')?.classList.add('hidden');
        document.getElementById('user-dropdown')?.classList.add('hidden');
    }
}

function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
        document.getElementById('notificacoes-dropdown')?.classList.add('hidden');
        document.getElementById('config-dropdown')?.classList.add('hidden');
    }
}";

    // Criar o arquivo index_simplificado.php
    $index_content = "<?php
// Iniciar a sessão, se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// TEMPORARIAMENTE REMOVIDAS AS RESTRIÇÕES DE LOGIN
// Comentado para permitir acesso livre ao módulo financeiro
/*
if (!isset(\$_SESSION['user_id'])) {
    echo '<h2>Acesso Restrito</h2>';
    echo '<p>Você precisa estar logado para acessar esta página.</p>';
    echo '<p><a href=\"../login.php\">Fazer Login</a></p>';
    exit;
}
*/

// Incluir os arquivos de configuração e funções
require_once 'includes/config.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Função para obter o nome do usuário (verifica se já existe)
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        // Tenta usar Auth::getUserName() primeiro
        if (method_exists('Auth', 'getUserName')) {
            return Auth::getUserName() ?? 'Usuário';
        }
        // Fallback para sessão direta
        return \$_SESSION['user_nome'] ?? \$_SESSION['user_name'] ?? 'Usuário';
    }
}

// Função para obter notificações financeiras
function getNotificacoesFinanceiras() {
    try {
        \$db = Database::getInstance();
        \$notificacoes = [];

        // Contas a pagar vencendo hoje
        \$contasVencendoHoje = \$db->fetchOne(\"SELECT COUNT(*) as total FROM contas_pagar WHERE data_vencimento = CURDATE() AND status = 'pendente'\");
        if (\$contasVencendoHoje['total'] > 0) {
            \$notificacoes[] = [
                'tipo' => 'contas_pagar',
                'titulo' => 'Contas a Pagar',
                'mensagem' => \$contasVencendoHoje['total'] . ' conta(s) vencem hoje',
                'icone' => 'fas fa-file-invoice',
                'cor' => 'text-red-600',
                'link' => 'contas_pagar.php?filtro=hoje'
            ];
        }

        // Contas a receber vencendo hoje
        \$recebimentosHoje = \$db->fetchOne(\"SELECT COUNT(*) as total FROM contas_receber WHERE data_vencimento = CURDATE() AND status = 'pendente'\");
        if (\$recebimentosHoje['total'] > 0) {
            \$notificacoes[] = [
                'tipo' => 'contas_receber',
                'titulo' => 'Contas a Receber',
                'mensagem' => \$recebimentosHoje['total'] . ' recebimento(s) previstos para hoje',
                'icone' => 'fas fa-file-invoice-dollar',
                'cor' => 'text-green-600',
                'link' => 'contas_receber.php?filtro=hoje'
            ];
        }

        return \$notificacoes;
    } catch (Exception \$e) {
        return [];
    }
}

// Título da página
\$pageTitle = \"Dashboard Financeiro\";

// Tentar carregar os dados do dashboard financeiro
try {
    \$db = Database::getInstance();
    
    // Dados para o Dashboard
    \$saldoAtual = \$db->fetchOne(\"
        SELECT COALESCE(SUM(CASE 
            WHEN tipo = 'receita' THEN valor 
            WHEN tipo = 'despesa' THEN -valor 
            ELSE 0 
        END), 0) as saldo 
        FROM transacoes_financeiras 
        WHERE data_transacao <= CURDATE()
    \");
    
    \$contasReceber = \$db->fetchOne(\"
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_receber
        WHERE status = 'pendente'
    \");
    
    \$contasPagar = \$db->fetchOne(\"
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_pagar
        WHERE status = 'pendente'
    \");
    
    \$contasVencidas = \$db->fetchOne(\"
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM contas_pagar
        WHERE status = 'pendente' AND data_vencimento < CURDATE()
    \");
    
    \$mensalidades = \$db->fetchOne(\"
        SELECT COUNT(*) as total, COALESCE(SUM(valor), 0) as valor_total
        FROM mensalidades
        WHERE status = 'pendente'
    \");
    
    \$receitasMensais = \$db->fetchAll(\"
        SELECT MONTH(data_transacao) as mes, YEAR(data_transacao) as ano, 
        SUM(valor) as total
        FROM transacoes_financeiras
        WHERE tipo = 'receita' AND data_transacao >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY YEAR(data_transacao), MONTH(data_transacao)
        ORDER BY ano ASC, mes ASC
    \");
    
    \$despesasMensais = \$db->fetchAll(\"
        SELECT MONTH(data_transacao) as mes, YEAR(data_transacao) as ano, 
        SUM(valor) as total
        FROM transacoes_financeiras
        WHERE tipo = 'despesa' AND data_transacao >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY YEAR(data_transacao), MONTH(data_transacao)
        ORDER BY ano ASC, mes ASC
    \");
    
    \$transacoesRecentes = \$db->fetchAll(\"
        SELECT t.*, c.nome as categoria_nome
        FROM transacoes_financeiras t
        LEFT JOIN categorias_financeiras c ON t.categoria_id = c.id
        ORDER BY t.data_transacao DESC
        LIMIT 5
    \");
} catch (Exception \$e) {
    \$_SESSION['msg_error'] = \"Erro ao carregar dados: \" . \$e->getMessage();
}
?><!DOCTYPE html>
<html lang=\"pt-BR\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title><?php echo \$pageTitle; ?> - Faciência ERP</title>
    
    <!-- Favicon -->
    <link rel=\"shortcut icon\" href=\"../assets/img/favicon.ico\" type=\"image/x-icon\">
    
    <!-- CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css\" rel=\"stylesheet\">
    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\" rel=\"stylesheet\">
    <link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\">
    
    <!-- CSS Simplificado -->
    <link href=\"assets/css/layout-simplificado.css\" rel=\"stylesheet\">
</head>
<body class=\"antialiased text-gray-800\">
    <?php
    // Obter notificações para o cabeçalho
    \$notificacoes = getNotificacoesFinanceiras();
    \$totalNotificacoes = count(\$notificacoes);
    ?>

    <!-- Header -->
    <header>
        <div class=\"px-4 sm:px-6 lg:px-8 py-4\">
            <div class=\"flex items-center justify-between\">
                <!-- Logo e Título para Mobile -->
                <div class=\"flex items-center\">
                    <!-- Botão de menu para mobile -->
                    <button id=\"mobile-menu-button\" class=\"lg:hidden mr-4 p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors\">
                        <i class=\"fas fa-bars text-lg\"></i>
                    </button>

                    <div class=\"hidden md:block\">
                        <h1 class=\"text-2xl font-bold text-gray-900\">
                            <?php echo \$pageTitle; ?>
                        </h1>
                        <p class=\"text-sm text-gray-600 mt-1\">Módulo Financeiro - Faciência ERP</p>
                    </div>
                    <div class=\"md:hidden\">
                        <h1 class=\"text-lg font-semibold text-gray-900\">Financeiro</h1>
                    </div>
                </div>

                <!-- Ações do Header -->
                <div class=\"flex items-center space-x-4\">
                    <!-- Indicadores Rápidos -->
                    <div class=\"hidden lg:flex items-center space-x-6 mr-6\">
                        <div class=\"text-center\">
                            <div class=\"text-lg font-bold text-green-600\" id=\"saldo-atual\">
                                R$ <?php echo number_format(\$saldoAtual['saldo'] ?? 0, 2, ',', '.'); ?>
                            </div>
                            <div class=\"text-xs text-gray-500\">Saldo Atual</div>
                        </div>
                        <div class=\"text-center\">
                            <div class=\"text-lg font-bold text-blue-600\" id=\"receber-mes\">
                                R$ <?php echo number_format(\$contasReceber['valor_total'] ?? 0, 2, ',', '.'); ?>
                            </div>
                            <div class=\"text-xs text-gray-500\">A Receber/Mês</div>
                        </div>
                        <div class=\"text-center\">
                            <div class=\"text-lg font-bold text-red-600\" id=\"pagar-mes\">
                                R$ <?php echo number_format(\$contasPagar['valor_total'] ?? 0, 2, ',', '.'); ?>
                            </div>
                            <div class=\"text-xs text-gray-500\">A Pagar/Mês</div>
                        </div>
                    </div>

                    <!-- Notificações -->
                    <div class=\"relative\">
                        <button onclick=\"toggleNotificacoes()\" class=\"relative p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors\">
                            <i class=\"fas fa-bell text-lg\"></i>
                            <?php if (\$totalNotificacoes > 0): ?>
                            <span class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">
                                <?php echo min(\$totalNotificacoes, 9); ?><?php echo \$totalNotificacoes > 9 ? '+' : ''; ?>
                            </span>
                            <?php endif; ?>
                        </button>

                        <!-- Dropdown de Notificações -->
                        <div id=\"notificacoes-dropdown\" class=\"hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">
                            <div class=\"p-4 border-b border-gray-200\">
                                <h3 class=\"text-sm font-semibold text-gray-900\">Notificações</h3>
                            </div>
                            <div class=\"max-h-64 overflow-y-auto\">
                                <?php if (empty(\$notificacoes)): ?>
                                <div class=\"p-4 text-center text-gray-500\">
                                    <i class=\"fas fa-check-circle text-2xl mb-2\"></i>
                                    <p>Nenhuma notificação pendente</p>
                                </div>
                                <?php else: ?>
                                <ul class=\"divide-y divide-gray-100\">
                                    <?php foreach (\$notificacoes as \$notificacao): ?>
                                    <li>
                                        <a href=\"<?php echo \$notificacao['link']; ?>\" class=\"block p-4 hover:bg-gray-50 transition-colors\">
                                            <div class=\"flex\">
                                                <div class=\"flex-shrink-0 <?php echo \$notificacao['cor']; ?>\">
                                                    <i class=\"<?php echo \$notificacao['icone']; ?>\"></i>
                                                </div>
                                                <div class=\"ml-3\">
                                                    <p class=\"text-sm font-medium text-gray-900\"><?php echo \$notificacao['titulo']; ?></p>
                                                    <p class=\"text-sm text-gray-500\"><?php echo \$notificacao['mensagem']; ?></p>
                                                </div>
                                            </div>
                                        </a>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php endif; ?>
                            </div>
                            <div class=\"p-3 border-t border-gray-100 text-center\">
                                <a href=\"#\" class=\"text-xs text-green-600 hover:text-green-700 font-medium\">Ver todas</a>
                            </div>
                        </div>
                    </div>

                    <!-- Menu do Usuário -->
                    <div class=\"relative\">
                        <button onclick=\"toggleUserMenu()\" class=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors\">
                            <img src=\"https://ui-avatars.com/api/?name=<?php echo urlencode(getUsuarioNome()); ?>&background=10B981&color=fff\"
                                 alt=\"Avatar\" class=\"w-8 h-8 rounded-full\">
                            <div class=\"hidden md:block text-left\">
                                <p class=\"text-sm font-medium text-gray-900\"><?php echo getUsuarioNome(); ?></p>
                                <p class=\"text-xs text-gray-500\">Financeiro</p>
                            </div>
                            <i class=\"fas fa-chevron-down text-gray-400 text-sm\"></i>
                        </button>

                        <!-- Dropdown do Usuário -->
                        <div id=\"user-dropdown\" class=\"hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">
                            <div class=\"py-1\">
                                <a href=\"../perfil.php\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">
                                    <i class=\"fas fa-user mr-2 text-gray-500\"></i> Meu Perfil
                                </a>
                                <a href=\"../configuracoes.php\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">
                                    <i class=\"fas fa-cog mr-2 text-gray-500\"></i> Configurações
                                </a>
                                <div class=\"border-t border-gray-100\"></div>
                                <a href=\"../logout.php\" class=\"block px-4 py-2 text-sm text-red-600 hover:bg-gray-100\">
                                    <i class=\"fas fa-sign-out-alt mr-2\"></i> Sair
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <div id=\"sidebar\" class=\"sidebar\">
        <!-- Logo -->
        <div class=\"p-4 flex items-center justify-between bg-green-900 border-b border-green-700\">
            <div class=\"flex items-center\">
                <div class=\"w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center shadow-lg\">
                    <i class=\"fas fa-dollar-sign text-white text-xl\"></i>
                </div>
                <div class=\"ml-3\">
                    <h1 class=\"text-white font-bold text-lg\">Financeiro</h1>
                    <p class=\"text-green-200 text-xs\">Faciência ERP</p>
                </div>
            </div>
            <!-- Botão de toggle para mobile -->
            <button id=\"toggle-sidebar\" class=\"text-white focus:outline-none lg:hidden\">
                <i class=\"fas fa-times\"></i>
            </button>
        </div>

        <!-- Menu de Navegação -->
        <div class=\"flex-1 overflow-y-auto py-4\">
            <div class=\"px-3\">
                <!-- Dashboard -->
                <div class=\"mb-6\">
                    <a href=\"index.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md bg-green-600\">
                        <i class=\"fas fa-tachometer-alt w-6\"></i>
                        <span>Dashboard</span>
                    </a>
                </div>

                <!-- Recursos Humanos -->
                <div class=\"mb-4\">
                    <p class=\"text-xs text-green-200 uppercase font-semibold mb-2 px-4\">Recursos Humanos</p>
                    <a href=\"funcionarios.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-users w-6\"></i>
                        <span>Funcionários</span>
                    </a>
                    <a href=\"folha_pagamento.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-money-check-alt w-6\"></i>
                        <span>Folha de Pagamento</span>
                    </a>
                </div>

                <!-- Contas e Pagamentos -->
                <div class=\"mb-4\">
                    <p class=\"text-xs text-green-200 uppercase font-semibold mb-2 px-4\">Contas e Pagamentos</p>
                    <a href=\"contas_pagar.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-file-invoice w-6\"></i>
                        <span>Contas a Pagar</span>
                    </a>
                    <a href=\"contas_receber.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-file-invoice-dollar w-6\"></i>
                        <span>Contas a Receber</span>
                    </a>
                    <a href=\"pagamentos.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-money-bill-wave w-6\"></i>
                        <span>Pagamentos</span>
                    </a>
                    <a href=\"mensalidades.php\" class=\"flex items-center py-3 px-4 text-white hover:bg-green-700 rounded-md\">
                        <i class=\"fas fa-graduation-cap w-6\"></i>
                        <span>Mensalidades</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Conteúdo principal -->
    <main class=\"main-content w-full px-4 py-6\">
        <div class=\"container mx-auto max-w-7xl\">
            <!-- Alerts -->
            <?php if (isset(\$_SESSION['msg_success'])): ?>
            <div class=\"mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm alert-dismissible\">
                <div class=\"flex\">
                    <div class=\"flex-shrink-0\">
                        <i class=\"fas fa-check-circle text-green-500\"></i>
                    </div>
                    <div class=\"ml-3\">
                        <p><?php echo \$_SESSION['msg_success']; ?></p>
                    </div>
                    <button class=\"ml-auto text-green-500 hover:text-green-700\" onclick=\"this.parentElement.parentElement.remove();\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>
            </div>
            <?php unset(\$_SESSION['msg_success']); ?>
            <?php endif; ?>

            <?php if (isset(\$_SESSION['msg_error'])): ?>
            <div class=\"mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm alert-dismissible\">
                <div class=\"flex\">
                    <div class=\"flex-shrink-0\">
                        <i class=\"fas fa-exclamation-circle text-red-500\"></i>
                    </div>
                    <div class=\"ml-3\">
                        <p><?php echo \$_SESSION['msg_error']; ?></p>
                    </div>
                    <button class=\"ml-auto text-red-500 hover:text-red-700\" onclick=\"this.parentElement.parentElement.remove();\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>
            </div>
            <?php unset(\$_SESSION['msg_error']); ?>
            <?php endif; ?>

            <!-- Dashboard Content - Cards -->
            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">
                <!-- Card Saldo Atual -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200 card\">
                    <div class=\"p-3 rounded-full bg-green-100 mr-4\">
                        <i class=\"fas fa-wallet text-green-600 text-xl\"></i>
                    </div>
                    <div>
                        <p class=\"text-sm text-gray-500 mb-1\">Saldo Atual</p>
                        <p class=\"text-xl font-bold text-gray-800\">
                            R$ <?php echo number_format(\$saldoAtual['saldo'] ?? 0, 2, ',', '.'); ?>
                        </p>
                    </div>
                </div>

                <!-- Card Contas a Receber -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200 card\">
                    <div class=\"p-3 rounded-full bg-blue-100 mr-4\">
                        <i class=\"fas fa-hand-holding-usd text-blue-600 text-xl\"></i>
                    </div>
                    <div>
                        <p class=\"text-sm text-gray-500 mb-1\">Contas a Receber</p>
                        <p class=\"text-xl font-bold text-gray-800\">
                            R$ <?php echo number_format(\$contasReceber['valor_total'] ?? 0, 2, ',', '.'); ?>
                        </p>
                        <p class=\"text-sm text-gray-500\">
                            <?php echo \$contasReceber['total'] ?? 0; ?> registros
                        </p>
                    </div>
                </div>

                <!-- Card Contas a Pagar -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200 card\">
                    <div class=\"p-3 rounded-full bg-red-100 mr-4\">
                        <i class=\"fas fa-file-invoice text-red-600 text-xl\"></i>
                    </div>
                    <div>
                        <p class=\"text-sm text-gray-500 mb-1\">Contas a Pagar</p>
                        <p class=\"text-xl font-bold text-gray-800\">
                            R$ <?php echo number_format(\$contasPagar['valor_total'] ?? 0, 2, ',', '.'); ?>
                        </p>
                        <p class=\"text-sm text-gray-500\">
                            <?php echo \$contasPagar['total'] ?? 0; ?> registros
                        </p>
                    </div>
                </div>

                <!-- Card Mensalidades -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 flex items-center border border-gray-200 card\">
                    <div class=\"p-3 rounded-full bg-purple-100 mr-4\">
                        <i class=\"fas fa-graduation-cap text-purple-600 text-xl\"></i>
                    </div>
                    <div>
                        <p class=\"text-sm text-gray-500 mb-1\">Mensalidades</p>
                        <p class=\"text-xl font-bold text-gray-800\">
                            R$ <?php echo number_format(\$mensalidades['valor_total'] ?? 0, 2, ',', '.'); ?>
                        </p>
                        <p class=\"text-sm text-gray-500\">
                            <?php echo \$mensalidades['total'] ?? 0; ?> pendentes
                        </p>
                    </div>
                </div>
            </div>

            <!-- Gráficos e Resumos -->
            <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">
                <!-- Gráfico de Receitas e Despesas -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200 card\">
                    <h2 class=\"text-lg font-semibold text-gray-800 mb-4\">Receitas x Despesas</h2>
                    <div class=\"h-64\">
                        <canvas id=\"graficoReceitasDespesas\"></canvas>
                    </div>
                </div>
                
                <!-- Transações Recentes -->
                <div class=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200 card\">
                    <div class=\"flex justify-between items-center mb-4\">
                        <h2 class=\"text-lg font-semibold text-gray-800\">Transações Recentes</h2>
                        <a href=\"transacoes_financeiras.php\" class=\"text-sm text-green-600 hover:text-green-700\">Ver todas</a>
                    </div>
                    
                    <?php if (empty(\$transacoesRecentes)): ?>
                    <div class=\"text-center p-4 text-gray-500\">
                        <p>Nenhuma transação encontrada</p>
                    </div>
                    <?php else: ?>
                    <div class=\"divide-y divide-gray-200\">
                        <?php foreach (\$transacoesRecentes as \$transacao): ?>
                        <div class=\"py-3 flex items-center justify-between\">
                            <div class=\"flex items-center\">
                                <div class=\"mr-3\">
                                    <?php if (\$transacao['tipo'] == 'receita'): ?>
                                    <div class=\"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center\">
                                        <i class=\"fas fa-arrow-down text-green-600\"></i>
                                    </div>
                                    <?php else: ?>
                                    <div class=\"w-10 h-10 rounded-full bg-red-100 flex items-center justify-center\">
                                        <i class=\"fas fa-arrow-up text-red-600\"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <p class=\"font-medium text-gray-800\"><?php echo \$transacao['descricao']; ?></p>
                                    <p class=\"text-sm text-gray-500\">
                                        <?php echo \$transacao['categoria_nome'] ?? 'Sem categoria'; ?> · 
                                        <?php echo date('d/m/Y', strtotime(\$transacao['data_transacao'])); ?>
                                    </p>
                                </div>
                            </div>
                            <div class=\"text-right\">
                                <p class=\"font-medium <?php echo \$transacao['tipo'] == 'receita' ? 'text-green-600' : 'text-red-600'; ?>\">
                                    <?php echo \$transacao['tipo'] == 'receita' ? '+' : '-'; ?> 
                                    R$ <?php echo number_format(\$transacao['valor'], 2, ',', '.'); ?>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Ações Rápidas -->
            <div class=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200 card mb-6\">
                <h2 class=\"text-lg font-semibold text-gray-800 mb-4\">Ações Rápidas</h2>
                
                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">
                    <a href=\"transacoes_financeiras.php?tipo=nova&modo=receita\" class=\"flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors\">
                        <div class=\"p-2 rounded-md bg-green-100 mr-3\">
                            <i class=\"fas fa-plus text-green-600\"></i>
                        </div>
                        <span class=\"font-medium text-gray-800\">Nova Receita</span>
                    </a>
                    
                    <a href=\"transacoes_financeiras.php?tipo=nova&modo=despesa\" class=\"flex items-center p-3 bg-red-50 hover:bg-red-100 rounded-lg transition-colors\">
                        <div class=\"p-2 rounded-md bg-red-100 mr-3\">
                            <i class=\"fas fa-minus text-red-600\"></i>
                        </div>
                        <span class=\"font-medium text-gray-800\">Nova Despesa</span>
                    </a>
                    
                    <a href=\"relatorios.php\" class=\"flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\">
                        <div class=\"p-2 rounded-md bg-blue-100 mr-3\">
                            <i class=\"fas fa-chart-pie text-blue-600\"></i>
                        </div>
                        <span class=\"font-medium text-gray-800\">Relatórios</span>
                    </a>
                    
                    <a href=\"contas_receber.php?filtro=atrasadas\" class=\"flex items-center p-3 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors\">
                        <div class=\"p-2 rounded-md bg-yellow-100 mr-3\">
                            <i class=\"fas fa-exclamation-triangle text-yellow-600\"></i>
                        </div>
                        <span class=\"font-medium text-gray-800\">Cobranças Atrasadas</span>
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class=\"bg-white shadow-sm border-t border-gray-200\">
        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">
            <div class=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">
                <div>
                    <p class=\"text-sm text-gray-500\">
                        &copy; <?php echo date('Y'); ?> Faciência ERP. Todos os direitos reservados.
                    </p>
                    <p class=\"text-xs text-gray-400 mt-1\">
                        Sistema de Gestão Educacional - Módulo Financeiro
                    </p>
                </div>
                <div class=\"flex items-center space-x-4\">
                    <a href=\"../ajuda.php\" class=\"text-sm text-gray-500 hover:text-green-600\">
                        <i class=\"fas fa-question-circle mr-1\"></i> Ajuda
                    </a>
                    <a href=\"../termos.php\" class=\"text-sm text-gray-500 hover:text-green-600\">
                        <i class=\"fas fa-file-contract mr-1\"></i> Termos de Uso
                    </a>
                    <a href=\"../privacidade.php\" class=\"text-sm text-gray-500 hover:text-green-600\">
                        <i class=\"fas fa-shield-alt mr-1\"></i> Privacidade
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src=\"https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js\"></script>
    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>
    <script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>
    <script src=\"https://cdn.jsdelivr.net/npm/sweetalert2@11\"></script>
    <script src=\"https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js\"></script>
    <script src=\"https://cdn.jsdelivr.net/npm/inputmask@5.0.8/dist/jquery.inputmask.min.js\"></script>
    <script src=\"assets/js/layout-simplificado.js\"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de receitas e despesas
        const ctx = document.getElementById('graficoReceitasDespesas').getContext('2d');
        
        // Processar dados para o gráfico
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        const receitasDados = Array(12).fill(0);
        const despesasDados = Array(12).fill(0);
        
        // Preencher dados de receitas
        <?php foreach (\$receitasMensais as \$receita): ?>
        receitasDados[<?php echo \$receita['mes'] - 1; ?>] = <?php echo \$receita['total']; ?>;
        <?php endforeach; ?>
        
        // Preencher dados de despesas
        <?php foreach (\$despesasMensais as \$despesa): ?>
        despesasDados[<?php echo \$despesa['mes'] - 1; ?>] = <?php echo \$despesa['total']; ?>;
        <?php endforeach; ?>
        
        // Configurar gráfico
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: meses,
                datasets: [
                    {
                        label: 'Receitas',
                        data: receitasDados,
                        backgroundColor: 'rgba(34, 197, 94, 0.5)',
                        borderColor: 'rgb(34, 197, 94)',
                        borderWidth: 1
                    },
                    {
                        label: 'Despesas',
                        data: despesasDados,
                        backgroundColor: 'rgba(239, 68, 68, 0.5)',
                        borderColor: 'rgb(239, 68, 68)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + context.raw.toLocaleString('pt-BR', {minimumFractionDigits: 2});
                            }
                        }
                    }
                }
            }
        });
    });
    </script>
</body>
</html>";

    // Escrever os arquivos
    if (!file_exists('assets/css')) {
        mkdir('assets/css', 0755, true);
        echo "<p class='text-green-600'>✅ Diretório assets/css criado</p>";
    }
    
    if (!file_exists('assets/js')) {
        mkdir('assets/js', 0755, true);
        echo "<p class='text-green-600'>✅ Diretório assets/js criado</p>";
    }
    
    file_put_contents('assets/css/layout-simplificado.css', $css_content);
    echo "<p class='text-green-600'>✅ Arquivo assets/css/layout-simplificado.css criado</p>";
    
    file_put_contents('assets/js/layout-simplificado.js', $js_content);
    echo "<p class='text-green-600'>✅ Arquivo assets/js/layout-simplificado.js criado</p>";
    
    file_put_contents('index_simplificado.php', $index_content);
    echo "<p class='text-green-600'>✅ Arquivo index_simplificado.php criado</p>";
    
    // Adicionar mensagem de sucesso na sessão
    $_SESSION['msg_success'] = "Correção simplificada de layout aplicada com sucesso! Acesse o arquivo index_simplificado.php para ver as alterações.";
    
    echo "<div class='mt-6 flex justify-center space-x-4'>
            <a href='index_simplificado.php' class='bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded'>
                Visualizar Index Simplificado
            </a>
            <a href='index.php' class='bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded'>
                Voltar ao Dashboard Original
            </a>
          </div>";
    
    echo "</div></body></html>";
}
