<?php
/**
 * Verificação da estrutura das tabelas - Folha de Pagamento
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';

echo "<h1>🔍 Verificação da Estrutura das Tabelas</h1>";

try {
    $db = Database::getInstance();
    
    // Verifica estrutura da tabela folha_pagamento
    echo "<h2>📋 Estrutura da tabela 'folha_pagamento'</h2>";
    try {
        $colunas = $db->fetchAll("DESCRIBE folha_pagamento");
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
        foreach ($colunas as $coluna) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($coluna['Field']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($coluna['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Verifica estrutura da tabela folha_pagamento_itens
    echo "<h2>📋 Estrutura da tabela 'folha_pagamento_itens'</h2>";
    try {
        $colunas = $db->fetchAll("DESCRIBE folha_pagamento_itens");
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
        foreach ($colunas as $coluna) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($coluna['Field']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($coluna['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Verifica estrutura da tabela funcionarios
    echo "<h2>👥 Estrutura da tabela 'funcionarios'</h2>";
    try {
        $colunas = $db->fetchAll("DESCRIBE funcionarios");
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
        foreach ($colunas as $coluna) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($coluna['Field']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($coluna['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Verifica dados existentes
    echo "<h2>📊 Dados Existentes</h2>";
    
    echo "<h3>Folhas de Pagamento:</h3>";
    try {
        $folhas = $db->fetchAll("SELECT * FROM folha_pagamento LIMIT 5");
        if (!empty($folhas)) {
            echo "<pre>" . htmlspecialchars(print_r($folhas, true)) . "</pre>";
        } else {
            echo "<p>Nenhuma folha encontrada.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h3>Funcionários:</h3>";
    try {
        $funcionarios = $db->fetchAll("SELECT * FROM funcionarios LIMIT 5");
        if (!empty($funcionarios)) {
            echo "<pre>" . htmlspecialchars(print_r($funcionarios, true)) . "</pre>";
        } else {
            echo "<p>Nenhum funcionário encontrado.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Erro Geral:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; padding: 8px; }
td { padding: 8px; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
