<?php
require_once '../includes/init.php';
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== ESTRUTURA DA TABELA FOLHA_PAGAMENTO ===\n";
    $estrutura = $db->fetchAll("DESCRIBE folha_pagamento");
    foreach ($estrutura as $campo) {
        echo "{$campo['Field']} - {$campo['Type']}\n";
    }
    
} catch (Exception $e) {
    echo 'Erro: ' . $e->getMessage() . "\n";
}
?>
