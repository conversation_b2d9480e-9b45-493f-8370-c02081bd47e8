<?php
/**
 * Listagem de Documentos Emitidos
 * Mostra todos os documentos gerados com códigos de verificação
 */

// Carrega as configurações do sistema
require_once 'config/config.php';

// Carrega as classes principais do sistema
require_once 'includes/Database.php';
require_once 'includes/Auth.php';
require_once 'includes/Utils.php';

// Carrega as funções e inicializações
require_once 'includes/functions.php';
require_once 'includes/init.php';

$titulo_pagina = 'Documentos Emitidos';

// Inicializa a conexão com o banco de dados
$db = Database::getInstance();

// Funções auxiliares para consultas
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

function executarConsultaAll($db, $sql, $params = [], $default = []) {
    try {
        $result = $db->fetchAll($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

// Parâmetros de filtro
$filtro_tipo = $_GET['tipo'] ?? '';
$filtro_aluno = $_GET['aluno'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Monta a query com filtros
$where_conditions = ["de.status = 'ativo'"];
$params = [];

if (!empty($filtro_tipo)) {
    $where_conditions[] = "de.tipo_documento_id = ?";
    $params[] = $filtro_tipo;
}

if (!empty($filtro_aluno)) {
    $where_conditions[] = "(a.nome LIKE ? OR a.cpf LIKE ?)";
    $params[] = "%$filtro_aluno%";
    $params[] = "%$filtro_aluno%";
}

if (!empty($filtro_data_inicio)) {
    $where_conditions[] = "de.data_emissao >= ?";
    $params[] = $filtro_data_inicio;
}

if (!empty($filtro_data_fim)) {
    $where_conditions[] = "de.data_emissao <= ?";
    $params[] = $filtro_data_fim;
}

$where_clause = implode(' AND ', $where_conditions);

// Query principal
$sql = "SELECT 
            de.*,
            a.nome as aluno_nome,
            a.cpf as aluno_cpf,
            c.nome as curso_nome,
            p.razao_social as polo_nome,
            td.nome as tipo_documento_nome
        FROM documentos_emitidos de
        LEFT JOIN alunos a ON de.aluno_id = a.id
        LEFT JOIN cursos c ON de.curso_id = c.id
        LEFT JOIN polos p ON de.polo_id = p.id
        LEFT JOIN tipos_documentos td ON de.tipo_documento_id = td.id
        WHERE $where_clause
        ORDER BY de.data_emissao DESC, de.id DESC
        LIMIT $per_page OFFSET $offset";

$documentos = executarConsultaAll($db, $sql, $params);

// Conta total para paginação
$sql_count = "SELECT COUNT(*) as total 
              FROM documentos_emitidos de
              LEFT JOIN alunos a ON de.aluno_id = a.id
              WHERE $where_clause";
$total_result = executarConsulta($db, $sql_count, $params);
$total_documentos = $total_result['total'] ?? 0;
$total_pages = ceil($total_documentos / $per_page);

// Busca tipos de documentos para o filtro
$tipos_documentos = executarConsultaAll($db, "SELECT id, nome FROM tipos_documentos WHERE status = 'ativo' ORDER BY nome");

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $titulo_pagina; ?> - FaCiencia ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <?php include 'includes/header.php'; ?>

            <!-- Main -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <div class="container mx-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold text-gray-800"><?php echo $titulo_pagina; ?></h1>
                        <div class="flex space-x-2">
                            <a href="verificar_documento.php" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                                <i class="fas fa-shield-alt mr-2"></i>
                                Verificar Documento
                            </a>
                        </div>
                    </div>

                    <!-- Filtros -->
                    <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-filter text-blue-500 mr-2"></i>
                            Filtros
                        </h2>
                        
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Documento:</label>
                                <select name="tipo" class="form-select w-full">
                                    <option value="">Todos os tipos</option>
                                    <?php foreach ($tipos_documentos as $tipo): ?>
                                    <option value="<?php echo $tipo['id']; ?>" <?php echo $filtro_tipo == $tipo['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($tipo['nome']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Aluno:</label>
                                <input type="text" name="aluno" value="<?php echo htmlspecialchars($filtro_aluno); ?>" 
                                       class="form-input w-full" placeholder="Nome ou CPF">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data Início:</label>
                                <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($filtro_data_inicio); ?>" 
                                       class="form-input w-full">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data Fim:</label>
                                <input type="date" name="data_fim" value="<?php echo htmlspecialchars($filtro_data_fim); ?>" 
                                       class="form-input w-full">
                            </div>
                            
                            <div class="flex items-end space-x-2">
                                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                                    <i class="fas fa-search mr-1"></i>
                                    Filtrar
                                </button>
                                <a href="documentos_emitidos.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                                    <i class="fas fa-times mr-1"></i>
                                    Limpar
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Estatísticas -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <?php
                        // Estatísticas rápidas
                        $stats = [
                            'total' => executarConsulta($db, "SELECT COUNT(*) as count FROM documentos_emitidos WHERE status = 'ativo'")['count'] ?? 0,
                            'hoje' => executarConsulta($db, "SELECT COUNT(*) as count FROM documentos_emitidos WHERE status = 'ativo' AND data_emissao = CURDATE()")['count'] ?? 0,
                            'mes' => executarConsulta($db, "SELECT COUNT(*) as count FROM documentos_emitidos WHERE status = 'ativo' AND MONTH(data_emissao) = MONTH(CURDATE()) AND YEAR(data_emissao) = YEAR(CURDATE())")['count'] ?? 0,
                            'expirados' => executarConsulta($db, "SELECT COUNT(*) as count FROM documentos_emitidos WHERE status = 'ativo' AND data_validade < CURDATE()")['count'] ?? 0
                        ];
                        ?>
                        
                        <div class="bg-white rounded-lg shadow p-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i class="fas fa-file-alt text-blue-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-500">Total</p>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['total']); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i class="fas fa-calendar-day text-green-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-500">Hoje</p>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['hoje']); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i class="fas fa-calendar-alt text-purple-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-500">Este Mês</p>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['mes']); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-red-100 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-500">Expirados</p>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['expirados']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Documentos -->
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-800">
                                Documentos Emitidos
                                <span class="text-sm font-normal text-gray-600">(<?php echo number_format($total_documentos); ?> encontrados)</span>
                            </h2>
                        </div>
                        
                        <?php if (empty($documentos)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-600">Nenhum documento encontrado.</p>
                        </div>
                        <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documento</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aluno</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Curso</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Emissão</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validade</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($documentos as $doc): 
                                        $expirado = strtotime($doc['data_validade']) < time();
                                    ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($doc['tipo_documento_nome']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    ID: <?php echo $doc['id']; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($doc['aluno_nome']); ?>
                                                </div>
                                                <?php if (!empty($doc['aluno_cpf'])): ?>
                                                <div class="text-sm text-gray-500">
                                                    CPF: <?php echo htmlspecialchars($doc['aluno_cpf']); ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($doc['curso_nome'] ?? 'N/A'); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo date('d/m/Y', strtotime($doc['data_emissao'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $expirado ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'; ?>">
                                                <?php echo date('d/m/Y', strtotime($doc['data_validade'])); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">
                                                <?php echo htmlspecialchars($doc['codigo_verificacao']); ?>
                                            </code>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="verificar_documento.php?codigo=<?php echo urlencode($doc['codigo_verificacao']); ?>" 
                                               target="_blank"
                                               class="text-blue-600 hover:text-blue-900 mr-3">
                                                <i class="fas fa-shield-alt mr-1"></i>
                                                Verificar
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Paginação -->
                        <?php if ($total_pages > 1): ?>
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Anterior
                                </a>
                                <?php endif; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Próximo
                                </a>
                                <?php endif; ?>
                            </div>
                            
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Mostrando <span class="font-medium"><?php echo $offset + 1; ?></span> a 
                                        <span class="font-medium"><?php echo min($offset + $per_page, $total_documentos); ?></span> de 
                                        <span class="font-medium"><?php echo $total_documentos; ?></span> resultados
                                    </p>
                                </div>
                                
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <a href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_diff_key($_GET, ['page' => ''])); ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?php echo $i == $page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                        <?php endfor; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>

            <!-- Footer -->
            <?php include 'includes/footer.php'; ?>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
