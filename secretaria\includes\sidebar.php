<!-- Sidebar Moderno - Módulo Secretaria -->
<div id="sidebar" class="sidebar sidebar-expanded bg-purple-600 text-white flex flex-col w-64 min-h-screen fixed left-0 top-0 transition-all duration-300 z-10">
    <!-- Logo -->
    <div class="p-4 flex items-center justify-between bg-purple-700 border-b border-purple-500">
        <div class="flex items-center sidebar-logo-full">
            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center shadow-lg">
                <i class="fas fa-graduation-cap text-white text-xl"></i>
            </div>
            <div class="ml-3">
                <h1 class="text-white font-bold text-lg">Secretaria</h1>
                <p class="text-purple-100 text-xs">Faciência ERP</p>
            </div>
        </div>
        <!-- Botão de toggle para mobile -->
        <button id="toggle-sidebar" class="text-white focus:outline-none lg:hidden">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- <PERSON><PERSON> -->
    <div class="flex-1 overflow-y-auto">
        <div class="px-3 py-4">
            <!-- Dashboard -->
            <div class="mb-6">
                <a href="index.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-tachometer-alt w-6"></i>
                    <span class="sidebar-label">Dashboard</span>
                </a>
            </div>

            <!-- Gestão Acadêmica -->
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Gestão Acadêmica</p>
                <!-- Menu Alunos com Submenu -->
                <div class="sidebar-dropdown">
                    <a href="#" class="sidebar-item sidebar-dropdown-toggle flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo in_array(basename($_SERVER['PHP_SELF']), ['alunos.php', 'acessos_alunos.php']) ? 'active bg-purple-500' : ''; ?>">
                        <i class="fas fa-user-graduate w-6"></i>
                        <span class="sidebar-label">Alunos</span>
                        <i class="fas fa-chevron-down ml-auto sidebar-dropdown-arrow transition-transform duration-200"></i>
                    </a>
                    <div class="sidebar-dropdown-menu ml-6 mt-1 space-y-1 <?php echo in_array(basename($_SERVER['PHP_SELF']), ['alunos.php', 'acessos_alunos.php']) ? 'block' : 'hidden'; ?>">
                        <a href="core/alunos.php" class="sidebar-item flex items-center py-2 px-4 text-purple-100 hover:bg-purple-500 rounded-md text-sm <?php echo basename($_SERVER['PHP_SELF']) == 'alunos.php' ? 'active bg-purple-500 text-white' : ''; ?>">
                            <i class="fas fa-list w-4 mr-2"></i>
                            <span class="sidebar-label">Gerenciar Alunos</span>
                        </a>
                        <a href="core/acessos_alunos.php" class="sidebar-item flex items-center py-2 px-4 text-purple-100 hover:bg-purple-500 rounded-md text-sm <?php echo basename($_SERVER['PHP_SELF']) == 'acessos_alunos.php' ? 'active bg-purple-500 text-white' : ''; ?>">
                            <i class="fas fa-user-lock w-4 mr-2"></i>
                            <span class="sidebar-label">Criar Acessos Portal</span>
                        </a>
                    </div>
                </div>
                <a href="core/matriculas.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'matriculas.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-file-signature w-6"></i>
                    <span class="sidebar-label">Matrículas</span>
                </a>
                <a href="core/notas.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'notas.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-clipboard-list w-6"></i>
                    <span class="sidebar-label">Notas e Frequências</span>
                </a>
                <a href="documentos/declaracoes.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'declaracoes.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-file-alt w-6"></i>
                    <span class="sidebar-label">Declarações</span>
                </a>
                <a href="core/historicos.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'historicos.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-graduation-cap w-6"></i>
                    <span class="sidebar-label">Históricos</span>
                </a>
            </div>

            <!-- Estrutura Institucional -->
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Estrutura Institucional</p>
                <a href="core/professores.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'professores.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-chalkboard-teacher w-6"></i>
                    <span class="sidebar-label">Professores</span>
                </a>
                <a href="core/polos.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'polos.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-university w-6"></i>
                    <span class="sidebar-label">Polos</span>
                </a>
                <a href="core/cursos.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'cursos.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-graduation-cap w-6"></i>
                    <span class="sidebar-label">Cursos</span>
                </a>
                <a href="core/turmas.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'turmas.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-users w-6"></i>
                    <span class="sidebar-label">Turmas</span>
                </a>
                <!-- Disciplinas com submenu -->
                <div class="relative">
                    <button class="sidebar-item flex items-center justify-between w-full py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo in_array(basename($_SERVER['PHP_SELF']), ['disciplinas.php', 'turma_disciplinas.php', 'declaracao_grade_curricular.php']) ? 'active bg-purple-500' : ''; ?>" onclick="toggleSubmenu('disciplinas-submenu')">
                        <div class="flex items-center">
                            <i class="fas fa-book w-6"></i>
                            <span class="sidebar-label">Disciplinas</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs transition-transform duration-200" id="disciplinas-submenu-icon"></i>
                    </button>
                    <div id="disciplinas-submenu" class="ml-6 mt-1 space-y-1 <?php echo in_array(basename($_SERVER['PHP_SELF']), ['disciplinas.php', 'turma_disciplinas.php', 'declaracao_grade_curricular.php']) ? '' : 'hidden'; ?>">
                        <a href="core/disciplinas.php" class="sidebar-item flex items-center py-2 px-4 text-purple-100 hover:text-white hover:bg-purple-500 rounded-md text-sm <?php echo basename($_SERVER['PHP_SELF']) == 'disciplinas.php' ? 'text-white bg-purple-500' : ''; ?>">
                            <i class="fas fa-list w-5 text-xs"></i>
                            <span class="sidebar-label">Gerenciar Disciplinas</span>
                        </a>
                        <a href="core/turma_disciplinas.php" class="sidebar-item flex items-center py-2 px-4 text-purple-100 hover:text-white hover:bg-purple-500 rounded-md text-sm <?php echo basename($_SERVER['PHP_SELF']) == 'turma_disciplinas.php' ? 'text-white bg-purple-500' : ''; ?>">
                            <i class="fas fa-link w-5 text-xs"></i>
                            <span class="sidebar-label">Turma-Disciplinas</span>
                        </a>
                        <a href="documentos/declaracao_grade_curricular.php" class="sidebar-item flex items-center py-2 px-4 text-purple-100 hover:text-white hover:bg-purple-500 rounded-md text-sm <?php echo basename($_SERVER['PHP_SELF']) == 'declaracao_grade_curricular.php' ? 'text-white bg-purple-500' : ''; ?>">
                            <i class="fas fa-file-alt w-5 text-xs"></i>
                            <span class="sidebar-label">Grade Curricular</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Módulo Financeiro -->
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Financeiro</p>
                <a href="../financeiro/index.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md">
                    <i class="fas fa-dollar-sign w-6"></i>
                    <span class="sidebar-label">Módulo Financeiro</span>
                </a>
            </div>

            <!-- Ambiente Virtual de Aprendizagem
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Ambiente Virtual</p>
                <a href="ava/ava_gerenciar_acesso.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'ava_gerenciar_acesso.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-key w-6"></i>
                    <span class="sidebar-label">Gerenciar Acesso</span>
                </a>
                <a href="ava/ava_cursos.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'ava_cursos.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-laptop w-6"></i>
                    <span class="sidebar-label">Cursos do AVA</span>
                </a>
                <a href="ava/ava_alunos.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'ava_alunos.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-user-friends w-6"></i>
                    <span class="sidebar-label">Alunos do AVA</span>
                </a>
                <a href="ava/ava_relatorios.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'ava_relatorios.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-chart-bar w-6"></i>
                    <span class="sidebar-label">Relatórios do AVA</span>
                </a>
                <a href="ava/ava_dashboard_aluno.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) == 'ava_dashboard_aluno.php' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-desktop w-6"></i>
                    <span class="sidebar-label">Ambiente do Aluno</span>
                </a>
            </div> -->



            <!-- Sistema de Chamados -->
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Suporte</p>
                <a href="chamados/index.php" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo strpos($_SERVER['PHP_SELF'], 'chamados/') !== false ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-headset w-6"></i>
                    <span class="sidebar-label">Gerenciar Chamados</span>
                </a>
            </div>

            <!-- Relatórios e Análises -->
            <div class="mb-4">
                <p class="text-xs text-purple-100 uppercase font-semibold mb-2">Relatórios</p>
                <?php if (file_exists('relatorios.php')): ?>
                <a href="core/relatorios.php?tipo=desempenho" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) === 'relatorios.php' && isset($_GET['tipo']) && $_GET['tipo'] === 'desempenho' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-chart-line w-6"></i>
                    <span class="sidebar-label">Desempenho</span>
                </a>
                <a href="core/relatorios.php?tipo=estatisticas" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) === 'relatorios.php' && isset($_GET['tipo']) && $_GET['tipo'] === 'estatisticas' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-chart-pie w-6"></i>
                    <span class="sidebar-label">Estatísticas</span>
                </a>
                <a href="core/relatorios.php?tipo=documentos" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) === 'relatorios.php' && isset($_GET['tipo']) && $_GET['tipo'] === 'documentos' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-file-chart-line w-6"></i>
                    <span class="sidebar-label">Documentos</span>
                </a>
                <a href="core/relatorios.php?tipo=chamados" class="sidebar-item flex items-center py-3 px-4 text-white hover:bg-purple-500 rounded-md <?php echo basename($_SERVER['PHP_SELF']) === 'relatorios.php' && isset($_GET['tipo']) && $_GET['tipo'] === 'chamados' ? 'active bg-purple-500' : ''; ?>">
                    <i class="fas fa-ticket-alt w-6"></i>
                    <span class="sidebar-label">Chamados</span>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Overlay para mobile -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-5 hidden lg:hidden"></div>

<script>
// Função para toggle do submenu
function toggleSubmenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const icon = document.getElementById(submenuId + '-icon');

    if (submenu.classList.contains('hidden')) {
        submenu.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
    } else {
        submenu.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}

// Auto-expandir submenu se página atual estiver no submenu
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = '<?php echo basename($_SERVER['PHP_SELF']); ?>';
    const disciplinasPages = ['disciplinas.php', 'turma_disciplinas.php', 'declaracao_grade_curricular.php'];

    if (disciplinasPages.includes(currentPage)) {
        const submenu = document.getElementById('disciplinas-submenu');
        const icon = document.getElementById('disciplinas-submenu-icon');
        if (submenu && submenu.classList.contains('hidden')) {
            submenu.classList.remove('hidden');
            if (icon) icon.style.transform = 'rotate(180deg)';
        }
    }
});
</script>
