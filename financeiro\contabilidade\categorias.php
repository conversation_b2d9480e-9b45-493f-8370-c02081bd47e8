<?php
/**
 * Gestão de Categorias Financeiras
 * Sistema de ERP - Módulo Financeiro
 */

require_once '../includes/init.php';
require_once '../includes/Database.php';
require_once '../includes/Auth.php';

// Verificar autenticação
Auth::checkAuth();
Auth::checkPermission('financeiro_categorias');

$db = Database::getInstance();
$mensagem = '';
$tipo_mensagem = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'criar':
            try {
                $stmt = $db->prepare("
                    INSERT INTO categorias_financeiras (nome, tipo, descricao, ativo, created_at, updated_at) 
                    VALUES (?, ?, ?, 1, NOW(), NOW())
                ");
                
                $stmt->execute([
                    $_POST['nome'],
                    $_POST['tipo'],
                    $_POST['descricao'] ?? ''
                ]);
                
                $mensagem = "Categoria criada com sucesso!";
                $tipo_mensagem = "success";
                
            } catch (Exception $e) {
                $mensagem = "Erro ao criar categoria: " . $e->getMessage();
                $tipo_mensagem = "error";
            }
            break;
            
        case 'editar':
            try {
                $stmt = $db->prepare("
                    UPDATE categorias_financeiras 
                    SET nome = ?, tipo = ?, descricao = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $_POST['nome'],
                    $_POST['tipo'],
                    $_POST['descricao'] ?? '',
                    $_POST['id']
                ]);
                
                $mensagem = "Categoria atualizada com sucesso!";
                $tipo_mensagem = "success";
                
            } catch (Exception $e) {
                $mensagem = "Erro ao atualizar categoria: " . $e->getMessage();
                $tipo_mensagem = "error";
            }
            break;
    }
}

// Processar exclusão via GET
if (isset($_GET['action']) && $_GET['action'] === 'excluir' && isset($_GET['id'])) {
    try {
        // Verificar se a categoria está sendo usada
        $uso = $db->fetchOne("
            SELECT COUNT(*) as total FROM (
                SELECT categoria_id FROM contas_pagar WHERE categoria_id = ?
                UNION ALL
                SELECT categoria_id FROM contas_receber WHERE categoria_id = ?
                UNION ALL
                SELECT categoria_id FROM transacoes_financeiras WHERE categoria_id = ?
            ) AS uso_total
        ", [$_GET['id'], $_GET['id'], $_GET['id']]);
        
        if ($uso['total'] > 0) {
            $mensagem = "Não é possível excluir esta categoria pois ela está sendo utilizada em {$uso['total']} registro(s).";
            $tipo_mensagem = "error";
        } else {
            $stmt = $db->prepare("DELETE FROM categorias_financeiras WHERE id = ?");
            $stmt->execute([$_GET['id']]);
            
            $mensagem = "Categoria excluída com sucesso!";
            $tipo_mensagem = "success";
        }
        
    } catch (Exception $e) {
        $mensagem = "Erro ao excluir categoria: " . $e->getMessage();
        $tipo_mensagem = "error";
    }
}

// Buscar categorias
$filtro_tipo = $_GET['tipo'] ?? '';
$busca = $_GET['busca'] ?? '';

$where = "WHERE 1=1";
$params = [];

if ($filtro_tipo) {
    $where .= " AND tipo = ?";
    $params[] = $filtro_tipo;
}

if ($busca) {
    $where .= " AND (nome LIKE ? OR descricao LIKE ?)";
    $params[] = "%$busca%";
    $params[] = "%$busca%";
}

$categorias = $db->fetchAll("
    SELECT c.*, 
           (SELECT COUNT(*) FROM contas_pagar WHERE categoria_id = c.id) +
           (SELECT COUNT(*) FROM contas_receber WHERE categoria_id = c.id) +
           (SELECT COUNT(*) FROM transacoes_financeiras WHERE categoria_id = c.id) as uso_total
    FROM categorias_financeiras c 
    $where 
    ORDER BY tipo, nome
", $params);

// Buscar estatísticas
$stats = $db->fetchOne("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN tipo = 'receita' THEN 1 ELSE 0 END) as receitas,
        SUM(CASE WHEN tipo = 'despesa' THEN 1 ELSE 0 END) as despesas,
        SUM(CASE WHEN ativo = 1 THEN 1 ELSE 0 END) as ativas
    FROM categorias_financeiras
");
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categorias Financeiras - Sistema ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/financeiro.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <?php include 'includes/sidebar.php'; ?>
            </div>
            
            <!-- Conteúdo Principal -->
            <div class="col-md-10">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-tags"></i> Categorias Financeiras</h1>
                            <p class="text-muted">Gerencie as categorias de receitas e despesas</p>
                        </div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalCategoria">
                            <i class="fas fa-plus"></i> Nova Categoria
                        </button>
                    </div>
                </div>

                <!-- Alertas -->
                <?php if ($mensagem): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($mensagem); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Estatísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($stats['total']); ?></h4>
                                <p class="card-text">Total de Categorias</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-up fa-2x text-success mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($stats['receitas']); ?></h4>
                                <p class="card-text">Receitas</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-down fa-2x text-danger mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($stats['despesas']); ?></h4>
                                <p class="card-text">Despesas</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($stats['ativas']); ?></h4>
                                <p class="card-text">Ativas</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Tipo</label>
                                <select name="tipo" class="form-select">
                                    <option value="">Todos os tipos</option>
                                    <option value="receita" <?php echo $filtro_tipo === 'receita' ? 'selected' : ''; ?>>Receita</option>
                                    <option value="despesa" <?php echo $filtro_tipo === 'despesa' ? 'selected' : ''; ?>>Despesa</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Buscar</label>
                                <input type="text" name="busca" class="form-control" placeholder="Nome ou descrição..." value="<?php echo htmlspecialchars($busca); ?>">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Buscar
                                </button>
                                <a href="categorias.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Lista de Categorias -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Lista de Categorias</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($categorias)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5>Nenhuma categoria encontrada</h5>
                                <p class="text-muted">Crie a primeira categoria financeira do sistema.</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalCategoria">
                                    <i class="fas fa-plus"></i> Criar Primeira Categoria
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Nome</th>
                                            <th>Tipo</th>
                                            <th>Descrição</th>
                                            <th>Status</th>
                                            <th>Uso</th>
                                            <th>Criado em</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($categorias as $categoria): ?>
                                            <tr>
                                                <td><?php echo $categoria['id']; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($categoria['nome']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($categoria['tipo'] === 'receita'): ?>
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-arrow-up"></i> Receita
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-arrow-down"></i> Despesa
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($categoria['descricao']); ?></td>
                                                <td>
                                                    <?php if ($categoria['ativo']): ?>
                                                        <span class="badge bg-success">Ativa</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inativa</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($categoria['uso_total'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $categoria['uso_total']; ?> registros</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Não usada</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($categoria['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-warning" 
                                                                onclick="editarCategoria(<?php echo htmlspecialchars(json_encode($categoria)); ?>)"
                                                                data-bs-toggle="modal" data-bs-target="#modalCategoria">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($categoria['uso_total'] == 0): ?>
                                                            <a href="?action=excluir&id=<?php echo $categoria['id']; ?>" 
                                                               class="btn btn-danger"
                                                               onclick="return confirm('Tem certeza que deseja excluir esta categoria?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-secondary" disabled title="Categoria em uso">
                                                                <i class="fas fa-ban"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Categoria -->
    <div class="modal fade" id="modalCategoria" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-tag"></i> <span id="modal-title">Nova Categoria</span>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" id="form-action" value="criar">
                        <input type="hidden" name="id" id="categoria-id">
                        
                        <div class="mb-3">
                            <label class="form-label">Nome da Categoria *</label>
                            <input type="text" name="nome" id="categoria-nome" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Tipo *</label>
                            <select name="tipo" id="categoria-tipo" class="form-select" required>
                                <option value="">Selecione o tipo</option>
                                <option value="receita">Receita</option>
                                <option value="despesa">Despesa</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Descrição</label>
                            <textarea name="descricao" id="categoria-descricao" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> <span id="btn-text">Salvar</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarCategoria(categoria) {
            document.getElementById('modal-title').textContent = 'Editar Categoria';
            document.getElementById('form-action').value = 'editar';
            document.getElementById('btn-text').textContent = 'Atualizar';
            
            document.getElementById('categoria-id').value = categoria.id;
            document.getElementById('categoria-nome').value = categoria.nome;
            document.getElementById('categoria-tipo').value = categoria.tipo;
            document.getElementById('categoria-descricao').value = categoria.descricao || '';
        }
        
        // Reset form when modal is hidden
        document.getElementById('modalCategoria').addEventListener('hidden.bs.modal', function () {
            document.getElementById('modal-title').textContent = 'Nova Categoria';
            document.getElementById('form-action').value = 'criar';
            document.getElementById('btn-text').textContent = 'Salvar';
            
            document.getElementById('categoria-id').value = '';
            document.getElementById('categoria-nome').value = '';
            document.getElementById('categoria-tipo').value = '';
            document.getElementById('categoria-descricao').value = '';
        });
    </script>
</body>
</html>
